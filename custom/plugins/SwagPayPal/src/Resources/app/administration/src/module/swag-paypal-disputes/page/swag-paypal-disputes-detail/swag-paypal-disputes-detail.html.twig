{% block swag_paypal_disputes_detail %}
    <sw-page class="swag-paypal-disputes-detail">

        {% block swag_paypal_disputes_detail_smart_bar_header %}
            <template #smart-bar-header>

                {% block swag_paypal_disputes_detail_smart_bar_header_title %}
                    <h2>

                        {% block swag_paypal_disputes_detail_smart_bar_header_title %}
                            {{ $tc('swag-paypal-disputes.detail.title') }}
                        {% endblock %}

                        {% block swag_paypal_disputes_detail_smart_bar_header_dispute_id %}
                            {{ disputeId }}
                        {% endblock %}

                        {% block swag_paypal_disputes_detail_smart_bar_header_title_addition %}
                            {{ $tc('swag-paypal-disputes.detail.titleAddition') }}
                        {% endblock %}

                    </h2>
                {% endblock %}

            </template>
        {% endblock %}

        {% block swag_paypal_disputes_detail_content %}
            <template #content>
                <sw-card-view>

                    {% block swag_paypal_disputes_detail_content_details %}
                        <sw-card position-identifier="swag-paypal-disputes-detail-content-details"
                                 :isLoading="isLoading"
                                 :title="$tc('swag-paypal-disputes.detail.mainFieldsCardTitle')">

                            {% block swag_paypal_disputes_detail_content_details_fields %}
                                <template v-if="dispute">

                                    {% block swag_paypal_disputes_detail_content_details_fields_external_link %}
                                        <div class="swag-paypal-disputes-detail__links">
                                            <sw-external-link :href="externalDetailPageLink">
                                                {{ $tc('swag-paypal-disputes.detail.externalLinkText') }}
                                            </sw-external-link>
                                        </div>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_order_module_link %}
                                        <div class="swag-paypal-disputes-detail__links">
                                            <router-link v-if="orderModuleLink"
                                                         :to="orderModuleLink"
                                                         target="_blank"
                                                         rel="noopener">
                                                {{ $tc('swag-paypal-disputes.detail.orderModuleLinkText') }}
                                            </router-link>
                                        </div>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_separator %}
                                        <hr class="swag-paypal-disputes-detail__separator"/>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_id %}
                                        <sw-text-field :value="dispute.dispute_id"
                                                       :label="$tc('swag-paypal-disputes.common.dispute_id')"
                                                       disabled>
                                        </sw-text-field>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_update_time %}
                                        <sw-text-field :value="formatDate(dispute.update_time)"
                                                       :label="$tc('swag-paypal-disputes.common.update_time')"
                                                       disabled>
                                        </sw-text-field>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_due_date %}
                                        <sw-text-field v-if="dispute.seller_response_due_date || dispute.buyer_response_due_date"
                                                       :value="getDueDate(dispute.seller_response_due_date, dispute.buyer_response_due_date)"
                                                       :label="$tc('swag-paypal-disputes.common.response_due_date.label')"
                                                       disabled>
                                        </sw-text-field>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_status %}
                                        <sw-text-field :value="`${formatTechnicalText(dispute.status)} (${formatTechnicalText(dispute.reason)})`"
                                                       :label="$tc('swag-paypal-disputes.common.status')"
                                                       disabled>
                                        </sw-text-field>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_dispute_life_cycle_stage %}
                                        <sw-text-field :value="formatTechnicalText(dispute.dispute_life_cycle_stage)"
                                                       :label="$tc('swag-paypal-disputes.common.dispute_life_cycle_stage')"
                                                       :class="getInquiryClass(dispute.dispute_life_cycle_stage)"
                                                       disabled>
                                        </sw-text-field>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_details_fields_dispute_amount %}
                                        <sw-text-field :value="`${dispute.dispute_amount.value} ${dispute.dispute_amount.currency_code}`"
                                                       :label="$tc('swag-paypal-disputes.common.dispute_amount')"
                                                       disabled>
                                        </sw-text-field>
                                    {% endblock %}

                                </template>
                            {% endblock %}

                        </sw-card>
                    {% endblock %}

                    {% block swag_paypal_disputes_detail_content_raw_data %}
                        <sw-card position-identifier="swag-paypal-disputes-detail-content-raw-data"
                                 :isLoading="isLoading"
                                 :title="$tc('swag-paypal-disputes.detail.rawDataCardTitle')">

                            {% block swag_paypal_disputes_detail_content_raw_data_content %}
                                <template v-if="dispute">

                                    {% block swag_paypal_disputes_detail_content_raw_data_text %}
                                        <sw-textarea-field :value="JSON.stringify(dispute, null, 2)"
                                                           disabled>
                                        </sw-textarea-field>
                                    {% endblock %}

                                    {% block swag_paypal_disputes_detail_content_raw_data_copy_button %}
                                        <sw-button @click="copyToClipboard">
                                            {{ $tc('swag-paypal-disputes.detail.copyButtonLabel') }}
                                        </sw-button>
                                    {% endblock %}

                                </template>
                            {% endblock %}

                        </sw-card>
                    {% endblock %}

                </sw-card-view>
            </template>
        {% endblock %}

    </sw-page>
{% endblock %}
