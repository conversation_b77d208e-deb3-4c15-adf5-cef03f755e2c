{% block sw_first_run_wizard_paypal_credentials %}
    <div class="sw-first-run-wizard-paypal-credentials">

        {% block sw_first_run_wizard_paypal_credentials_inner %}
            <sw-loader v-if="isLoading"></sw-loader>

            {% block sw_first_run_wizard_paypal_credentials_intro %}
                <p class="sw-first-run-wizard-paypal-credentials__headerText">
                    {{ $tc('swag-paypal-frw-credentials.textIntroPayPal') }}
                </p>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_sandbox %}
                <sw-switch-field v-model="config['SwagPayPal.settings.sandbox']"
                                 :label="$tc('swag-paypal-frw-credentials.labelSandbox')"
                                 :helpText="$tc('swag-paypal-frw-credentials.tooltipSandbox')">
                </sw-switch-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_button_container %}
                <div class="sw-first-run-wizard-paypal-credentials__button-container">

                    {% block sw_first_run_wizard_paypal_credentials_button %}
                        <a class="sw-button sw-button--primary swag-paypal-frw__signup-button"
                           target="_blank"
                           :data-paypal-onboard-complete="onboardingCallback"
                           :href="`${onboardingUrl}`"
                           data-paypal-button="true">
                            {{ $tc('swag-paypal-frw-credentials.buttonGetCredentials') }}
                        </a>
                    {% endblock %}

                    {% block sw_first_run_wizard_paypal_credentials_indicator %}
                        <div class="sw-first-run-wizard-paypal-credentials__indicator">
                            <template v-if="isGetCredentialsSuccessful">

                                {% block sw_first_run_wizard_paypal_credentials_indicator_icon %}
                                    <sw-icon name="regular-checkmark-s"
                                             class="sw-first-run-wizard-paypal-credentials__icon-successful">
                                    </sw-icon>
                                {% endblock %}

                                {% block sw_first_run_wizard_paypal_credentials_indicator_text %}
                                    <span class="sw-first-run-wizard-paypal-credentials__text-indicator">
                                        {{ $tc('swag-paypal-frw-credentials.textFetchedSuccessful') }}
                                    </span>
                                {% endblock %}
                            </template>
                        </div>
                    {% endblock %}
                </div>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_client_id %}
                <sw-text-field v-model="config['SwagPayPal.settings.clientId']"
                               v-show="!sandboxMode"
                               :label="$tc('swag-paypal-frw-credentials.labelClientId')"
                               @change="onCredentialsChanged">
                </sw-text-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_client_secret %}
                <sw-text-field v-model="config['SwagPayPal.settings.clientSecret']"
                               v-show="!sandboxMode"
                               :label="$tc('swag-paypal-frw-credentials.labelClientSecret')"
                               @change="onCredentialsChanged">
                </sw-text-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_merchant_id %}
                <sw-text-field v-model="config['SwagPayPal.settings.merchantPayerId']"
                               v-show="!sandboxMode"
                               :label="$tc('swag-paypal-frw-credentials.labelMerchantPayerId')"
                               @change="onCredentialsChanged">
                </sw-text-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_client_id_sandbox %}
                <sw-text-field v-model="config['SwagPayPal.settings.clientIdSandbox']"
                               v-show="sandboxMode"
                               :label="$tc('swag-paypal-frw-credentials.labelClientIdSandbox')"
                               @change="onCredentialsChanged">
                </sw-text-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_client_secret_sandbox %}
                <sw-text-field v-model="config['SwagPayPal.settings.clientSecretSandbox']"
                               v-show="sandboxMode"
                               :label="$tc('swag-paypal-frw-credentials.labelClientSecretSandbox')"
                               @change="onCredentialsChanged">
                </sw-text-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_merchant_id_sandbox %}
                <sw-text-field v-model="config['SwagPayPal.settings.merchantPayerIdSandbox']"
                               v-show="sandboxMode"
                               :label="$tc('swag-paypal-frw-credentials.labelMerchantPayerIdSandbox')"
                               @change="onCredentialsChanged">
                </sw-text-field>
            {% endblock %}

            {% block sw_first_run_wizard_paypal_credentials_set_default %}
                <sw-switch-field v-model="setDefault"
                                 :disabled="!credentialsProvided"
                                 :label="$tc('swag-paypal-frw-credentials.labelSetDefault')"
                                 :helpText="$tc('swag-paypal-frw-credentials.tooltipSetDefault')">
                </sw-switch-field>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}
