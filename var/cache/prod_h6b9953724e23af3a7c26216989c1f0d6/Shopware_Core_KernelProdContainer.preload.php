<?php

// This file has been auto-generated by the Symfony Dependency Injection Component
// You can reference it in the "opcache.preload" php.ini setting on PHP >= 7.4 when preloading is desired

use Symfony\Component\DependencyInjection\Dumper\Preloader;

if (in_array(PHP_SAPI, ['cli', 'phpdbg'], true)) {
    return;
}

require dirname(__DIR__, 3).'/vendor/autoload.php';
(require __DIR__.'/Shopware_Core_KernelProdContainer.php')->set(\ContainerJt96own\Shopware_Core_KernelProdContainer::class, null);

$classes = [];
$classes[] = 'Symfony\Bundle\FrameworkBundle\FrameworkBundle';
$classes[] = 'Symfony\Bundle\MonologBundle\MonologBundle';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\SensioFrameworkExtraBundle';
$classes[] = 'Symfony\Bundle\TwigBundle\TwigBundle';
$classes[] = 'Enqueue\Bundle\EnqueueBundle';
$classes[] = 'Enqueue\MessengerAdapter\Bundle\EnqueueAdapterBundle';
$classes[] = 'Shopware\Core\Framework\Framework';
$classes[] = 'Shopware\Core\System\System';
$classes[] = 'Shopware\Core\Content\Content';
$classes[] = 'Shopware\Core\Checkout\Checkout';
$classes[] = 'Shopware\Administration\Administration';
$classes[] = 'Shopware\Storefront\Storefront';
$classes[] = 'Shopware\Elasticsearch\Elasticsearch';
$classes[] = 'Shopware\Core\Maintenance\Maintenance';
$classes[] = 'Shopware\Core\Framework\Plugin\KernelPluginLoader\DbalKernelPluginLoader';
$classes[] = 'Symfony\Component\Cache\Adapter\FilesystemAdapter';
$classes[] = 'Symfony\Component\HttpKernel\CacheClearer\ChainCacheClearer';
$classes[] = 'Symfony\Component\Filesystem\Filesystem';
$classes[] = 'Symfony\Component\Security\Csrf\CsrfTokenManager';
$classes[] = 'Symfony\Component\Security\Csrf\TokenGenerator\UriSafeTokenGenerator';
$classes[] = 'Symfony\Component\Security\Csrf\TokenStorage\SessionTokenStorage';
$classes[] = 'Symfony\Component\Serializer\Serializer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UnwrappingDenormalizer';
$classes[] = 'Symfony\Component\PropertyAccess\PropertyAccessor';
$classes[] = 'Shopware\Core\Framework\Struct\Serializer\StructNormalizer';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\Normalizer\FlattenExceptionNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ProblemNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UidNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ConstraintViolationListNormalizer';
$classes[] = 'Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter';
$classes[] = 'Symfony\Component\Serializer\Normalizer\MimeMessageNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\PropertyNormalizer';
$classes[] = 'Symfony\Component\Serializer\Mapping\ClassDiscriminatorFromClassMetadata';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeZoneNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateIntervalNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\FormErrorNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DataUriNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\JsonSerializableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ArrayDenormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ObjectNormalizer';
$classes[] = 'Shopware\Core\Framework\Struct\Serializer\StructDecoder';
$classes[] = 'Shopware\Core\Framework\Api\Serializer\JsonApiDecoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\XmlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\JsonEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\YamlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\CsvEncoder';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Session';
$classes[] = 'Twig\Cache\FilesystemCache';
$classes[] = 'Twig\Extension\CoreExtension';
$classes[] = 'Twig\Extension\EscaperExtension';
$classes[] = 'Twig\Extension\OptimizerExtension';
$classes[] = 'Twig\Extension\StagingExtension';
$classes[] = 'Twig\ExtensionSet';
$classes[] = 'Twig\Template';
$classes[] = 'Twig\TemplateWrapper';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\TwigEnvironment';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\AssetExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\CodeExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\RoutingExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\YamlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\StopwatchExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpFoundationExtension';
$classes[] = 'Symfony\Component\HttpFoundation\UrlHelper';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\NodeExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\PhpSyntaxExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\FeatureFlagExtension';
$classes[] = 'Twig\Extra\Intl\IntlExtension';
$classes[] = 'Twig\Extra\String\StringExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\InstanceOfExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Filter\CurrencyFilter';
$classes[] = 'Cocur\Slugify\Bridge\Twig\SlugifyExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Filter\ReplaceRecursiveFilter';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\SecurityExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\SeoUrlFunctionExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\CategoryUrlExtension';
$classes[] = 'Shopware\Core\Content\Category\Service\CategoryUrlGenerator';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\MediaExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\RawUrlFunctionExtension';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\SwSanitizeTwigFilter';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\Extension\BuildBreadcrumbExtension';
$classes[] = 'Shopware\Storefront\Framework\Twig\TemplateDataExtension';
$classes[] = 'Shopware\Storefront\Framework\Twig\Extension\ConfigExtension';
$classes[] = 'Shopware\Storefront\Framework\Twig\TemplateConfigAccessor';
$classes[] = 'Shopware\Storefront\Framework\Twig\IconExtension';
$classes[] = 'Shopware\Storefront\Framework\Twig\ThumbnailExtension';
$classes[] = 'Shopware\Storefront\Framework\Twig\Extension\UrlEncodingTwigFilter';
$classes[] = 'Shopware\Storefront\Framework\Twig\Extension\IconCacheTwigFilter';
$classes[] = 'Shopware\Storefront\Framework\Twig\Extension\CsrfFunctionExtension';
$classes[] = 'Shopware\Storefront\Framework\Twig\TwigAppVariable';
$classes[] = 'Symfony\Bridge\Twig\AppVariable';
$classes[] = 'Twig\RuntimeLoader\ContainerRuntimeLoader';
$classes[] = 'Symfony\Component\DependencyInjection\ServiceLocator';
$classes[] = 'Symfony\Bundle\TwigBundle\DependencyInjection\Configurator\EnvironmentConfigurator';
$classes[] = 'Shopware\Core\Framework\Validation\HappyPathValidator';
$classes[] = 'Symfony\Component\Validator\Validator\ValidatorInterface';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlerDescriptor';
$classes[] = 'Symfony\Component\HttpFoundation\Session\SessionInterface';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Session\DeprecatedSessionFactory';
$classes[] = 'Doctrine\DBAL\Connection';
$classes[] = 'Shopware\Core\Kernel';
$classes[] = 'Elasticsearch\Client';
$classes[] = 'Shopware\Elasticsearch\Framework\ClientFactory';
$classes[] = 'Nyholm\Psr7\Factory\Psr17Factory';
$classes[] = 'Shopware\Administration\Controller\AdminExtensionApiController';
$classes[] = 'Shopware\Administration\Controller\AdminProductStreamController';
$classes[] = 'Shopware\Administration\Controller\AdminSearchController';
$classes[] = 'Shopware\Administration\Service\AdminSearcher';
$classes[] = 'Shopware\Administration\Controller\AdminTagController';
$classes[] = 'Shopware\Administration\Controller\AdministrationController';
$classes[] = 'Shopware\Administration\Snippet\CachedSnippetFinder';
$classes[] = 'Shopware\Administration\Snippet\SnippetFinder';
$classes[] = 'Shopware\Administration\Framework\Routing\KnownIps\KnownIpsCollector';
$classes[] = 'Shopware\Administration\Controller\DocumentServiceDeprecationController';
$classes[] = 'Shopware\Administration\Controller\NotificationController';
$classes[] = 'Shopware\Administration\Controller\UserConfigController';
$classes[] = 'Shopware\Administration\Framework\Routing\AdministrationRouteScope';
$classes[] = 'Shopware\Administration\Notification\Extension\IntegrationExtension';
$classes[] = 'Shopware\Administration\Notification\Extension\UserExtension';
$classes[] = 'Shopware\Administration\Notification\NotificationDefinition';
$classes[] = 'Shopware\Administration\Notification\NotificationService';
$classes[] = 'Shopware\Administration\Snippet\AppAdministrationSnippetDefinition';
$classes[] = 'Shopware\Administration\System\SalesChannel\Subscriber\SalesChannelUserConfigSubscriber';
$classes[] = 'Shopware\Core\Checkout\Cart\Address\AddressValidator';
$classes[] = 'Shopware\Core\Checkout\Cart\CachedRuleLoader';
$classes[] = 'Shopware\Core\Checkout\Cart\RuleLoader';
$classes[] = 'Shopware\Core\Checkout\Cart\Calculator';
$classes[] = 'Shopware\Core\Checkout\Cart\CartCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\CartPersister';
$classes[] = 'Shopware\Core\Checkout\Cart\CartSerializationCleaner';
$classes[] = 'Shopware\Core\Checkout\Cart\CartRuleLoader';
$classes[] = 'Shopware\Core\Checkout\Cart\CartValueResolver';
$classes[] = 'Shopware\Core\Checkout\Cart\Cleanup\CleanupCartTask';
$classes[] = 'Shopware\Core\Checkout\Cart\Cleanup\CleanupCartTaskHandler';
$classes[] = 'Shopware\Core\Checkout\Cart\CreditCartProcessor';
$classes[] = 'Shopware\Core\Checkout\Cart\CustomCartProcessor';
$classes[] = 'Shopware\Core\Checkout\Cart\Delivery\DeliveryBuilder';
$classes[] = 'Shopware\Core\Checkout\Cart\Delivery\DeliveryProcessor';
$classes[] = 'Shopware\Core\Checkout\Cart\Delivery\DeliveryCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Delivery\DeliveryValidator';
$classes[] = 'Shopware\Core\Checkout\Cart\Facade\CartFacadeHelper';
$classes[] = 'Shopware\Core\Checkout\Cart\Facade\CartFacadeHookFactory';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItemFactoryHandler\CreditLineItemFactory';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItemFactoryHandler\CustomLineItemFactory';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItemFactoryHandler\ProductLineItemFactory';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItemFactoryHandler\PromotionLineItemFactory';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItemFactoryRegistry';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\LineItemGroupBuilder';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\RulesMatcher\AnyRuleMatcher';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\RulesMatcher\AnyRuleLineItemMatcher';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\ProductLineItemProvider';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\LineItemGroupServiceRegistry';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\Packager\LineItemGroupCountPackager';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\Packager\LineItemGroupUnitPriceGrossPackager';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\Packager\LineItemGroupUnitPriceNetPackager';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\Sorter\LineItemGroupPriceAscSorter';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\Group\Sorter\LineItemGroupPriceDescSorter';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\LineItemQuantitySplitter';
$classes[] = 'Shopware\Core\Checkout\Cart\LineItem\LineItemValidator';
$classes[] = 'Shopware\Core\Checkout\Cart\Order\Api\OrderConverterController';
$classes[] = 'Shopware\Core\Checkout\Cart\Order\Api\OrderRecalculationController';
$classes[] = 'Shopware\Core\Checkout\Cart\Order\RecalculationService';
$classes[] = 'Shopware\Core\Checkout\Cart\Order\OrderConverter';
$classes[] = 'Shopware\Core\Checkout\Cart\Order\LineItemDownloadLoader';
$classes[] = 'Shopware\Core\Checkout\Cart\PriceActionController';
$classes[] = 'Shopware\Core\Checkout\Cart\PriceDefinitionFactory';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\AbsolutePriceCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\AmountCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\CashRounding';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\CurrencyPriceCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\GrossPriceCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\NetPriceCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\PercentagePriceCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Price\QuantityPriceCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Processor';
$classes[] = 'Shopware\Core\Checkout\Cart\Validator';
$classes[] = 'Shopware\Core\Checkout\Cart\Transaction\TransactionProcessor';
$classes[] = 'Shopware\Core\Checkout\Cart\Processor\ContainerCartProcessor';
$classes[] = 'Shopware\Core\Checkout\Cart\Processor\DiscountCartProcessor';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\AlwaysValidRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\CartAmountRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\CartHasDeliveryFreeItemRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\CartPositionPriceRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\CartTaxDisplayRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\CartVolumeRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\CartWeightRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\GoodsCountRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\GoodsPriceRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemActualStockRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemClearanceSaleRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemCreationDateRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemCustomFieldRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemDimensionHeightRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemDimensionLengthRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemDimensionVolumeRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemDimensionWeightRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemDimensionWidthRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemGoodsTotalRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemInCategoryRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemInProductStreamRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemIsNewRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemListPriceRatioRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemListPriceRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemOfManufacturerRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemOfTypeRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemProductStatesRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemPromotedRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemPropertyRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemPurchasePriceRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemReleaseDateRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemStockRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemTagRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemTaxationRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemTotalPriceRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemUnitPriceRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemWithQuantityRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemWrapperRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemsInCartCountRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\LineItemsInCartRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\PaymentMethodRule';
$classes[] = 'Shopware\Core\Checkout\Cart\Rule\ShippingMethodRule';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartDeleteRoute';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartItemAddRoute';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartItemRemoveRoute';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartItemUpdateRoute';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartLoadRoute';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartOrderRoute';
$classes[] = 'Shopware\Core\Checkout\Cart\Order\OrderPersister';
$classes[] = 'Shopware\Core\Checkout\Payment\PreparedPaymentService';
$classes[] = 'Shopware\Core\Checkout\Cart\SalesChannel\CartService';
$classes[] = 'Shopware\Core\Checkout\Cart\Tax\PercentageTaxRuleBuilder';
$classes[] = 'Shopware\Core\Checkout\Cart\Tax\TaxCalculator';
$classes[] = 'Shopware\Core\Checkout\Cart\Tax\TaxDetector';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerAddress\CustomerAddressDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerGroupRegistrationSalesChannel\CustomerGroupRegistrationSalesChannelDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerGroupTranslation\CustomerGroupTranslationDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerGroup\CustomerGroupDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerRecovery\CustomerRecoveryDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerTag\CustomerTagDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerWishlistProduct\CustomerWishlistProductDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Aggregate\CustomerWishlist\CustomerWishlistDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\Api\CustomerGroupRegistrationActionController';
$classes[] = 'Shopware\Core\Checkout\Customer\CustomerDefinition';
$classes[] = 'Shopware\Core\Checkout\Customer\CustomerValueResolver';
$classes[] = 'Shopware\Core\Checkout\Customer\DataAbstractionLayer\CustomerIndexer';
$classes[] = 'Shopware\Core\Checkout\Customer\DataAbstractionLayer\CustomerWishlistProductExceptionHandler';
$classes[] = 'Shopware\Core\Checkout\Customer\Password\LegacyEncoder\Md5';
$classes[] = 'Shopware\Core\Checkout\Customer\Password\LegacyEncoder\Sha256';
$classes[] = 'Shopware\Core\Checkout\Customer\Password\LegacyPasswordVerifier';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\AffiliateCodeRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\BillingCityRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\BillingCountryRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\BillingStateRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\BillingStreetRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\BillingZipCodeRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CampaignCodeRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerAgeRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerBirthdayRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerCustomFieldRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerGroupRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerLoggedInRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerNumberRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\CustomerTagRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\DaysSinceLastLoginRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\DaysSinceLastOrderRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\DifferentAddressesRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\EmailRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\IsActiveRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\IsCompanyRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\IsGuestCustomerRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\IsNewCustomerRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\IsNewsletterRecipientRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\LastNameRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\OrderCountRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\OrderTotalAmountRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\ShippingCityRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\ShippingCountryRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\ShippingStateRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\ShippingStreetRule';
$classes[] = 'Shopware\Core\Checkout\Customer\Rule\ShippingZipCodeRule';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\AccountNewsletterRecipientRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\AccountService';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\AddWishlistProductRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ChangeCustomerProfileRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ChangeEmailRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ChangeLanguageRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ChangePasswordRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ChangePaymentMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\CustomerGroupRegistrationSettingsRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\CustomerRecoveryIsExpiredRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\CustomerRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\DeleteAddressRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\DeleteCustomerRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\DownloadRoute';
$classes[] = 'Shopware\Core\Content\Media\File\DownloadResponseGenerator';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ListAddressRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\LoadWishlistRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\LoginRoute';
$classes[] = 'Shopware\Core\Framework\RateLimiter\RateLimiter';
$classes[] = 'Shopware\Core\Framework\RateLimiter\RateLimiterFactory';
$classes[] = 'Symfony\Component\RateLimiter\Storage\CacheStorage';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\LogoutRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\MergeWishlistProductRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\RegisterConfirmRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\RegisterRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\CustomerValidationFactory';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\RemoveWishlistProductRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\ResetPasswordRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\SendPasswordRecoveryMailRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\SwitchDefaultAddressRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\SalesChannel\UpsertAddressRoute';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerBeforeDeleteSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerChangePasswordSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerDefaultSalutationSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerFlowEventsSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerGroupSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerMetaFieldSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerRemoteAddressSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Subscriber\CustomerTokenSubscriber';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\AddressValidationFactory';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\Constraint\CustomerEmailUniqueValidator';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\Constraint\CustomerPasswordMatchesValidator';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\Constraint\CustomerVatIdentificationValidator';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\Constraint\CustomerZipCodeValidator';
$classes[] = 'Shopware\Core\Checkout\Customer\Validation\CustomerProfileValidationFactory';
$classes[] = 'Shopware\Core\Checkout\Document\Aggregate\DocumentBaseConfigSalesChannel\DocumentBaseConfigSalesChannelDefinition';
$classes[] = 'Shopware\Core\Checkout\Document\Aggregate\DocumentBaseConfig\DocumentBaseConfigDefinition';
$classes[] = 'Shopware\Core\Checkout\Document\Aggregate\DocumentTypeTranslation\DocumentTypeTranslationDefinition';
$classes[] = 'Shopware\Core\Checkout\Document\Aggregate\DocumentType\DocumentTypeDefinition';
$classes[] = 'Shopware\Core\Checkout\Document\Controller\DocumentController';
$classes[] = 'Shopware\Core\Checkout\Document\Service\DocumentMerger';
$classes[] = 'setasign\Fpdi\Tcpdf\Fpdi';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentDefinition';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentGeneratorController';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentGenerator\CreditNoteGenerator';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentGenerator\DeliveryNoteGenerator';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentGenerator\DocumentGeneratorRegistry';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentGenerator\InvoiceGenerator';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentGenerator\StornoGenerator';
$classes[] = 'Shopware\Core\Checkout\Document\DocumentService';
$classes[] = 'Shopware\Core\Checkout\Document\FileGenerator\FileGeneratorRegistry';
$classes[] = 'Shopware\Core\Checkout\Document\FileGenerator\PdfGenerator';
$classes[] = 'Shopware\Core\Checkout\Document\Renderer\CreditNoteRenderer';
$classes[] = 'Shopware\Core\Checkout\Document\Renderer\DeliveryNoteRenderer';
$classes[] = 'Shopware\Core\Checkout\Document\Renderer\InvoiceRenderer';
$classes[] = 'Shopware\Core\Checkout\Document\Renderer\StornoRenderer';
$classes[] = 'Shopware\Core\Checkout\Document\SalesChannel\DocumentRoute';
$classes[] = 'Shopware\Core\Checkout\Document\Service\DocumentConfigLoader';
$classes[] = 'Shopware\Core\Checkout\Document\Service\DocumentGenerator';
$classes[] = 'Shopware\Core\Checkout\Document\Renderer\DocumentRendererRegistry';
$classes[] = 'Shopware\Core\Checkout\Document\Service\PdfRenderer';
$classes[] = 'Shopware\Core\Checkout\Document\Service\ReferenceInvoiceLoader';
$classes[] = 'Shopware\Core\Checkout\Document\Twig\DocumentTemplateRenderer';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderAddress\OrderAddressDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderCustomer\OrderCustomerDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderDeliveryPosition\OrderDeliveryPositionDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderDelivery\OrderDeliveryDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderLineItemDownload\OrderLineItemDownloadDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderLineItem\OrderLineItemDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTag\OrderTagDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTransactionCaptureRefundPosition\OrderTransactionCaptureRefundPositionDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTransactionCaptureRefund\OrderTransactionCaptureRefundDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTransactionCapture\OrderTransactionCaptureDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTransaction\OrderTransactionDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTransaction\OrderTransactionStateHandler';
$classes[] = 'Shopware\Core\Checkout\Order\Api\OrderActionController';
$classes[] = 'Shopware\Core\Checkout\Order\Listener\OrderStateChangeEventListener';
$classes[] = 'Shopware\Core\Checkout\Order\OrderDefinition';
$classes[] = 'Shopware\Core\Checkout\Order\OrderExceptionHandler';
$classes[] = 'Shopware\Core\Checkout\Order\SalesChannel\CancelOrderRoute';
$classes[] = 'Shopware\Core\Checkout\Order\SalesChannel\OrderRoute';
$classes[] = 'Shopware\Core\Checkout\Order\SalesChannel\OrderService';
$classes[] = 'Shopware\Core\Checkout\Order\Validation\OrderValidationFactory';
$classes[] = 'Shopware\Core\Checkout\Order\SalesChannel\SetPaymentOrderRoute';
$classes[] = 'Shopware\Core\Checkout\Payment\Aggregate\PaymentMethodTranslation\PaymentMethodTranslationDefinition';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentHandler\CashPayment';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentHandler\DebitPayment';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentHandler\DefaultPayment';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentHandler\InvoicePayment';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentHandler\PaymentHandlerRegistry';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentHandler\PrePayment';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentMethodValidator';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentRefundProcessor';
$classes[] = 'Shopware\Core\Checkout\Order\Aggregate\OrderTransactionCaptureRefund\OrderTransactionCaptureRefundStateHandler';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\Token\JWTFactoryV2';
$classes[] = 'Shopware\Core\Checkout\Payment\Controller\PaymentController';
$classes[] = 'Shopware\Core\Checkout\Payment\DataAbstractionLayer\PaymentDistinguishableNameSubscriber';
$classes[] = 'Shopware\Core\Checkout\Payment\DataAbstractionLayer\PaymentHandlerIdentifierSubscriber';
$classes[] = 'Shopware\Core\Checkout\Payment\DataAbstractionLayer\PaymentMethodIndexer';
$classes[] = 'Shopware\Core\Checkout\Payment\DataAbstractionLayer\PaymentDistinguishableNameGenerator';
$classes[] = 'Shopware\Core\Checkout\Payment\DataAbstractionLayer\PaymentMethodValidator';
$classes[] = 'Shopware\Core\Checkout\Payment\PaymentMethodDefinition';
$classes[] = 'Shopware\Core\Checkout\Payment\PaymentService';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\PaymentTransactionChainProcessor';
$classes[] = 'Shopware\Core\Checkout\Payment\SalesChannel\CachedPaymentMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Payment\SalesChannel\PaymentMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Payment\SalesChannel\HandlePaymentMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Payment\SalesChannel\SalesChannelPaymentMethodDefinition';
$classes[] = 'Shopware\Core\Checkout\Payment\SalesChannel\SortedPaymentMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionCartRule\PromotionCartRuleDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionDiscountPrice\PromotionDiscountPriceDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionDiscountRule\PromotionDiscountRuleDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionDiscount\PromotionDiscountDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionIndividualCode\PromotionIndividualCodeDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionOrderRule\PromotionOrderRuleDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionPersonaCustomer\PromotionPersonaCustomerDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionPersonaRule\PromotionPersonaRuleDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionSalesChannel\PromotionSalesChannelDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionSetGroupRule\PromotionSetGroupRuleDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionSetGroup\PromotionSetGroupDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Aggregate\PromotionTranslation\PromotionTranslationDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Api\PromotionActionController';
$classes[] = 'Shopware\Core\Checkout\Promotion\Util\PromotionCodesLoader';
$classes[] = 'Shopware\Core\Checkout\Promotion\Util\PromotionCodesRemover';
$classes[] = 'Shopware\Core\Checkout\Promotion\Api\PromotionController';
$classes[] = 'Shopware\Core\Checkout\Promotion\Util\PromotionCodeService';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\FilterServiceRegistry';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\Picker\HorizontalPicker';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\Picker\VerticalPicker';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\Sorter\FilterSorterPriceAsc';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\Sorter\FilterSorterPriceDesc';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\PromotionCollector';
$classes[] = 'Shopware\Core\Checkout\Promotion\Gateway\PromotionGateway';
$classes[] = 'Shopware\Core\Checkout\Promotion\Service\PromotionDateTimeService';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\PromotionDeliveryProcessor';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\PromotionDeliveryCalculator';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\PromotionItemBuilder';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\PromotionProcessor';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\PromotionCalculator';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Composition\DiscountCompositionBuilder';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\AdvancedPackageFilter';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\AdvancedPackagePicker';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\Filter\AdvancedPackageRules';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\ScopePackager\CartScopeDiscountPackager';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\ScopePackager\SetGroupScopeDiscountPackager';
$classes[] = 'Shopware\Core\Checkout\Promotion\Cart\Discount\ScopePackager\SetScopeDiscountPackager';
$classes[] = 'Shopware\Core\Checkout\Promotion\DataAbstractionLayer\PromotionIndexer';
$classes[] = 'Shopware\Core\Checkout\Promotion\DataAbstractionLayer\PromotionExclusionUpdater';
$classes[] = 'Shopware\Core\Checkout\Promotion\DataAbstractionLayer\PromotionRedemptionUpdater';
$classes[] = 'Shopware\Core\Checkout\Promotion\PromotionDefinition';
$classes[] = 'Shopware\Core\Checkout\Promotion\Rule\PromotionCodeOfTypeRule';
$classes[] = 'Shopware\Core\Checkout\Promotion\Rule\PromotionLineItemRule';
$classes[] = 'Shopware\Core\Checkout\Promotion\Rule\PromotionValueRule';
$classes[] = 'Shopware\Core\Checkout\Promotion\Rule\PromotionsInCartCountRule';
$classes[] = 'Shopware\Core\Checkout\Promotion\Subscriber\PromotionIndividualCodeRedeemer';
$classes[] = 'Shopware\Core\Checkout\Promotion\Subscriber\Storefront\StorefrontCartSubscriber';
$classes[] = 'Shopware\Core\Checkout\Promotion\Validator\PromotionValidator';
$classes[] = 'Shopware\Core\Checkout\Shipping\Aggregate\ShippingMethodPrice\ShippingMethodPriceDefinition';
$classes[] = 'Shopware\Core\Checkout\Shipping\Aggregate\ShippingMethodTag\ShippingMethodTagDefinition';
$classes[] = 'Shopware\Core\Checkout\Shipping\Aggregate\ShippingMethodTranslation\ShippingMethodTranslationDefinition';
$classes[] = 'Shopware\Core\Checkout\Shipping\SalesChannel\CachedShippingMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Shipping\SalesChannel\ShippingMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Shipping\SalesChannel\SalesChannelShippingMethodDefinition';
$classes[] = 'Shopware\Core\Checkout\Shipping\SalesChannel\SortedShippingMethodRoute';
$classes[] = 'Shopware\Core\Checkout\Shipping\ShippingMethodDefinition';
$classes[] = 'Shopware\Core\Checkout\Shipping\Validator\ShippingMethodValidator';
$classes[] = 'Shopware\Core\Content\Category\Aggregate\CategoryTag\CategoryTagDefinition';
$classes[] = 'Shopware\Core\Content\Category\Aggregate\CategoryTranslation\CategoryTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Category\CategoryDefinition';
$classes[] = 'Shopware\Core\Content\Category\CategoryHydrator';
$classes[] = 'Shopware\Core\Content\Category\DataAbstractionLayer\CategoryIndexer';
$classes[] = 'Shopware\Core\Content\Category\DataAbstractionLayer\CategoryBreadcrumbUpdater';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\CachedCategoryRoute';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\CategoryRoute';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\CachedNavigationRoute';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\NavigationRoute';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\CategoryListRoute';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\SalesChannelCategoryDefinition';
$classes[] = 'Shopware\Core\Content\Category\SalesChannel\TreeBuildingNavigationRoute';
$classes[] = 'Shopware\Core\Content\Category\Service\CategoryBreadcrumbBuilder';
$classes[] = 'Shopware\Core\Content\Category\Service\NavigationLoader';
$classes[] = 'Shopware\Core\Content\Category\Subscriber\CategorySubscriber';
$classes[] = 'Shopware\Core\Content\Category\Validation\EntryPointValidator';
$classes[] = 'Shopware\Core\Content\Cms\Aggregate\CmsBlock\CmsBlockDefinition';
$classes[] = 'Shopware\Core\Content\Cms\Aggregate\CmsPageTranslation\CmsPageTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Cms\Aggregate\CmsSection\CmsSectionDefinition';
$classes[] = 'Shopware\Core\Content\Cms\Aggregate\CmsSlotTranslation\CmsSlotTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotDefinition';
$classes[] = 'Shopware\Core\Content\Cms\CmsPageDefinition';
$classes[] = 'Shopware\Core\Content\Cms\DataAbstractionLayer\FieldSerializer\SlotConfigFieldSerializer';
$classes[] = 'Shopware\Core\Content\Cms\DataResolver\CmsSlotsDataResolver';
$classes[] = 'Shopware\Core\Content\Cms\DataResolver\Element\FormCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Cms\DataResolver\Element\TextCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Cms\SalesChannel\CmsRoute';
$classes[] = 'Shopware\Core\Content\Cms\SalesChannel\SalesChannelCmsPageLoader';
$classes[] = 'Shopware\Core\Content\Cms\Subscriber\CmsPageDefaultChangeSubscriber';
$classes[] = 'Shopware\Core\Content\ContactForm\SalesChannel\ContactFormRoute';
$classes[] = 'Shopware\Core\Content\ContactForm\Validation\ContactFormValidationFactory';
$classes[] = 'Shopware\Core\Content\Flow\Aggregate\FlowSequence\FlowSequenceDefinition';
$classes[] = 'Shopware\Core\Content\Flow\Aggregate\FlowTemplate\FlowTemplateDefinition';
$classes[] = 'Shopware\Core\Content\Flow\Api\FlowActionCollector';
$classes[] = 'Shopware\Core\Content\Flow\DataAbstractionLayer\FieldSerializer\FlowTemplateConfigFieldSerializer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\AddCustomerAffiliateAndCampaignCodeAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\AddCustomerTagAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\AddOrderAffiliateAndCampaignCodeAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\AddOrderTagAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\ChangeCustomerGroupAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\ChangeCustomerStatusAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\GenerateDocumentAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\GrantDownloadAccessAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\RemoveCustomerTagAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\RemoveOrderTagAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\SendMailAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\SetCustomerCustomFieldAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\SetCustomerGroupCustomFieldAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\SetOrderCustomFieldAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\SetOrderStateAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Action\StopFlowAction';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\CachedFlowLoader';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\FlowLoader';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\FlowExecutor';
$classes[] = 'Shopware\Core\Content\Flow\Rule\FlowRuleScopeBuilder';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\FlowFactory';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\ConfirmUrlStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\ContactFormDataStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\ContentsStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\ContextTokenStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\CustomerGroupStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\CustomerRecoveryStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\CustomerStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\EmailStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\MailStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\MessageStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\NameStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\NewsletterRecipientStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\OrderStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\OrderTransactionStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\RecipientsStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\ResetUrlStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\ShopNameStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\SubjectStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\TemplateDataStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\UrlStorer';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\Storer\UserStorer';
$classes[] = 'Shopware\Core\Content\Flow\FlowDefinition';
$classes[] = 'Shopware\Core\Content\Flow\Indexing\FlowIndexer';
$classes[] = 'Shopware\Core\Content\Flow\Indexing\FlowPayloadUpdater';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\FlowBuilder';
$classes[] = 'Shopware\Core\Content\Flow\Rule\OrderTagRule';
$classes[] = 'Shopware\Core\Content\ImportExport\Aggregate\ImportExportFile\ImportExportFileDefinition';
$classes[] = 'Shopware\Core\Content\ImportExport\Aggregate\ImportExportLog\ImportExportLogDefinition';
$classes[] = 'Shopware\Core\Content\ImportExport\Controller\ImportExportActionController';
$classes[] = 'Shopware\Core\Content\ImportExport\Service\SupportedFeaturesService';
$classes[] = 'Shopware\Core\Content\ImportExport\Service\DownloadService';
$classes[] = 'Shopware\Core\Content\ImportExport\Service\MappingService';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\CountrySerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\CustomerSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\EntitySerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\LanguageSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\MediaSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\OrderSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\ProductCrossSellingSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\ProductSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\PromotionIndividualCodeSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Entity\SalutationSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Field\CustomFieldsSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Field\FieldSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Field\PriceSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Field\ToOneSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\Field\TranslationsSerializer';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\PrimaryKeyResolver';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\SystemDefaultValidator';
$classes[] = 'Shopware\Core\Content\ImportExport\Event\Subscriber\CategoryCriteriaSubscriber';
$classes[] = 'Shopware\Core\Content\ImportExport\Event\Subscriber\FileDeletedSubscriber';
$classes[] = 'Shopware\Core\Content\ImportExport\Event\Subscriber\ProductCategoryPathsSubscriber';
$classes[] = 'Shopware\Core\Content\ImportExport\Event\Subscriber\ProductCriteriaSubscriber';
$classes[] = 'Shopware\Core\Content\ImportExport\Event\Subscriber\ProductVariantsSubscriber';
$classes[] = 'Shopware\Core\Content\ImportExport\ImportExportFactory';
$classes[] = 'Shopware\Core\Content\ImportExport\ImportExportProfileDefinition';
$classes[] = 'Shopware\Core\Content\ImportExport\ImportExportProfileTranslationDefinition';
$classes[] = 'Shopware\Core\Content\ImportExport\Message\DeleteFileHandler';
$classes[] = 'Shopware\Core\Content\ImportExport\Message\ImportExportHandler';
$classes[] = 'Shopware\Core\Content\ImportExport\Processing\Pipe\PipeFactory';
$classes[] = 'Shopware\Core\Content\ImportExport\DataAbstractionLayer\Serializer\SerializerRegistry';
$classes[] = 'Shopware\Core\Content\ImportExport\Processing\Reader\CsvReaderFactory';
$classes[] = 'Shopware\Core\Content\ImportExport\Processing\Writer\CsvFileWriterFactory';
$classes[] = 'Shopware\Core\Content\ImportExport\ScheduledTask\CleanupImportExportFileTask';
$classes[] = 'Shopware\Core\Content\ImportExport\ScheduledTask\CleanupImportExportFileTaskHandler';
$classes[] = 'Shopware\Core\Content\ImportExport\Service\DeleteExpiredFilesService';
$classes[] = 'Shopware\Core\Content\ImportExport\Service\FileService';
$classes[] = 'Shopware\Core\Content\ImportExport\Service\ImportExportService';
$classes[] = 'Shopware\Core\Content\LandingPage\Aggregate\LandingPageSalesChannel\LandingPageSalesChannelDefinition';
$classes[] = 'Shopware\Core\Content\LandingPage\Aggregate\LandingPageTag\LandingPageTagDefinition';
$classes[] = 'Shopware\Core\Content\LandingPage\Aggregate\LandingPageTranslation\LandingPageTranslationDefinition';
$classes[] = 'Shopware\Core\Content\LandingPage\DataAbstractionLayer\LandingPageIndexer';
$classes[] = 'Shopware\Core\Content\LandingPage\LandingPageDefinition';
$classes[] = 'Shopware\Core\Content\LandingPage\LandingPageValidator';
$classes[] = 'Shopware\Core\Content\LandingPage\SalesChannel\CachedLandingPageRoute';
$classes[] = 'Shopware\Core\Content\LandingPage\SalesChannel\LandingPageRoute';
$classes[] = 'Shopware\Core\Content\LandingPage\SalesChannel\SalesChannelLandingPageDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Aggregate\MailHeaderFooterTranslation\MailHeaderFooterTranslationDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Aggregate\MailHeaderFooter\MailHeaderFooterDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Aggregate\MailTemplateMedia\MailTemplateMediaDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Aggregate\MailTemplateTranslation\MailTemplateTranslationDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Aggregate\MailTemplateTypeTranslation\MailTemplateTypeTranslationDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Aggregate\MailTemplateType\MailTemplateTypeDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Api\MailActionController';
$classes[] = 'Shopware\Core\Content\MailTemplate\Service\AttachmentLoader';
$classes[] = 'Shopware\Core\Content\MailTemplate\MailTemplateDefinition';
$classes[] = 'Shopware\Core\Content\MailTemplate\Subscriber\MailSendSubscriber';
$classes[] = 'Shopware\Core\Content\Mail\Service\MailFactory';
$classes[] = 'Shopware\Core\Content\Mail\Service\MailSender';
$classes[] = 'Symfony\Component\Mailer\Mailer';
$classes[] = 'Shopware\Core\Content\Mail\Service\MailService';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaDefaultFolder\MediaDefaultFolderDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaFolderConfigurationMediaThumbnailSize\MediaFolderConfigurationMediaThumbnailSizeDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaFolderConfiguration\MediaFolderConfigurationDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaFolder\MediaFolderDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaTag\MediaTagDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaThumbnailSize\MediaThumbnailSizeDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaThumbnail\MediaThumbnailDefinition';
$classes[] = 'Shopware\Core\Content\Media\Aggregate\MediaTranslation\MediaTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Media\Api\MediaFolderController';
$classes[] = 'Shopware\Core\Content\Media\MediaFolderService';
$classes[] = 'Shopware\Core\Content\Media\Api\MediaUploadController';
$classes[] = 'Shopware\Core\Content\Media\Cms\ImageCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Media\Cms\Type\ImageGalleryTypeDataResolver';
$classes[] = 'Shopware\Core\Content\Media\Cms\Type\ImageSliderTypeDataResolver';
$classes[] = 'Shopware\Core\Content\Media\Cms\VimeoVideoCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Media\Cms\YoutubeVideoCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Media\DataAbstractionLayer\MediaFolderConfigurationIndexer';
$classes[] = 'Shopware\Core\Content\Media\DataAbstractionLayer\MediaFolderIndexer';
$classes[] = 'Shopware\Core\Content\Media\DataAbstractionLayer\MediaIndexer';
$classes[] = 'Shopware\Core\Content\Media\DataAbstractionLayer\MediaRepositoryDecorator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\EntityRepository';
$classes[] = 'Shopware\Core\Content\Media\DataAbstractionLayer\MediaThumbnailRepositoryDecorator';
$classes[] = 'Shopware\Core\Content\Media\DeleteNotUsedMediaService';
$classes[] = 'Shopware\Core\Content\Media\File\WindowsStyleFileNameProvider';
$classes[] = 'Shopware\Core\Content\Media\File\FileSaver';
$classes[] = 'Shopware\Core\Content\Media\Metadata\MetadataLoader';
$classes[] = 'Shopware\Core\Content\Media\MediaDefinition';
$classes[] = 'Shopware\Core\Content\Media\MediaService';
$classes[] = 'Shopware\Core\Content\Media\File\FileLoader';
$classes[] = 'Shopware\Core\Content\Media\File\FileFetcher';
$classes[] = 'Shopware\Core\Content\Media\File\FileUrlValidator';
$classes[] = 'Shopware\Core\Content\Media\Message\DeleteFileHandler';
$classes[] = 'Shopware\Core\Content\Media\Message\GenerateThumbnailsHandler';
$classes[] = 'Shopware\Core\Content\Media\Metadata\MetadataLoader\ImageMetadataLoader';
$classes[] = 'Shopware\Core\Content\Media\Pathname\PathnameStrategy\FilenamePathnameStrategy';
$classes[] = 'Shopware\Core\Content\Media\Pathname\PathnameStrategy\IdPathnameStrategy';
$classes[] = 'Shopware\Core\Content\Media\Pathname\PathnameStrategy\PhysicalFilenamePathnameStrategy';
$classes[] = 'Shopware\Core\Content\Media\Pathname\PathnameStrategy\PlainPathnameStrategy';
$classes[] = 'Shopware\Core\Content\Media\Pathname\UrlGenerator';
$classes[] = 'Shopware\Core\Content\Media\Pathname\PathnameStrategy\PathnameStrategyInterface';
$classes[] = 'Shopware\Core\Content\Media\Pathname\PathnameStrategy\StrategyFactory';
$classes[] = 'Shopware\Core\Content\Media\Subscriber\MediaDeletionSubscriber';
$classes[] = 'Shopware\Core\Content\Media\Subscriber\MediaFolderConfigLoadedSubscriber';
$classes[] = 'Shopware\Core\Content\Media\Subscriber\MediaLoadedSubscriber';
$classes[] = 'Shopware\Core\Content\Media\Thumbnail\ThumbnailService';
$classes[] = 'Shopware\Core\Content\Media\TypeDetector\AudioTypeDetector';
$classes[] = 'Shopware\Core\Content\Media\TypeDetector\DefaultTypeDetector';
$classes[] = 'Shopware\Core\Content\Media\TypeDetector\DocumentTypeDetector';
$classes[] = 'Shopware\Core\Content\Media\TypeDetector\ImageTypeDetector';
$classes[] = 'Shopware\Core\Content\Media\TypeDetector\TypeDetector';
$classes[] = 'Shopware\Core\Content\Media\TypeDetector\VideoTypeDetector';
$classes[] = 'Shopware\Core\Content\Newsletter\Aggregate\NewsletterRecipientTag\NewsletterRecipientTagDefinition';
$classes[] = 'Shopware\Core\Content\Newsletter\Aggregate\NewsletterRecipient\NewsletterRecipientDefinition';
$classes[] = 'Shopware\Core\Content\Newsletter\DataAbstractionLayer\Indexing\CustomerNewsletterSalesChannelsUpdater';
$classes[] = 'Shopware\Core\Content\Newsletter\DataAbstractionLayer\NewsletterRecipientIndexer';
$classes[] = 'Shopware\Core\Content\Newsletter\Event\Subscriber\NewsletterRecipientDeletedSubscriber';
$classes[] = 'Shopware\Core\Content\Newsletter\NewsletterExceptionHandler';
$classes[] = 'Shopware\Core\Content\Newsletter\SalesChannel\NewsletterConfirmRoute';
$classes[] = 'Shopware\Core\Content\Newsletter\SalesChannel\NewsletterSubscribeRoute';
$classes[] = 'Shopware\Core\Content\Newsletter\SalesChannel\NewsletterUnsubscribeRoute';
$classes[] = 'Shopware\Core\Content\Newsletter\SalesChannel\SalesChannelNewsletterRecipientDefinition';
$classes[] = 'Shopware\Core\Content\Newsletter\ScheduledTask\NewsletterRecipientTask';
$classes[] = 'Shopware\Core\Content\Newsletter\ScheduledTask\NewsletterRecipientTaskHandler';
$classes[] = 'Shopware\Core\Content\ProductExport\Api\ProductExportController';
$classes[] = 'Shopware\Core\Content\ProductExport\DataAbstractionLayer\ProductExportExceptionHandler';
$classes[] = 'Shopware\Core\Content\ProductExport\EventListener\ProductExportEventListener';
$classes[] = 'Shopware\Core\Content\ProductExport\ProductExportDefinition';
$classes[] = 'Shopware\Core\Content\ProductExport\ProductExportHydrator';
$classes[] = 'Shopware\Core\Content\ProductExport\SalesChannel\ExportController';
$classes[] = 'Shopware\Core\Content\ProductExport\ScheduledTask\ProductExportGenerateTask';
$classes[] = 'Shopware\Core\Content\ProductExport\ScheduledTask\ProductExportGenerateTaskHandler';
$classes[] = 'Shopware\Core\Content\ProductExport\ScheduledTask\ProductExportPartialGenerationHandler';
$classes[] = 'Shopware\Core\Content\ProductExport\Service\ProductExportFileHandler';
$classes[] = 'Shopware\Core\Content\ProductExport\Service\ProductExportGenerator';
$classes[] = 'Shopware\Core\Content\ProductExport\Service\ProductExportValidator';
$classes[] = 'Shopware\Core\Content\ProductExport\Service\ProductExportRenderer';
$classes[] = 'Shopware\Core\Content\ProductExport\Service\ProductExporter';
$classes[] = 'Shopware\Core\Content\ProductExport\Validator\XmlValidator';
$classes[] = 'Shopware\Core\Content\ProductStream\Aggregate\ProductStreamFilter\ProductStreamFilterDefinition';
$classes[] = 'Shopware\Core\Content\ProductStream\Aggregate\ProductStreamFilter\ProductStreamFilterHydrator';
$classes[] = 'Shopware\Core\Content\ProductStream\Aggregate\ProductStreamTranslation\ProductStreamTranslationDefinition';
$classes[] = 'Shopware\Core\Content\ProductStream\DataAbstractionLayer\ProductStreamIndexer';
$classes[] = 'Shopware\Core\Content\ProductStream\ProductStreamDefinition';
$classes[] = 'Shopware\Core\Content\ProductStream\ProductStreamHydrator';
$classes[] = 'Shopware\Core\Content\ProductStream\ScheduledTask\UpdateProductStreamMappingTask';
$classes[] = 'Shopware\Core\Content\ProductStream\ScheduledTask\UpdateProductStreamMappingTaskHandler';
$classes[] = 'Shopware\Core\Content\ProductStream\Service\ProductStreamBuilder';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCategoryTree\ProductCategoryTreeDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCategory\ProductCategoryDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductConfiguratorSetting\ProductConfiguratorSettingDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductConfiguratorSetting\ProductConfiguratorSettingHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCrossSellingAssignedProducts\ProductCrossSellingAssignedProductsHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCrossSellingTranslation\ProductCrossSellingTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCrossSelling\ProductCrossSellingHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductCustomFieldSet\ProductCustomFieldSetDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductDownload\ProductDownloadDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductFeatureSetTranslation\ProductFeatureSetTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductFeatureSet\ProductFeatureSetDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductFeatureSet\ProductFeatureSetHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductKeywordDictionary\ProductKeywordDictionaryDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductKeywordDictionary\ProductKeywordDictionaryHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductManufacturerTranslation\ProductManufacturerTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductOption\ProductOptionDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductPrice\ProductPriceDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductPrice\ProductPriceHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductProperty\ProductPropertyDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductReview\ProductReviewDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductReview\ProductReviewHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchConfigField\ProductSearchConfigFieldDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchConfigField\ProductSearchConfigFieldExceptionHandler';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchConfigField\ProductSearchConfigFieldHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchConfig\ProductSearchConfigDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchConfig\ProductSearchConfigExceptionHandler';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchConfig\ProductSearchConfigHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchKeyword\ProductSearchKeywordDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductSearchKeyword\ProductSearchKeywordHydrator';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductStreamMapping\ProductStreamMappingDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductTag\ProductTagDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductTranslation\ProductTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductVisibility\ProductVisibilityDefinition';
$classes[] = 'Shopware\Core\Content\Product\Aggregate\ProductVisibility\ProductVisibilityHydrator';
$classes[] = 'Shopware\Core\Content\Product\Api\ProductActionController';
$classes[] = 'Shopware\Core\Content\Product\Util\VariantCombinationLoader';
$classes[] = 'Shopware\Core\Content\Product\Cart\ProductCartProcessor';
$classes[] = 'Shopware\Core\Content\Product\Cart\ProductGateway';
$classes[] = 'Shopware\Core\Content\Product\Cart\ProductFeatureBuilder';
$classes[] = 'Shopware\Core\Content\Product\Cart\ProductLineItemCommandValidator';
$classes[] = 'Shopware\Core\Content\Product\Cart\ProductLineItemValidator';
$classes[] = 'Shopware\Core\Content\Product\Cleanup\CleanupProductKeywordDictionaryTask';
$classes[] = 'Shopware\Core\Content\Product\Cleanup\CleanupProductKeywordDictionaryTaskHandler';
$classes[] = 'Shopware\Core\Content\Product\Cleanup\CleanupUnusedDownloadMediaTask';
$classes[] = 'Shopware\Core\Content\Product\Cleanup\CleanupUnusedDownloadMediaTaskHandler';
$classes[] = 'Shopware\Core\Content\Product\Cms\BuyBoxCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\CrossSellingCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\ManufacturerLogoCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\ProductBoxCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\ProductDescriptionReviewsCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\ProductListingCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\ProductNameCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\Cms\ProductSliderCmsElementResolver';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\CheapestPrice\CheapestPriceAccessorBuilder';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\ProductExceptionHandler';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\ProductIndexer';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\VariantListingUpdater';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\ProductCategoryDenormalizer';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\RatingAverageUpdater';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\CheapestPriceUpdater';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\CheapestPriceQuantitySelector';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\ProductStreamUpdater';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\StatesUpdater';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\StockUpdater';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\StockUpdate\StockUpdateFilterProvider';
$classes[] = 'Shopware\Core\Content\Product\ProductDefinition';
$classes[] = 'Shopware\Core\Content\Product\ProductHydrator';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\CrossSelling\CachedProductCrossSellingRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\CrossSelling\ProductCrossSellingRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Detail\CachedProductDetailRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Detail\ProductDetailRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\FindVariant\FindProductVariantRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Listing\CachedProductListingRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingFeaturesSubscriber';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingLoader';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Listing\ResolveCriteriaProductListingRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Price\ProductPriceCalculator';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\ProductCloseoutFilterFactory';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\ProductListRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Review\CachedProductReviewRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Review\ProductReviewRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Review\ProductReviewSaveRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\SalesChannelProductDefinition';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Search\CachedProductSearchRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Search\ProductSearchRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Search\ResolvedCriteriaProductSearchRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Sorting\ProductSortingDefinition';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Sorting\ProductSortingExceptionHandler';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Sorting\ProductSortingHydrator';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Sorting\ProductSortingTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Suggest\CachedProductSuggestRoute';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Suggest\ProductSuggestRoute';
$classes[] = 'Shopware\Core\Content\Product\Subscriber\ProductSubscriber';
$classes[] = 'Shopware\Core\Content\Product\SalesChannelProductBuilder';
$classes[] = 'Shopware\Core\Content\Product\PropertyGroupSorter';
$classes[] = 'Shopware\Core\Content\Product\ProductMaxPurchaseCalculator';
$classes[] = 'Shopware\Core\Content\Product\IsNewDetector';
$classes[] = 'Shopware\Core\Content\Product\ProductVariationBuilder';
$classes[] = 'Shopware\Core\Content\Property\Aggregate\PropertyGroupOptionTranslation\PropertyGroupOptionTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Property\Aggregate\PropertyGroupOption\PropertyGroupOptionDefinition';
$classes[] = 'Shopware\Core\Content\Property\Aggregate\PropertyGroupOption\PropertyGroupOptionHydrator';
$classes[] = 'Shopware\Core\Content\Property\Aggregate\PropertyGroupTranslation\PropertyGroupTranslationDefinition';
$classes[] = 'Shopware\Core\Content\Property\PropertyGroupDefinition';
$classes[] = 'Shopware\Core\Content\Property\PropertyGroupHydrator';
$classes[] = 'Shopware\Core\Content\Rule\Aggregate\RuleCondition\RuleConditionDefinition';
$classes[] = 'Shopware\Core\Content\Rule\Aggregate\RuleTag\RuleTagDefinition';
$classes[] = 'Shopware\Core\Content\Rule\DataAbstractionLayer\RuleAreaUpdater';
$classes[] = 'Shopware\Core\Content\Rule\DataAbstractionLayer\RuleIndexer';
$classes[] = 'Shopware\Core\Content\Rule\DataAbstractionLayer\RulePayloadSubscriber';
$classes[] = 'Shopware\Core\Content\Rule\DataAbstractionLayer\RulePayloadUpdater';
$classes[] = 'Shopware\Core\Content\Rule\RuleDefinition';
$classes[] = 'Shopware\Core\Content\Rule\RuleValidator';
$classes[] = 'Shopware\Core\Content\Seo\Api\SeoActionController';
$classes[] = 'Shopware\Core\Content\Seo\Validation\SeoUrlValidationFactory';
$classes[] = 'Shopware\Core\Content\Seo\CachedSeoResolver';
$classes[] = 'Shopware\Core\Content\Seo\SeoResolver';
$classes[] = 'Shopware\Core\Content\Seo\EmptyPathInfoResolver';
$classes[] = 'Shopware\Core\Content\Seo\Entity\Dbal\SeoUrlAssociationFieldResolver';
$classes[] = 'Shopware\Core\Content\Seo\Entity\Serializer\SeoUrlFieldSerializer';
$classes[] = 'Shopware\Core\Content\Seo\MainCategory\MainCategoryDefinition';
$classes[] = 'Shopware\Core\Content\Seo\MainCategory\SalesChannel\SalesChannelMainCategoryDefinition';
$classes[] = 'Shopware\Core\Content\Seo\SalesChannel\SeoUrlRoute';
$classes[] = 'Shopware\Core\Content\Seo\SalesChannel\StoreApiSeoResolver';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlGenerator';
$classes[] = 'Twig\Environment';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlTwigFactory';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlPersister';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlPlaceholderHandler';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlRoute\SeoUrlRouteRegistry';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlTemplate\SeoUrlTemplateDefinition';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrl\SalesChannel\SalesChannelSeoUrlDefinition';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrl\SeoUrlDefinition';
$classes[] = 'Shopware\Core\Content\Sitemap\ConfigHandler\File';
$classes[] = 'Shopware\Core\Content\Sitemap\Provider\CategoryUrlProvider';
$classes[] = 'Shopware\Core\Content\Sitemap\Provider\CustomUrlProvider';
$classes[] = 'Shopware\Core\Content\Sitemap\Provider\HomeUrlProvider';
$classes[] = 'Shopware\Core\Content\Sitemap\Provider\ProductUrlProvider';
$classes[] = 'Shopware\Core\Content\Sitemap\SalesChannel\CachedSitemapRoute';
$classes[] = 'Shopware\Core\Content\Sitemap\SalesChannel\SitemapRoute';
$classes[] = 'Shopware\Core\Content\Sitemap\Service\SitemapLister';
$classes[] = 'Shopware\Core\Content\Sitemap\ScheduledTask\SitemapGenerateTask';
$classes[] = 'Shopware\Core\Content\Sitemap\ScheduledTask\SitemapGenerateTaskHandler';
$classes[] = 'Shopware\Core\Content\Sitemap\Service\ConfigHandler';
$classes[] = 'Shopware\Core\Content\Sitemap\Service\SitemapExporter';
$classes[] = 'Shopware\Core\Content\Sitemap\Service\SitemapHandleFactory';
$classes[] = 'Shopware\Core\Framework\Adapter\Asset\AssetPackageService';
$classes[] = 'Shopware\Core\Framework\Adapter\Asset\FallbackUrlPackage';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheClearer';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheDecorator';
$classes[] = 'Symfony\Component\Cache\Adapter\TagAwareAdapter';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheIdLoader';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheInvalidationSubscriber';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheInvalidator';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheStateSubscriber';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheTagCollection';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\InvalidateCacheTask';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\InvalidateCacheTaskHandler';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\Message\CleanupOldCacheFoldersHandler';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\Script\Facade\CacheInvalidatorFacadeHookFactory';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\Script\ScriptCacheInvalidationSubscriber';
$classes[] = 'Shopware\Core\Framework\Adapter\Filesystem\FilesystemFactory';
$classes[] = 'Shopware\Core\Framework\Adapter\Filesystem\Adapter\AwsS3v3Factory';
$classes[] = 'Shopware\Core\Framework\Adapter\Filesystem\Adapter\GoogleStorageFactory';
$classes[] = 'Shopware\Core\Framework\Adapter\Filesystem\Adapter\LocalFactory';
$classes[] = 'Shopware\Core\Framework\Adapter\Filesystem\Plugin\CopyBatch';
$classes[] = 'Shopware\Core\Framework\Adapter\Translation\TranslatorCacheInvalidate';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\AppTemplateIterator';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\EntityTemplateLoader';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\NamespaceHierarchy\BundleHierarchyBuilder';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\StringTemplateRenderer';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\TemplateFinder';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\NamespaceHierarchy\NamespaceHierarchyBuilder';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\TemplateIterator';
$classes[] = 'Symfony\Bundle\TwigBundle\TemplateIterator';
$classes[] = 'Shopware\Core\Framework\Adapter\Twig\TwigVariableParser';
$classes[] = 'Shopware\Core\Framework\Api\Acl\AclAnnotationValidator';
$classes[] = 'Shopware\Core\Framework\Api\Acl\AclCriteriaValidator';
$classes[] = 'Shopware\Core\Framework\Api\Acl\AclWriteValidator';
$classes[] = 'Shopware\Core\Framework\Api\Acl\Role\AclRoleDefinition';
$classes[] = 'Shopware\Core\Framework\Api\Acl\Role\AclUserRoleDefinition';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\DefinitionService';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\StoreApiGenerator';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\OpenApi\OpenApiSchemaBuilder';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\OpenApi\OpenApiDefinitionSchemaBuilder';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\OpenApi\OpenApiLoader';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\BundleSchemaPathCollection';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\OpenApi3Generator';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\OpenApi\OpenApiPathBuilder';
$classes[] = 'Shopware\Core\Framework\Api\ApiDefinition\Generator\EntitySchemaGenerator';
$classes[] = 'Shopware\Core\Framework\Api\Context\ContextValueResolver';
$classes[] = 'Shopware\Core\Framework\Api\Controller\AccessKeyController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\AclController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\ApiController';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\RequestCriteriaBuilder';
$classes[] = 'Shopware\Core\Framework\Api\Controller\AuthController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\CacheController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\CustomSnippetFormatController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\FallbackController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\IndexingController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\InfoController';
$classes[] = 'Shopware\Core\Maintenance\System\Service\AppUrlVerifier';
$classes[] = 'GuzzleHttp\Client';
$classes[] = 'Shopware\Core\Framework\Api\Controller\IntegrationController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\SalesChannelProxyController';
$classes[] = 'Shopware\Core\Checkout\Cart\ApiOrderCartService';
$classes[] = 'Shopware\Core\Framework\Api\Controller\SyncController';
$classes[] = 'Shopware\Core\Framework\Api\Controller\UserController';
$classes[] = 'Shopware\Core\Framework\Api\Converter\ApiVersionConverter';
$classes[] = 'Shopware\Core\Framework\Api\Converter\ConverterRegistry';
$classes[] = 'Shopware\Core\Framework\Api\Converter\DefaultApiConverter';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\Acl\CreditOrderLineItemListener';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\Authentication\ApiAuthenticationListener';
$classes[] = 'League\OAuth2\Server\ResourceServer';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\BearerTokenValidator';
$classes[] = 'League\OAuth2\Server\AuthorizationValidators\BearerTokenValidator';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\UserRepository';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\Authentication\SalesChannelAuthenticationListener';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\Authentication\UserCredentialsChangedSubscriber';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\CorsListener';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\ExpectationSubscriber';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\JsonRequestTransformerListener';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\ResponseExceptionListener';
$classes[] = 'Shopware\Core\Framework\Api\EventListener\ResponseHeaderListener';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\AccessTokenRepository';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\RefreshTokenRepository';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\Scope\AdminScope';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\Scope\UserVerifiedScope';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\Scope\WriteScope';
$classes[] = 'Shopware\Core\Framework\Api\Response\ResponseFactoryInterfaceValueResolver';
$classes[] = 'Shopware\Core\Framework\Api\Response\ResponseFactoryRegistry';
$classes[] = 'Shopware\Core\Framework\Api\Response\Type\Api\JsonApiType';
$classes[] = 'Shopware\Core\Framework\Api\Serializer\JsonApiEncoder';
$classes[] = 'Shopware\Core\Framework\Api\Response\Type\Api\JsonType';
$classes[] = 'Shopware\Core\Framework\Api\Serializer\JsonEntityEncoder';
$classes[] = 'Shopware\Core\Framework\Api\Sync\SyncService';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\Executor';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\Response\ActionButtonResponseFactory';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\Response\NotificationResponseFactory';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\Response\OpenModalResponseFactory';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\Response\OpenNewTabResponseFactory';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\Response\ReloadDataResponseFactory';
$classes[] = 'Shopware\Core\Framework\App\ActiveAppsLoader';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\ActionButtonTranslation\ActionButtonTranslationDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\ActionButton\ActionButtonDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\AppPaymentMethod\AppPaymentMethodDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\AppScriptConditionTranslation\AppScriptConditionTranslationDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\AppScriptCondition\AppScriptConditionDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\AppTranslation\AppTranslationDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\CmsBlockTranslation\AppCmsBlockTranslationDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\CmsBlock\AppCmsBlockDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\FlowActionTranslation\AppFlowActionTranslationDefinition';
$classes[] = 'Shopware\Core\Framework\App\Aggregate\FlowAction\AppFlowActionDefinition';
$classes[] = 'Shopware\Core\Framework\App\Api\AppActionController';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\ActionButtonLoader';
$classes[] = 'Shopware\Core\Framework\App\ActionButton\AppActionLoader';
$classes[] = 'Shopware\Core\Framework\App\Manifest\ModuleLoader';
$classes[] = 'Shopware\Core\Framework\App\Api\AppCmsController';
$classes[] = 'Shopware\Core\Framework\App\Api\AppUrlChangeController';
$classes[] = 'Shopware\Core\Framework\App\AppDefinition';
$classes[] = 'Shopware\Core\Framework\App\AppLocaleProvider';
$classes[] = 'Shopware\Core\Framework\App\AppStateService';
$classes[] = 'Shopware\Core\Framework\App\Payment\PaymentMethodStateService';
$classes[] = 'Shopware\Core\Framework\App\AppUrlChangeResolver\MoveShopPermanentlyStrategy';
$classes[] = 'Shopware\Core\Framework\App\AppUrlChangeResolver\ReinstallAppsStrategy';
$classes[] = 'Shopware\Core\Framework\App\AppUrlChangeResolver\Resolver';
$classes[] = 'Shopware\Core\Framework\App\AppUrlChangeResolver\UninstallAppsStrategy';
$classes[] = 'Shopware\Core\Framework\App\Delta\AppConfirmationDeltaProvider';
$classes[] = 'Shopware\Core\Framework\App\Delta\DomainsDeltaProvider';
$classes[] = 'Shopware\Core\Framework\App\Delta\PermissionsDeltaProvider';
$classes[] = 'Shopware\Core\Framework\App\FlowAction\AppFlowActionLoadedSubscriber';
$classes[] = 'Shopware\Core\Framework\App\FlowAction\AppFlowActionProvider';
$classes[] = 'Shopware\Core\Framework\App\Hmac\QuerySigner';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\AppLifecycle';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\PermissionPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\CustomFieldPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\ActionButtonPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\TemplatePersister';
$classes[] = 'Shopware\Storefront\Framework\App\Template\IconTemplateLoader';
$classes[] = 'Shopware\Core\Framework\App\Template\TemplateLoader';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\WebhookPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\PaymentMethodPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\CmsBlockPersister';
$classes[] = 'Shopware\Core\Framework\App\Cms\BlockTemplateLoader';
$classes[] = 'Shopware\Core\System\CustomEntity\Schema\CustomEntityPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\FlowActionPersister';
$classes[] = 'Shopware\Administration\Snippet\AppAdministrationSnippetPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\AppLoader';
$classes[] = 'Shopware\Core\System\CustomEntity\Xml\CustomEntityXmlSchemaValidator';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\RuleConditionPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Persister\ScriptPersister';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Registration\AppRegistrationService';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Registration\HandshakeFactory';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\ScriptFileReader';
$classes[] = 'Shopware\Core\Framework\App\Payment\Handler\AppAsyncPaymentHandler';
$classes[] = 'Shopware\Core\Framework\App\Payment\Handler\AppPaymentHandler';
$classes[] = 'Shopware\Core\Framework\App\Payment\Handler\AppSyncPaymentHandler';
$classes[] = 'Shopware\Core\Framework\App\Payment\Payload\PayloadService';
$classes[] = 'Shopware\Core\Framework\App\ScheduledTask\DeleteCascadeAppsHandler';
$classes[] = 'Shopware\Core\Framework\App\ScheduledTask\DeleteCascadeAppsTask';
$classes[] = 'Shopware\Core\Framework\App\ScheduledTask\UpdateAppsHandler';
$classes[] = 'Shopware\Core\Framework\App\Lifecycle\Update\AppUpdater';
$classes[] = 'Shopware\Core\Framework\App\ScheduledTask\UpdateAppsTask';
$classes[] = 'Shopware\Core\Framework\App\ShopId\ShopIdProvider';
$classes[] = 'Shopware\Core\Framework\App\Subscriber\AppLoadedSubscriber';
$classes[] = 'Shopware\Core\Framework\App\Subscriber\AppScriptConditionConstraintsSubscriber';
$classes[] = 'Shopware\Core\Framework\App\Subscriber\CustomFieldProtectionSubscriber';
$classes[] = 'Shopware\Core\Framework\App\Template\TemplateDefinition';
$classes[] = 'Shopware\Core\Framework\App\Template\TemplateStateService';
$classes[] = 'Shopware\Core\Framework\App\Validation\ConfigValidator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Cache\EntityCacheKeyGenerator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\Common\IteratorFactory';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\CriteriaQueryBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\CriteriaPartResolver';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityDefinitionQueryHelper';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityHydrator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldAccessorBuilder\ConfigJsonFieldAccessorBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldAccessorBuilder\CustomFieldsAccessorBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldAccessorBuilder\DefaultFieldAccessorBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldAccessorBuilder\JsonFieldAccessorBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldAccessorBuilder\PriceFieldAccessorBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\ManyToManyAssociationFieldResolver';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\ManyToOneAssociationFieldResolver';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\OneToManyAssociationFieldResolver';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\FieldResolver\TranslationFieldResolver';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\JoinGroupBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\DefinitionInstanceRegistry';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\EntityProtection\EntityProtectionValidator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Event\EntityLoadedEventFactory';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\ExtensionRegistry';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Facade\AppContextCreator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Facade\RepositoryFacadeHookFactory';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Facade\RepositoryWriterFacadeHookFactory';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Facade\SalesChannelRepositoryFacadeHookFactory';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\BlobFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\BoolFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\CalculatedPriceFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\CartPriceFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\CashRoundingConfigFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\ConfigJsonFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\CreatedAtFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\CreatedByFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\CustomFieldsSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\DateFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\DateTimeFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\EmailFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\FkFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\FloatFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\IdFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\IntFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\JsonFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\ListFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\LongTextFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\ManyToManyAssociationFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\ManyToOneAssociationFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\OneToManyAssociationFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\OneToOneAssociationFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\PHPUnserializeFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\PasswordFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\PriceDefinitionFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\PriceFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\ReferenceVersionFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\RemoteAddressFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\StateMachineStateFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\StringFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\TaxFreeConfigFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\TimeZoneFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\TranslatedFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\TranslationsAssociationFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\UpdatedAtFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\UpdatedByFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\VariantListingConfigFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\VersionDataPayloadFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\VersionFieldSerializer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\ChildCountUpdater';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\EntityIndexerRegistry';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\InheritanceUpdater';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\ManyToManyIdFieldUpdater';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\Subscriber\EntityIndexingSubscriber';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\Subscriber\RegisteredIndexerSubscriber';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Indexing\TreeUpdater';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityReader';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\ApiCriteriaValidator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\CompositeEntitySearcher';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\CriteriaArrayConverter';
$classes[] = 'Shopware\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntityAggregator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityAggregator';
$classes[] = 'Shopware\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntityAggregatorHydrator';
$classes[] = 'Shopware\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntitySearcher';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntitySearcher';
$classes[] = 'Shopware\Elasticsearch\Framework\DataAbstractionLayer\ElasticsearchEntitySearchHydrator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\Parser\AggregationParser';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\Parser\SqlQueryParser';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\Term\EntityScoreQueryBuilder';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\Term\Filter\TokenFilter';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\Term\SearchTermInterpreter';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Search\Term\Tokenizer';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Validation\EntityExistsValidator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Validation\EntityNotExistsValidator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\VersionManager';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Version\Aggregate\VersionCommitData\VersionCommitDataDefinition';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Version\Aggregate\VersionCommit\VersionCommitDefinition';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Version\Cleanup\CleanupVersionTask';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Version\Cleanup\CleanupVersionTaskHandler';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Version\VersionDefinition';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityWriteGateway';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\ExceptionHandlerRegistry';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Write\EntityWriter';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityForeignKeyResolver';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Write\EntityWriteResultFactory';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Write\Validation\LockValidator';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\Write\WriteCommandExtractor';
$classes[] = 'Shopware\Core\Framework\Event\BusinessEventCollector';
$classes[] = 'Shopware\Core\Framework\Event\BusinessEventDispatcher';
$classes[] = 'Shopware\Core\Framework\Event\BusinessEventRegistry';
$classes[] = 'Shopware\Core\Framework\Event\EventAction\Aggregate\EventActionRule\EventActionRuleDefinition';
$classes[] = 'Shopware\Core\Framework\Event\EventAction\Aggregate\EventActionSalesChannel\EventActionSalesChannelDefinition';
$classes[] = 'Shopware\Core\Framework\Event\EventAction\EventActionDefinition';
$classes[] = 'Shopware\Core\Framework\Event\EventAction\EventActionSubscriber';
$classes[] = 'Shopware\Core\Framework\Increment\Controller\IncrementApiController';
$classes[] = 'Shopware\Core\Framework\Log\LogEntryDefinition';
$classes[] = 'Shopware\Core\Framework\Log\LoggingService';
$classes[] = 'Shopware\Core\Framework\Log\Monolog\ExcludeFlowEventHandler';
$classes[] = 'Monolog\Handler\FingersCrossedHandler';
$classes[] = 'Monolog\Handler\RotatingFileHandler';
$classes[] = 'Monolog\Processor\PsrLogMessageProcessor';
$classes[] = 'Shopware\Core\Framework\Log\ScheduledTask\LogCleanupTask';
$classes[] = 'Shopware\Core\Framework\Log\ScheduledTask\LogCleanupTaskHandler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Api\ConsumeMessagesController';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Subscriber\EarlyReturnMessagesListener';
$classes[] = 'Shopware\Core\Framework\MessageQueue\DeadMessage\DeadMessageDefinition';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Handler\EncryptedMessageHandler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Handler\RetryMessageHandler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Handler\SleepTaskHandler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\MessageQueueStatsDefinition';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Middleware\RetryMiddleware';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\Api\ScheduledTaskController';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\MessageQueue\RegisterScheduledTaskHandler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\Registry\TaskRegistry';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\RequeueDeadMessagesHandler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\DeadMessage\RequeueDeadMessagesService';
$classes[] = 'Shopware\Core\Framework\MessageQueue\EncryptedBus';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\RequeueDeadMessagesTask';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\ScheduledTaskDefinition';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\Scheduler\TaskScheduler';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\Subscriber\PluginLifecycleSubscriber';
$classes[] = 'Shopware\Core\Framework\MessageQueue\ScheduledTask\Subscriber\UpdatePostFinishSubscriber';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Subscriber\DeadMessageLoadedSubscriber';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Subscriber\MessageFailedHandler';
$classes[] = 'Shopware\Core\Framework\Migration\Api\MigrationController';
$classes[] = 'Shopware\Core\Framework\Migration\IndexerQueuer';
$classes[] = 'Shopware\Core\Framework\Migration\MigrationCollectionLoader';
$classes[] = 'Shopware\Core\Framework\Migration\MigrationRuntime';
$classes[] = 'Shopware\Core\Framework\Migration\MigrationSource';
$classes[] = 'Shopware\Core\Framework\Plugin\Aggregate\PluginTranslation\PluginTranslationDefinition';
$classes[] = 'Shopware\Core\Framework\Plugin\Composer\CommandExecutor';
$classes[] = 'Shopware\Core\Framework\Plugin\KernelPluginCollection';
$classes[] = 'Shopware\Core\Framework\Plugin\KernelPluginLoader\KernelPluginLoader';
$classes[] = 'Shopware\Core\Framework\Plugin\PluginDefinition';
$classes[] = 'Shopware\Core\Framework\Plugin\PluginLifecycleService';
$classes[] = 'Shopware\Core\Framework\Plugin\Requirement\RequirementsValidator';
$classes[] = 'Shopware\Core\Framework\Plugin\PluginManagementService';
$classes[] = 'Shopware\Core\Framework\Plugin\PluginZipDetector';
$classes[] = 'Shopware\Core\Framework\Plugin\PluginExtractor';
$classes[] = 'Shopware\Core\Framework\Plugin\PluginService';
$classes[] = 'Shopware\Core\Framework\Plugin\Changelog\ChangelogService';
$classes[] = 'Shopware\Core\Framework\Plugin\Changelog\ChangelogParser';
$classes[] = 'Shopware\Core\Framework\Plugin\Util\PluginFinder';
$classes[] = 'Shopware\Core\Framework\Plugin\Composer\PackageProvider';
$classes[] = 'Shopware\Core\Framework\Plugin\Util\VersionSanitizer';
$classes[] = 'Shopware\Core\Framework\Plugin\Subscriber\PluginAclPrivilegesSubscriber';
$classes[] = 'Shopware\Core\Framework\Plugin\Subscriber\PluginLoadedSubscriber';
$classes[] = 'Shopware\Core\Framework\Plugin\Util\AssetService';
$classes[] = 'Shopware\Core\Framework\Plugin\Util\PluginIdProvider';
$classes[] = 'Shopware\Core\Framework\Routing\Annotation\CriteriaValueResolver';
$classes[] = 'Shopware\Core\Framework\Routing\ApiRouteScope';
$classes[] = 'Shopware\Core\Framework\Routing\CanonicalRedirectService';
$classes[] = 'Shopware\Core\Framework\Routing\ContextResolverListener';
$classes[] = 'Shopware\Core\Framework\Routing\CoreSubscriber';
$classes[] = 'Shopware\Core\Framework\Routing\PaymentScopeWhitelist';
$classes[] = 'Shopware\Core\Framework\Routing\QueryDataBagResolver';
$classes[] = 'Shopware\Core\Framework\Routing\RequestDataBagResolver';
$classes[] = 'Shopware\Storefront\Framework\Routing\RequestTransformer';
$classes[] = 'Shopware\Core\Framework\Routing\RequestTransformer';
$classes[] = 'Shopware\Storefront\Framework\Routing\CachedDomainLoader';
$classes[] = 'Shopware\Storefront\Framework\Routing\DomainLoader';
$classes[] = 'Shopware\Core\Framework\Routing\RouteEventSubscriber';
$classes[] = 'Shopware\Core\Framework\Routing\RouteParamsCleanupListener';
$classes[] = 'Shopware\Core\Framework\Routing\RouteScope';
$classes[] = 'Shopware\Core\Framework\Routing\RouteScopeListener';
$classes[] = 'Shopware\Core\Framework\Routing\RouteScopeRegistry';
$classes[] = 'Shopware\Core\Framework\Routing\SalesChannelRequestContextResolver';
$classes[] = 'Shopware\Core\Framework\Routing\ApiRequestContextResolver';
$classes[] = 'Shopware\Core\Framework\Routing\StoreApiRouteScope';
$classes[] = 'Shopware\Core\Framework\Routing\SymfonyRouteScopeWhitelist';
$classes[] = 'Shopware\Core\Framework\Rule\Api\RuleConfigController';
$classes[] = 'Shopware\Core\Framework\Rule\Collector\RuleConditionRegistry';
$classes[] = 'Shopware\Core\Framework\Rule\Container\AndRule';
$classes[] = 'Shopware\Core\Framework\Rule\Container\MatchAllLineItemsRule';
$classes[] = 'Shopware\Core\Framework\Rule\Container\NotRule';
$classes[] = 'Shopware\Core\Framework\Rule\Container\OrRule';
$classes[] = 'Shopware\Core\Framework\Rule\Container\XorRule';
$classes[] = 'Shopware\Core\Framework\Rule\DateRangeRule';
$classes[] = 'Shopware\Core\Framework\Rule\SalesChannelRule';
$classes[] = 'Shopware\Core\Framework\Rule\ScriptRule';
$classes[] = 'Shopware\Core\Framework\Rule\SimpleRule';
$classes[] = 'Shopware\Core\Framework\Rule\TimeRangeRule';
$classes[] = 'Shopware\Core\Framework\Rule\WeekdayRule';
$classes[] = 'Shopware\Core\Framework\Script\Api\ScriptApiRoute';
$classes[] = 'Shopware\Core\Framework\Script\Api\ScriptResponseEncoder';
$classes[] = 'Shopware\Core\Framework\Script\Api\ScriptResponseFactoryFacadeHookFactory';
$classes[] = 'Shopware\Core\Framework\Script\Api\ScriptStoreApiRoute';
$classes[] = 'Shopware\Core\Framework\Script\Debugging\ScriptTraces';
$classes[] = 'Shopware\Core\Framework\Script\Execution\ScriptExecutor';
$classes[] = 'Shopware\Core\Framework\Script\Execution\ScriptLoader';
$classes[] = 'Shopware\Core\Framework\Script\ScriptDefinition';
$classes[] = 'Shopware\Core\Framework\Store\Api\ExtensionStoreActionsController';
$classes[] = 'Shopware\Core\Framework\Store\Api\ExtensionStoreDataController';
$classes[] = 'Shopware\Core\Framework\Store\Api\ExtensionStoreLicensesController';
$classes[] = 'Shopware\Core\Framework\Store\Services\ExtensionStoreLicensesService';
$classes[] = 'Shopware\Core\Framework\Store\Api\FirstRunWizardController';
$classes[] = 'Shopware\Core\Framework\Store\Api\StoreController';
$classes[] = 'Shopware\Core\Framework\Store\Authentication\LocaleProvider';
$classes[] = 'Shopware\Core\Framework\Store\Authentication\StoreRequestOptionsProvider';
$classes[] = 'Shopware\Core\Framework\Store\Services\ExtensionDataProvider';
$classes[] = 'Shopware\Core\Framework\Store\Services\ExtensionListingLoader';
$classes[] = 'Shopware\Core\Framework\Store\Services\StoreAppLifecycleService';
$classes[] = 'Shopware\Core\Framework\Store\Services\ExtensionDownloader';
$classes[] = 'Shopware\Core\Framework\Store\Services\ExtensionLifecycleService';
$classes[] = 'Shopware\Core\Framework\Store\Services\ExtensionLoader';
$classes[] = 'Shopware\Core\Framework\Store\Services\FirstRunWizardClient';
$classes[] = 'Shopware\Core\Framework\Store\Authentication\FrwRequestOptionsProvider';
$classes[] = 'Shopware\Core\Framework\Store\Services\InstanceService';
$classes[] = 'Shopware\Core\Framework\Store\Services\OpenSSLVerifier';
$classes[] = 'Shopware\Core\Framework\Store\Services\ShopSecretInvalidMiddleware';
$classes[] = 'Shopware\Core\Framework\Store\Services\StoreClient';
$classes[] = 'Shopware\Core\Framework\Store\Services\StoreService';
$classes[] = 'Shopware\Core\Framework\Store\Services\StoreSessionExpiredMiddleware';
$classes[] = 'Shopware\Core\Framework\Store\Services\TrackingEventClient';
$classes[] = 'Shopware\Core\Framework\Store\Services\VerifyResponseSignatureMiddleware';
$classes[] = 'Shopware\Core\Framework\Store\Subscriber\LicenseHostChangedSubscriber';
$classes[] = 'Shopware\Core\Framework\Update\Api\UpdateController';
$classes[] = 'Shopware\Core\Framework\Update\Services\ApiClient';
$classes[] = 'Shopware\Core\Framework\Update\Services\UpdateApiHttpClientFactory';
$classes[] = 'Shopware\Core\Framework\Update\Services\RequirementsValidator';
$classes[] = 'Shopware\Core\Framework\Update\Services\PluginCompatibility';
$classes[] = 'Shopware\Core\Framework\Update\Checkers\LicenseCheck';
$classes[] = 'Shopware\Core\Framework\Update\Checkers\MysqlVersionCheck';
$classes[] = 'Shopware\Core\Framework\Update\Checkers\PhpVersionCheck';
$classes[] = 'Shopware\Core\Framework\Update\Checkers\WriteableCheck';
$classes[] = 'Shopware\Core\Framework\Update\Services\Filesystem';
$classes[] = 'Shopware\Core\Framework\Update\Services\CreateCustomAppsDir';
$classes[] = 'Shopware\Core\Framework\Update\Services\UpdateHtaccess';
$classes[] = 'Shopware\Core\Framework\Util\HtmlSanitizer';
$classes[] = 'Shopware\Core\Framework\Validation\DataValidator';
$classes[] = 'Shopware\Core\Framework\Webhook\BusinessEventEncoder';
$classes[] = 'Shopware\Core\Framework\Webhook\EventLog\WebhookEventLogDefinition';
$classes[] = 'Shopware\Core\Framework\Webhook\Handler\WebhookEventMessageHandler';
$classes[] = 'Shopware\Core\Framework\Webhook\Hookable\HookableEventFactory';
$classes[] = 'Shopware\Core\Framework\Webhook\Hookable\WriteResultMerger';
$classes[] = 'Shopware\Core\Framework\Webhook\Subscriber\RetryWebhookMessageFailedSubscriber';
$classes[] = 'Shopware\Core\Framework\Webhook\WebhookCacheClearer';
$classes[] = 'Shopware\Core\Framework\Webhook\WebhookDefinition';
$classes[] = 'Shopware\Core\Framework\Webhook\WebhookDispatcher';
$classes[] = 'Shopware\Core\Content\Flow\Dispatching\FlowDispatcher';
$classes[] = 'Symfony\Component\EventDispatcher\EventDispatcher';
$classes[] = 'Shopware\Core\Maintenance\SalesChannel\Service\SalesChannelCreator';
$classes[] = 'Shopware\Core\Maintenance\User\Service\UserProvisioner';
$classes[] = 'Shopware\Core\System\Country\Aggregate\CountryStateTranslation\CountryStateTranslationDefinition';
$classes[] = 'Shopware\Core\System\Country\Aggregate\CountryState\CountryStateDefinition';
$classes[] = 'Shopware\Core\System\Country\Aggregate\CountryState\SalesChannel\SalesChannelCountryStateDefinition';
$classes[] = 'Shopware\Core\System\Country\Aggregate\CountryTranslation\CountryTranslationDefinition';
$classes[] = 'Shopware\Core\System\Country\CountryDefinition';
$classes[] = 'Shopware\Core\System\Country\CountryTaxFreeDeprecationUpdater';
$classes[] = 'Shopware\Core\System\Country\SalesChannel\CachedCountryRoute';
$classes[] = 'Shopware\Core\System\Country\SalesChannel\CountryRoute';
$classes[] = 'Shopware\Core\System\Country\SalesChannel\CachedCountryStateRoute';
$classes[] = 'Shopware\Core\System\Country\SalesChannel\CountryStateRoute';
$classes[] = 'Shopware\Core\System\Country\SalesChannel\SalesChannelCountryDefinition';
$classes[] = 'Shopware\Core\System\Currency\Aggregate\CurrencyCountryRounding\CurrencyCountryRoundingDefinition';
$classes[] = 'Shopware\Core\System\Currency\Aggregate\CurrencyTranslation\CurrencyTranslationDefinition';
$classes[] = 'Shopware\Core\System\Currency\CurrencyDefinition';
$classes[] = 'Shopware\Core\System\Currency\CurrencyFormatter';
$classes[] = 'Shopware\Core\System\Currency\CurrencyLoadSubscriber';
$classes[] = 'Shopware\Core\System\Currency\CurrencyValidator';
$classes[] = 'Shopware\Core\System\Currency\Rule\CurrencyRule';
$classes[] = 'Shopware\Core\System\Currency\SalesChannel\CachedCurrencyRoute';
$classes[] = 'Shopware\Core\System\Currency\SalesChannel\CurrencyRoute';
$classes[] = 'Shopware\Core\System\Currency\SalesChannel\SalesChannelCurrencyDefinition';
$classes[] = 'Shopware\Core\System\CustomEntity\Api\CustomEntityApiController';
$classes[] = 'Shopware\Core\System\CustomEntity\CustomEntityDefinition';
$classes[] = 'Shopware\Core\System\CustomEntity\CustomEntityRegistrar';
$classes[] = 'Shopware\Core\System\CustomEntity\Schema\CustomEntitySchemaUpdater';
$classes[] = 'Shopware\Core\System\CustomEntity\Schema\SchemaUpdater';
$classes[] = 'Shopware\Core\System\CustomField\Aggregate\CustomFieldSetRelation\CustomFieldSetRelationDefinition';
$classes[] = 'Shopware\Core\System\CustomField\Aggregate\CustomFieldSet\CustomFieldSetDefinition';
$classes[] = 'Shopware\Core\System\CustomField\Api\CustomFieldSetActionController';
$classes[] = 'Shopware\Core\System\CustomField\CustomFieldDefinition';
$classes[] = 'Shopware\Core\System\CustomField\CustomFieldService';
$classes[] = 'Shopware\Core\System\DeliveryTime\Aggregate\DeliveryTimeTranslation\DeliveryTimeTranslationDefinition';
$classes[] = 'Shopware\Core\System\DeliveryTime\DeliveryTimeDefinition';
$classes[] = 'Shopware\Core\System\Integration\Aggregate\IntegrationRole\IntegrationRoleDefinition';
$classes[] = 'Shopware\Core\System\Integration\IntegrationDefinition';
$classes[] = 'Shopware\Core\System\Language\CachedLanguageLoader';
$classes[] = 'Shopware\Core\System\Language\LanguageLoader';
$classes[] = 'Shopware\Core\System\Language\LanguageDefinition';
$classes[] = 'Shopware\Core\System\Language\LanguageExceptionHandler';
$classes[] = 'Shopware\Core\System\Language\LanguageValidator';
$classes[] = 'Shopware\Core\System\Language\Rule\LanguageRule';
$classes[] = 'Shopware\Core\System\Language\SalesChannel\CachedLanguageRoute';
$classes[] = 'Shopware\Core\System\Language\SalesChannel\LanguageRoute';
$classes[] = 'Shopware\Core\System\Language\SalesChannel\SalesChannelLanguageDefinition';
$classes[] = 'Shopware\Core\System\Language\TranslationValidator';
$classes[] = 'Shopware\Core\System\Locale\Aggregate\LocaleTranslation\LocaleTranslationDefinition';
$classes[] = 'Shopware\Core\System\Locale\LanguageLocaleCodeProvider';
$classes[] = 'Shopware\Core\System\Locale\LocaleDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\Aggregate\NumberRangeSalesChannel\NumberRangeSalesChannelDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\Aggregate\NumberRangeState\NumberRangeStateDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\Aggregate\NumberRangeTranslation\NumberRangeTranslationDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\Aggregate\NumberRangeTypeTranslation\NumberRangeTypeTranslationDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\Aggregate\NumberRangeType\NumberRangeTypeDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\Api\NumberRangeController';
$classes[] = 'Shopware\Core\System\NumberRange\NumberRangeDefinition';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\NumberRangeValueGenerator';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\Pattern\ValueGeneratorPatternRegistry';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\Pattern\IncrementStorage\IncrementSqlStorage';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\Pattern\IncrementStorage\IncrementStorageRegistry';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\Pattern\ValueGeneratorPatternDate';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\Pattern\ValueGeneratorPatternIncrement';
$classes[] = 'Shopware\Core\System\NumberRange\ValueGenerator\Pattern\IncrementStorage\AbstractIncrementStorage';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelAnalytics\SalesChannelAnalyticsDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelCountry\SalesChannelCountryDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelCurrency\SalesChannelCurrencyDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelDomain\SalesChannelDomainDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelLanguage\SalesChannelLanguageDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelPaymentMethod\SalesChannelPaymentMethodDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelShippingMethod\SalesChannelShippingMethodDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelTranslation\SalesChannelTranslationDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelTypeTranslation\SalesChannelTypeTranslationDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Aggregate\SalesChannelType\SalesChannelTypeDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\Api\StoreApiResponseListener';
$classes[] = 'Shopware\Core\System\SalesChannel\Api\StructEncoder';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\CartRestorer';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\Cleanup\CleanupSalesChannelContextTask';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\Cleanup\CleanupSalesChannelContextTaskHandler';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\CachedSalesChannelContextFactory';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\SalesChannelContextFactory';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\CachedBaseContextFactory';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\BaseContextFactory';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\SalesChannelContextPersister';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\SalesChannelContextRestorer';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\SalesChannelContextService';
$classes[] = 'Shopware\Core\System\SalesChannel\Context\SalesChannelContextValueResolver';
$classes[] = 'Shopware\Core\System\SalesChannel\DataAbstractionLayer\SalesChannelIndexer';
$classes[] = 'Shopware\Core\System\SalesChannel\Entity\SalesChannelDefinitionInstanceRegistry';
$classes[] = 'Shopware\Core\System\SalesChannel\SalesChannelDefinition';
$classes[] = 'Shopware\Core\System\SalesChannel\SalesChannelExceptionHandler';
$classes[] = 'Shopware\Core\System\SalesChannel\SalesChannel\ContextSwitchRoute';
$classes[] = 'Shopware\Core\System\SalesChannel\SalesChannel\StoreApiInfoController';
$classes[] = 'Shopware\Core\System\SalesChannel\StoreApiCustomFieldMapper';
$classes[] = 'Shopware\Core\System\SalesChannel\Subscriber\SalesChannelTypeValidator';
$classes[] = 'Shopware\Core\System\SalesChannel\Validation\SalesChannelValidator';
$classes[] = 'Shopware\Core\System\Salutation\Aggregate\SalutationTranslation\SalutationTranslationDefinition';
$classes[] = 'Shopware\Core\System\Salutation\DefaultSalutationValidator';
$classes[] = 'Shopware\Core\System\Salutation\SalesChannel\CachedSalutationRoute';
$classes[] = 'Shopware\Core\System\Salutation\SalesChannel\SalutationRoute';
$classes[] = 'Shopware\Core\System\Salutation\SalesChannel\SalesChannelSalutationDefinition';
$classes[] = 'Shopware\Core\System\Salutation\SalutationDefinition';
$classes[] = 'Shopware\Core\System\Snippet\Aggregate\SnippetSet\SnippetSetDefinition';
$classes[] = 'Shopware\Core\System\Snippet\Api\SnippetController';
$classes[] = 'Shopware\Core\System\Snippet\Files\SnippetFileCollection';
$classes[] = 'Shopware\Core\System\Snippet\Files\SnippetFileCollectionFactory';
$classes[] = 'Shopware\Core\System\Snippet\Files\SnippetFileLoader';
$classes[] = 'Shopware\Core\System\Snippet\Files\AppSnippetFileLoader';
$classes[] = 'Shopware\Core\System\Snippet\Filter\AddedFilter';
$classes[] = 'Shopware\Core\System\Snippet\Filter\AuthorFilter';
$classes[] = 'Shopware\Core\System\Snippet\Filter\EditedFilter';
$classes[] = 'Shopware\Core\System\Snippet\Filter\EmptySnippetFilter';
$classes[] = 'Shopware\Core\System\Snippet\Filter\NamespaceFilter';
$classes[] = 'Shopware\Core\System\Snippet\Filter\SnippetFilterFactory';
$classes[] = 'Shopware\Core\System\Snippet\Filter\TermFilter';
$classes[] = 'Shopware\Core\System\Snippet\Filter\TranslationKeyFilter';
$classes[] = 'Shopware\Core\System\Snippet\SnippetDefinition';
$classes[] = 'Shopware\Core\System\Snippet\SnippetService';
$classes[] = 'Shopware\Core\System\Snippet\Subscriber\CustomFieldSubscriber';
$classes[] = 'Shopware\Core\System\StateMachine\Aggregation\StateMachineHistory\StateMachineHistoryDefinition';
$classes[] = 'Shopware\Core\System\StateMachine\Aggregation\StateMachineState\StateMachineStateDefinition';
$classes[] = 'Shopware\Core\System\StateMachine\Aggregation\StateMachineState\StateMachineStateTranslationDefinition';
$classes[] = 'Shopware\Core\System\StateMachine\Aggregation\StateMachineTransition\StateMachineTransitionDefinition';
$classes[] = 'Shopware\Core\System\StateMachine\Api\StateMachineActionController';
$classes[] = 'Shopware\Core\System\StateMachine\Loader\InitialStateIdLoader';
$classes[] = 'Shopware\Core\System\StateMachine\StateMachineDefinition';
$classes[] = 'Shopware\Core\System\StateMachine\StateMachineRegistry';
$classes[] = 'Shopware\Core\System\StateMachine\StateMachineTranslationDefinition';
$classes[] = 'Shopware\Core\System\SystemConfig\Api\SystemConfigController';
$classes[] = 'Shopware\Core\System\SystemConfig\Facade\SystemConfigFacadeHookFactory';
$classes[] = 'Shopware\Core\System\SystemConfig\Service\ConfigurationService';
$classes[] = 'Shopware\Core\System\SystemConfig\Store\MemoizedSystemConfigStore';
$classes[] = 'Shopware\Core\System\SystemConfig\SystemConfigDefinition';
$classes[] = 'Shopware\Core\System\SystemConfig\SystemConfigService';
$classes[] = 'Shopware\Core\System\SystemConfig\MemoizedSystemConfigLoader';
$classes[] = 'Shopware\Core\System\SystemConfig\CachedSystemConfigLoader';
$classes[] = 'Shopware\Core\System\SystemConfig\SystemConfigLoader';
$classes[] = 'Shopware\Core\System\SystemConfig\Util\ConfigReader';
$classes[] = 'Shopware\Core\System\Tag\Service\FilterTagIdsService';
$classes[] = 'Shopware\Core\System\Tag\TagDefinition';
$classes[] = 'Shopware\Core\System\Tax\Aggregate\TaxRuleTypeTranslation\TaxRuleTypeTranslationDefinition';
$classes[] = 'Shopware\Core\System\Tax\Aggregate\TaxRuleType\TaxRuleTypeDefinition';
$classes[] = 'Shopware\Core\System\Tax\Aggregate\TaxRule\TaxRuleDefinition';
$classes[] = 'Shopware\Core\System\Tax\TaxDefinition';
$classes[] = 'Shopware\Core\System\Tax\TaxRuleType\EntireCountryRuleTypeFilter';
$classes[] = 'Shopware\Core\System\Tax\TaxRuleType\IndividualStatesRuleTypeFilter';
$classes[] = 'Shopware\Core\System\Tax\TaxRuleType\ZipCodeRangeRuleTypeFilter';
$classes[] = 'Shopware\Core\System\Tax\TaxRuleType\ZipCodeRuleTypeFilter';
$classes[] = 'Shopware\Core\System\Unit\Aggregate\UnitTranslation\UnitTranslationDefinition';
$classes[] = 'Shopware\Core\System\Unit\UnitDefinition';
$classes[] = 'Shopware\Core\System\User\Aggregate\UserAccessKey\UserAccessKeyDefinition';
$classes[] = 'Shopware\Core\System\User\Aggregate\UserConfig\UserConfigDefinition';
$classes[] = 'Shopware\Core\System\User\Aggregate\UserRecovery\UserRecoveryDefinition';
$classes[] = 'Shopware\Core\System\User\Api\UserRecoveryController';
$classes[] = 'Shopware\Core\System\User\Recovery\UserRecoveryService';
$classes[] = 'Shopware\Core\System\User\Api\UserValidationController';
$classes[] = 'Shopware\Core\System\User\Service\UserValidationService';
$classes[] = 'Shopware\Core\System\User\UserDefinition';
$classes[] = 'Shopware\Elasticsearch\Admin\AdminElasticsearchHelper';
$classes[] = 'Shopware\Elasticsearch\Admin\AdminSearchController';
$classes[] = 'Shopware\Elasticsearch\Admin\AdminSearchRegistry';
$classes[] = 'Shopware\Elasticsearch\Admin\AdminSearcher';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\CategoryAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\CmsPageAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\CustomerAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\CustomerGroupAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\LandingPageAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\ManufacturerAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\MediaAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\NewsletterRecipientAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\OrderAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\PaymentMethodAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\ProductAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\ProductStreamAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\PromotionAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\PropertyGroupAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\SalesChannelAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Indexer\ShippingMethodAdminSearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Admin\Subscriber\RefreshIndexSubscriber';
$classes[] = 'Shopware\Elasticsearch\Framework\DataAbstractionLayer\CriteriaParser';
$classes[] = 'Shopware\Elasticsearch\Framework\ElasticsearchHelper';
$classes[] = 'Shopware\Elasticsearch\Framework\ElasticsearchOutdatedIndexDetector';
$classes[] = 'Shopware\Elasticsearch\Framework\ElasticsearchRegistry';
$classes[] = 'Shopware\Elasticsearch\Framework\Indexing\CreateAliasTask';
$classes[] = 'Shopware\Elasticsearch\Framework\Indexing\CreateAliasTaskHandler';
$classes[] = 'Shopware\Elasticsearch\Framework\Indexing\ElasticsearchIndexer';
$classes[] = 'Shopware\Elasticsearch\Framework\Indexing\IndexCreator';
$classes[] = 'Shopware\Elasticsearch\Product\CustomFieldUpdater';
$classes[] = 'Shopware\Elasticsearch\Product\ElasticsearchProductDefinition';
$classes[] = 'Shopware\Elasticsearch\Framework\Indexing\EntityMapper';
$classes[] = 'Shopware\Elasticsearch\Product\ProductSearchQueryBuilder';
$classes[] = 'Shopware\Elasticsearch\Product\LanguageSubscriber';
$classes[] = 'Shopware\Elasticsearch\Product\ProductSearchBuilder';
$classes[] = 'Shopware\Core\Content\Product\SearchKeyword\ProductSearchBuilder';
$classes[] = 'Shopware\Core\Content\Product\SearchKeyword\ProductSearchTermInterpreter';
$classes[] = 'Shopware\Elasticsearch\Product\ProductUpdater';
$classes[] = 'Shopware\Elasticsearch\Product\SearchKeywordReplacement';
$classes[] = 'Shopware\Core\Content\Product\DataAbstractionLayer\SearchKeywordUpdater';
$classes[] = 'Shopware\Core\Content\Product\SearchKeyword\ProductSearchKeywordAnalyzer';
$classes[] = 'Shopware\Storefront\Checkout\Cart\SalesChannel\StorefrontCartFacade';
$classes[] = 'Shopware\Storefront\Checkout\Shipping\BlockedShippingMethodSwitcher';
$classes[] = 'Shopware\Storefront\Checkout\Payment\BlockedPaymentMethodSwitcher';
$classes[] = 'Shopware\Storefront\Controller\AccountOrderController';
$classes[] = 'Shopware\Storefront\Page\Account\Order\AccountOrderPageLoader';
$classes[] = 'Shopware\Storefront\Page\Account\Order\AccountEditOrderPageLoader';
$classes[] = 'Shopware\Storefront\Page\Account\Order\AccountOrderDetailPageLoader';
$classes[] = 'Shopware\Storefront\Controller\AccountPaymentController';
$classes[] = 'Shopware\Storefront\Page\Account\PaymentMethod\AccountPaymentMethodPageLoader';
$classes[] = 'Shopware\Storefront\Controller\AccountProfileController';
$classes[] = 'Shopware\Storefront\Page\Account\Overview\AccountOverviewPageLoader';
$classes[] = 'Shopware\Storefront\Page\Account\Profile\AccountProfilePageLoader';
$classes[] = 'Shopware\Storefront\Controller\AddressController';
$classes[] = 'Shopware\Storefront\Page\Address\Listing\AddressListingPageLoader';
$classes[] = 'Shopware\Storefront\Page\Address\Detail\AddressDetailPageLoader';
$classes[] = 'Shopware\Storefront\Controller\Api\CaptchaController';
$classes[] = 'Shopware\Storefront\Controller\AuthController';
$classes[] = 'Shopware\Storefront\Page\Account\RecoverPassword\AccountRecoverPasswordPageLoader';
$classes[] = 'Shopware\Storefront\Controller\CaptchaController';
$classes[] = 'Shopware\Storefront\Pagelet\Captcha\BasicCaptchaPageletLoader';
$classes[] = 'Shopware\Storefront\Framework\Captcha\BasicCaptcha\BasicCaptchaGenerator';
$classes[] = 'Shopware\Storefront\Controller\CartLineItemController';
$classes[] = 'Shopware\Core\Content\Product\Cart\ProductLineItemFactory';
$classes[] = 'Shopware\Storefront\Controller\CheckoutController';
$classes[] = 'Shopware\Storefront\Page\Checkout\Cart\CheckoutCartPageLoader';
$classes[] = 'Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoader';
$classes[] = 'Shopware\Storefront\Page\Checkout\Finish\CheckoutFinishPageLoader';
$classes[] = 'Shopware\Storefront\Page\Checkout\Offcanvas\OffcanvasCartPageLoader';
$classes[] = 'Shopware\Storefront\Controller\CmsController';
$classes[] = 'Shopware\Storefront\Controller\ContextController';
$classes[] = 'Shopware\Storefront\Controller\CookieController';
$classes[] = 'Shopware\Storefront\Framework\Cookie\AppCookieProvider';
$classes[] = 'Shopware\Storefront\Framework\Cookie\CookieProvider';
$classes[] = 'Shopware\Storefront\Controller\CountryStateController';
$classes[] = 'Shopware\Storefront\Pagelet\Country\CountryStateDataPageletLoader';
$classes[] = 'Shopware\Storefront\Controller\CsrfController';
$classes[] = 'Shopware\Storefront\Controller\DocumentController';
$classes[] = 'Shopware\Storefront\Page\Account\Document\DocumentPageLoader';
$classes[] = 'Shopware\Storefront\Controller\DownloadController';
$classes[] = 'Shopware\Storefront\Controller\ErrorController';
$classes[] = 'Shopware\Storefront\Framework\Twig\ErrorTemplateResolver';
$classes[] = 'Shopware\Storefront\Page\Navigation\Error\ErrorPageLoader';
$classes[] = 'Shopware\Storefront\Controller\FormController';
$classes[] = 'Shopware\Storefront\Controller\LandingPageController';
$classes[] = 'Shopware\Storefront\Page\LandingPage\LandingPageLoader';
$classes[] = 'Shopware\Storefront\Controller\MaintenanceController';
$classes[] = 'Shopware\Storefront\Page\Maintenance\MaintenancePageLoader';
$classes[] = 'Shopware\Storefront\Controller\NavigationController';
$classes[] = 'Shopware\Storefront\Page\Navigation\NavigationPageLoader';
$classes[] = 'Shopware\Storefront\Pagelet\Menu\Offcanvas\MenuOffcanvasPageletLoader';
$classes[] = 'Shopware\Storefront\Controller\NewsletterController';
$classes[] = 'Shopware\Storefront\Page\Newsletter\Subscribe\NewsletterSubscribePageLoader';
$classes[] = 'Shopware\Storefront\Controller\ProductController';
$classes[] = 'Shopware\Storefront\Page\Product\ProductPageLoader';
$classes[] = 'Shopware\Storefront\Page\Product\QuickView\MinimalQuickViewPageLoader';
$classes[] = 'Shopware\Storefront\Controller\RegisterController';
$classes[] = 'Shopware\Storefront\Page\Account\CustomerGroupRegistration\CustomerGroupRegistrationPageLoader';
$classes[] = 'Shopware\Storefront\Controller\ScriptController';
$classes[] = 'Shopware\Storefront\Controller\SearchController';
$classes[] = 'Shopware\Storefront\Page\Suggest\SuggestPageLoader';
$classes[] = 'Shopware\Storefront\Controller\SitemapController';
$classes[] = 'Shopware\Storefront\Page\Sitemap\SitemapPageLoader';
$classes[] = 'Shopware\Storefront\Controller\StoreApiProxyController';
$classes[] = 'Shopware\Storefront\Controller\VerificationHashController';
$classes[] = 'Shopware\Storefront\Controller\WellKnownController';
$classes[] = 'Shopware\Storefront\Controller\WishlistController';
$classes[] = 'Shopware\Storefront\Page\Wishlist\WishlistPageLoader';
$classes[] = 'Shopware\Storefront\Page\Wishlist\GuestWishlistPageLoader';
$classes[] = 'Shopware\Storefront\Pagelet\Wishlist\GuestWishlistPageletLoader';
$classes[] = 'Shopware\Storefront\Event\CartMergedSubscriber';
$classes[] = 'Shopware\Storefront\Framework\AffiliateTracking\AffiliateTrackingListener';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheResponseSubscriber';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheStore';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheStateValidator';
$classes[] = 'Shopware\Storefront\Framework\Cache\HttpCacheKeyGenerator';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheTracer';
$classes[] = 'Shopware\Core\Framework\Adapter\Cache\CacheTracer';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheWarmer\CacheWarmer';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheWarmer\CacheRouteWarmerRegistry';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheWarmer\CacheWarmerTaskHandler';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheWarmer\Navigation\NavigationRouteWarmer';
$classes[] = 'Shopware\Storefront\Framework\Cache\CacheWarmer\Product\ProductRouteWarmer';
$classes[] = 'Shopware\Storefront\Framework\Captcha\BasicCaptcha';
$classes[] = 'Shopware\Storefront\Framework\Captcha\CaptchaRouteListener';
$classes[] = 'Shopware\Storefront\Framework\Captcha\GoogleReCaptchaV2';
$classes[] = 'Shopware\Storefront\Framework\Captcha\GoogleReCaptchaV3';
$classes[] = 'Shopware\Storefront\Framework\Captcha\HoneypotCaptcha';
$classes[] = 'Shopware\Storefront\Framework\Csrf\CsrfPlaceholderHandler';
$classes[] = 'Shopware\Storefront\Framework\Csrf\CsrfRouteListener';
$classes[] = 'Shopware\Storefront\Framework\Media\StorefrontMediaValidatorRegistry';
$classes[] = 'Shopware\Storefront\Framework\Media\Validator\StorefrontMediaDocumentValidator';
$classes[] = 'Shopware\Storefront\Framework\Media\Validator\StorefrontMediaImageValidator';
$classes[] = 'Shopware\Storefront\Framework\Routing\CachedDomainLoaderInvalidator';
$classes[] = 'Shopware\Storefront\Framework\Routing\MaintenanceModeResolver';
$classes[] = 'Shopware\Storefront\Framework\Routing\NotFound\NotFoundSubscriber';
$classes[] = 'Shopware\Storefront\Framework\Routing\ResponseHeaderListener';
$classes[] = 'Shopware\Storefront\Framework\Routing\StorefrontRouteScope';
$classes[] = 'Shopware\Storefront\Framework\Routing\StorefrontSubscriber';
$classes[] = 'Shopware\Core\Content\Seo\HreflangLoader';
$classes[] = 'Shopware\Storefront\Framework\Seo\SeoUrlRoute\LandingPageSeoUrlRoute';
$classes[] = 'Shopware\Storefront\Framework\Seo\SeoUrlRoute\NavigationPageSeoUrlRoute';
$classes[] = 'Shopware\Storefront\Framework\Seo\SeoUrlRoute\ProductPageSeoUrlRoute';
$classes[] = 'Shopware\Storefront\Framework\Seo\SeoUrlRoute\SeoUrlUpdateListener';
$classes[] = 'Shopware\Core\Content\Seo\SeoUrlUpdater';
$classes[] = 'Shopware\Storefront\Framework\Twig\TwigDateRequestListener';
$classes[] = 'Shopware\Storefront\Page\Account\Login\AccountLoginPageLoader';
$classes[] = 'Shopware\Storefront\Page\Checkout\Register\CheckoutRegisterPageLoader';
$classes[] = 'Shopware\Storefront\Page\Cms\DefaultMediaResolver';
$classes[] = 'Shopware\Core\Content\Media\Cms\DefaultMediaResolver';
$classes[] = 'Shopware\Storefront\Page\GenericPageLoader';
$classes[] = 'Shopware\Storefront\Page\Product\Configurator\ProductCombinationFinder';
$classes[] = 'Shopware\Storefront\Page\Product\Configurator\ProductPageConfiguratorLoader';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Detail\ProductConfiguratorLoader';
$classes[] = 'Shopware\Core\Content\Product\SalesChannel\Detail\AvailableCombinationLoader';
$classes[] = 'Shopware\Storefront\Page\Product\Review\ProductReviewLoader';
$classes[] = 'Shopware\Storefront\Page\Search\SearchPageLoader';
$classes[] = 'Shopware\Storefront\Pagelet\Footer\FooterPageletLoader';
$classes[] = 'Shopware\Storefront\Pagelet\Header\HeaderPageletLoader';
$classes[] = 'Shopware\Storefront\Pagelet\Newsletter\Account\NewsletterAccountPageletLoader';
$classes[] = 'Shopware\Storefront\Theme\Aggregate\ThemeChildDefinition';
$classes[] = 'Shopware\Storefront\Theme\Aggregate\ThemeMediaDefinition';
$classes[] = 'Shopware\Storefront\Theme\Aggregate\ThemeSalesChannelDefinition';
$classes[] = 'Shopware\Storefront\Theme\Aggregate\ThemeTranslationDefinition';
$classes[] = 'Shopware\Storefront\Theme\CachedResolvedConfigLoaderInvalidator';
$classes[] = 'Shopware\Storefront\Theme\ConfigLoader\DatabaseAvailableThemeProvider';
$classes[] = 'Shopware\Storefront\Theme\ConfigLoader\DatabaseConfigLoader';
$classes[] = 'Shopware\Storefront\Theme\ConfigLoader\StaticFileConfigDumper';
$classes[] = 'Shopware\Storefront\Theme\Controller\ThemeController';
$classes[] = 'Shopware\Storefront\Theme\DataAbstractionLayer\ThemeIndexer';
$classes[] = 'Shopware\Storefront\Theme\Extension\LanguageExtension';
$classes[] = 'Shopware\Storefront\Theme\Extension\MediaExtension';
$classes[] = 'Shopware\Storefront\Theme\Extension\SalesChannelExtension';
$classes[] = 'Shopware\Storefront\Theme\MD5ThemePathBuilder';
$classes[] = 'Shopware\Storefront\Theme\SalesChannelThemeLoader';
$classes[] = 'Shopware\Storefront\Theme\StorefrontPluginConfiguration\StorefrontPluginConfigurationFactory';
$classes[] = 'Shopware\Storefront\Theme\StorefrontPluginRegistry';
$classes[] = 'Shopware\Storefront\Theme\Subscriber\AppLifecycleSubscriber';
$classes[] = 'Shopware\Storefront\Theme\Subscriber\FirstRunWizardSubscriber';
$classes[] = 'Shopware\Storefront\Theme\Subscriber\PluginLifecycleSubscriber';
$classes[] = 'Shopware\Storefront\Theme\Subscriber\ThemeCompilerEnrichScssVarSubscriber';
$classes[] = 'Shopware\Storefront\Theme\Subscriber\UpdateSubscriber';
$classes[] = 'Shopware\Storefront\Theme\ThemeAppLifecycleHandler';
$classes[] = 'Shopware\Storefront\Theme\ThemeConfigValueAccessor';
$classes[] = 'Shopware\Storefront\Theme\CachedResolvedConfigLoader';
$classes[] = 'Shopware\Storefront\Theme\ResolvedConfigLoader';
$classes[] = 'Shopware\Storefront\Theme\ThemeDefinition';
$classes[] = 'Shopware\Storefront\Theme\ThemeFileImporter';
$classes[] = 'Shopware\Storefront\Theme\ThemeFileResolver';
$classes[] = 'Shopware\Storefront\Theme\ThemeLifecycleHandler';
$classes[] = 'Shopware\Storefront\Theme\ThemeLifecycleService';
$classes[] = 'Shopware\Storefront\Theme\ThemeService';
$classes[] = 'Shopware\Storefront\Theme\ThemeCompiler';
$classes[] = 'Shopware\Storefront\Theme\ScssPhpCompiler';
$classes[] = 'Shopware\Storefront\Theme\Twig\ThemeNamespaceHierarchyBuilder';
$classes[] = 'Shopware\Storefront\Theme\Twig\ThemeInheritanceBuilder';
$classes[] = 'Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\RedirectController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\TemplateController';
$classes[] = 'Shopware\Core\Framework\DataAbstractionLayer\EntityRepositoryForwardCompatibilityDecorator';
$classes[] = 'Shopware\Core\Framework\Adapter\Filesystem\PrefixFilesystem';
$classes[] = 'Symfony\Component\Cache\Adapter\PhpArrayAdapter';
$classes[] = 'Doctrine\Common\Annotations\PsrCachedReader';
$classes[] = 'Shopware\Core\Framework\Compatibility\AnnotationReader';
$classes[] = 'Doctrine\Common\Annotations\AnnotationRegistry';
$classes[] = 'Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver';
$classes[] = 'Symfony\Component\Asset\VersionStrategy\EmptyVersionStrategy';
$classes[] = 'Symfony\Component\Asset\Packages';
$classes[] = 'Symfony\Component\Asset\PathPackage';
$classes[] = 'Symfony\Component\Asset\Context\RequestStackContext';
$classes[] = 'Symfony\Component\Cache\Adapter\AdapterInterface';
$classes[] = 'Symfony\Component\Cache\Adapter\AbstractAdapter';
$classes[] = 'Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer';
$classes[] = 'Symfony\Component\Cache\Marshaller\DefaultMarshaller';
$classes[] = 'Symfony\Component\Config\ResourceCheckerConfigCacheFactory';
$classes[] = 'Symfony\Component\DependencyInjection\EnvVarProcessor';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DebugHandlersListener';
$classes[] = 'Symfony\Bridge\Monolog\Logger';
$classes[] = 'Symfony\Component\HttpKernel\Debug\FileLinkFormatter';
$classes[] = 'Symfony\Component\Stopwatch\Stopwatch';
$classes[] = 'Interop\Queue\Context';
$classes[] = 'Enqueue\Client\DelegateProcessor';
$classes[] = 'Enqueue\Symfony\ContainerProcessorRegistry';
$classes[] = 'Enqueue\Client\DriverInterface';
$classes[] = 'Enqueue\Client\DriverFactory';
$classes[] = 'Enqueue\Client\Config';
$classes[] = 'Enqueue\Client\RouteCollection';
$classes[] = 'Enqueue\Symfony\Client\FlushSpoolProducerListener';
$classes[] = 'Enqueue\Symfony\Client\LazyProducer';
$classes[] = 'Enqueue\Client\Producer';
$classes[] = 'Enqueue\Rpc\RpcFactory';
$classes[] = 'Enqueue\Client\ChainExtension';
$classes[] = 'Enqueue\Consumption\QueueConsumer';
$classes[] = 'Enqueue\Consumption\ChainExtension';
$classes[] = 'Enqueue\Client\ConsumptionExtension\SetRouterPropertiesExtension';
$classes[] = 'Enqueue\Client\ConsumptionExtension\ExclusiveCommandExtension';
$classes[] = 'Enqueue\Client\ConsumptionExtension\FlushSpoolProducerExtension';
$classes[] = 'Enqueue\Client\RouterProcessor';
$classes[] = 'Enqueue\Client\SpoolProducer';
$classes[] = 'Enqueue\Consumption\Extension\ReplyExtension';
$classes[] = 'Enqueue\Consumption\Extension\SignalExtension';
$classes[] = 'Enqueue\MessengerAdapter\QueueInteropTransportFactory';
$classes[] = 'Interop\Queue\ConnectionFactory';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Enqueue\ConnectionFactoryFactory';
$classes[] = 'Enqueue\Consumption\Extension\LogExtension';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ErrorController';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\SerializerErrorRenderer';
$classes[] = 'Symfony\Bridge\Twig\ErrorRenderer\TwigErrorRenderer';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer';
$classes[] = 'Symfony\Component\HttpKernel\HttpCache\Esi';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\SurrogateListener';
$classes[] = 'Shopware\Core\Framework\Event\NestedEventDispatcher';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ErrorListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\FragmentListener';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\EsiFragmentRenderer';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\HIncludeFragmentRenderer';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\InlineFragmentRenderer';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\EventListener\IsGrantedListener';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\Request\ArgumentNameConverter';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\HttpKernelRunner';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\ResponseRunner';
$classes[] = 'Symfony\Component\Runtime\SymfonyRuntime';
$classes[] = 'Symfony\Component\HttpKernel\HttpKernel';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\ControllerResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleAwareListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleListener';
$classes[] = 'Symfony\Component\Lock\LockFactory';
$classes[] = 'Symfony\Component\Lock\PersistingStoreInterface';
$classes[] = 'Symfony\Component\Lock\Store\StoreFactory';
$classes[] = 'Symfony\Component\Mailer\EventListener\EnvelopeListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageLoggerListener';
$classes[] = 'Symfony\Component\Mailer\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Mailer\Transport\NativeTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\SendmailTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Transports';
$classes[] = 'Shopware\Core\Content\Mail\Service\MailerTransportFactory';
$classes[] = 'Shopware\Core\Content\Mail\Service\MailAttachmentsBuilder';
$classes[] = 'Shopware\Core\Content\Media\DataAbstractionLayer\MediaFolderRepositoryDecorator';
$classes[] = 'Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\HandleMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlersLocator';
$classes[] = 'Shopware\Core\Framework\MessageQueue\Monitoring\MonitoringBusDecorator';
$classes[] = 'Symfony\Component\Messenger\MessageBus';
$classes[] = 'Symfony\Component\Messenger\EventListener\AddErrorDetailsStampListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\DispatchPcntlSignalListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnRestartSignalListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnSigtermSignalListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnCustomStopExceptionListener';
$classes[] = 'Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\SendMessageMiddleware';
$classes[] = 'Shopware\Core\Framework\MessageQueue\DefaultSenderLocator';
$classes[] = 'Symfony\Component\Messenger\Transport\Sender\SendersLocator';
$classes[] = 'Symfony\Component\Messenger\Retry\MultiplierRetryStrategy';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageForRetryListener';
$classes[] = 'Symfony\Component\Messenger\RoutableMessageBus';
$classes[] = 'Symfony\Component\Messenger\Bridge\Amqp\Transport\AmqpTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportInterface';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\InMemoryTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\PhpSerializer';
$classes[] = 'Symfony\Component\Messenger\Bridge\Redis\Transport\RedisTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\Sync\SyncTransportFactory';
$classes[] = 'Symfony\Component\Mime\MimeTypes';
$classes[] = 'Monolog\Handler\BufferHandler';
$classes[] = 'Shopware\Core\Framework\Log\Monolog\DoctrineSQLHandler';
$classes[] = 'Symfony\Bridge\Monolog\Handler\ConsoleHandler';
$classes[] = 'Symfony\Component\DependencyInjection\ParameterBag\ContainerBag';
$classes[] = 'Shopware\Core\Checkout\Payment\DataAbstractionLayer\PaymentMethodRepositoryDecorator';
$classes[] = 'Symfony\Component\PropertyInfo\PropertyInfoCacheExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\PropertyInfoExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\SerializerExtractor';
$classes[] = 'Symfony\Component\HttpFoundation\RequestStack';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ResponseListener';
$classes[] = 'Shopware\Storefront\Framework\Routing\Router';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\Router';
$classes[] = 'Symfony\Component\Routing\RequestContext';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\RouterListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\DelegatingLoader';
$classes[] = 'Symfony\Component\Config\Loader\LoaderResolver';
$classes[] = 'Shopware\Core\Framework\Api\Route\ApiRouteLoader';
$classes[] = 'Symfony\Component\Routing\Loader\XmlFileLoader';
$classes[] = 'Symfony\Component\HttpKernel\Config\FileLocator';
$classes[] = 'Symfony\Component\Routing\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\GlobFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\DirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\ContainerLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\AnnotatedRouteControllerLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AnnotationDirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AnnotationFileLoader';
$classes[] = 'Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Secrets\SodiumVault';
$classes[] = 'Symfony\Component\String\LazyString';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\EventListener\HttpCacheListener';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\EventListener\ControllerListener';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\EventListener\ParamConverterListener';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterManager';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\DoctrineParamConverter';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\DateTimeParamConverter';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\EventListener\TemplateListener';
$classes[] = 'Sensio\Bundle\FrameworkExtraBundle\Templating\TemplateGuesser';
$classes[] = 'Symfony\Component\Serializer\Mapping\Factory\CacheClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\LoaderChain';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader';
$classes[] = 'Psr\Cache\CacheItemPoolInterface';
$classes[] = 'Symfony\Component\DependencyInjection\ContainerInterface';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter';
$classes[] = 'Symfony\Component\HttpFoundation\Session\SessionFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorageFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\Handler\StrictSessionHandler';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\MetadataBag';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\SessionListener';
$classes[] = 'League\OAuth2\Server\AuthorizationServer';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\ClientRepository';
$classes[] = 'Shopware\Core\Framework\Api\OAuth\ScopeRepository';
$classes[] = 'GuzzleHttp\HandlerStack';
$classes[] = 'Shopware\Core\Framework\App\Hmac\Guzzle\AuthMiddleware';
$classes[] = 'Shopware\Core\Framework\Adapter\Asset\FlysystemLastModifiedVersionStrategy';
$classes[] = 'Shopware\Storefront\Theme\ThemeAssetPackage';
$classes[] = 'Psr\Log\LoggerInterface';
$classes[] = 'Shopware\Core\Framework\Log\LoggerFactory';
$classes[] = 'League\Flysystem\FilesystemInterface';
$classes[] = 'Shopware\Core\Framework\Increment\IncrementGatewayRegistry';
$classes[] = 'Shopware\Core\Framework\Increment\MySQLIncrementer';
$classes[] = 'Lcobucci\JWT\Configuration';
$classes[] = 'Shopware\Core\Checkout\Payment\Cart\Token\JWTConfigurationFactory';
$classes[] = 'Lcobucci\JWT\Signer\Rsa\Sha256';
$classes[] = 'League\OAuth2\Server\CryptKey';
$classes[] = 'Shopware\Core\Framework\Store\Services\StoreClientFactory';
$classes[] = 'Symfony\Component\String\Slugger\AsciiSlugger';
$classes[] = 'Cocur\Slugify\Slugify';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\StreamedResponseListener';
$classes[] = 'Symfony\Component\Translation\Loader\CsvFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuDatFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IniFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\JsonFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\MoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\QtFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuResFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\XliffFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\YamlFileLoader';
$classes[] = 'Shopware\Core\Framework\Adapter\Translation\Translator';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Translation\Translator';
$classes[] = 'Symfony\Component\Translation\Formatter\MessageFormatter';
$classes[] = 'Symfony\Component\Translation\IdentityTranslator';
$classes[] = 'Symfony\Bridge\Twig\Extension\TranslationExtension';
$classes[] = 'Twig\Loader\ChainLoader';
$classes[] = 'Twig\Loader\FilesystemLoader';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageListener';
$classes[] = 'Symfony\Bridge\Twig\Mime\BodyRenderer';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelRuntime';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\LazyLoadingFragmentHandler';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\FragmentUriGenerator';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfRuntime';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerRuntime';
$classes[] = 'Symfony\Component\HttpKernel\UriSigner';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ValidateRequestListener';
$classes[] = 'Symfony\Component\Validator\ValidatorBuilder';
$classes[] = 'Symfony\Component\Validator\Validation';
$classes[] = 'Symfony\Component\Validator\ContainerConstraintValidatorFactory';
$classes[] = 'Symfony\Component\Validator\Mapping\Loader\PropertyInfoLoader';
$classes[] = 'Symfony\Component\Validator\Constraints\EmailValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\NotCompromisedPasswordValidator';

$preloaded = Preloader::preload($classes);
