{% block swag_paypal_payment_detail %}
    <div class="swag-paypal-payment-detail">

        {% block swag_paypal_payment_detail_components %}
            <component v-if="!isLoading && showPayPalPayment"
                       :is="'swag-paypal-payment-details-v1'"
                       :paymentResource="paymentResource"
                       :orderId="order.id">
            </component>

            <component v-if="!isLoading && showPayPalOrder"
                       :is="'swag-paypal-payment-details-v2'"
                       :paypalOrder="paypalOrder"
                       :orderTransaction="orderTransaction"
                       :orderTransactionId="orderTransaction.id"
                       :paypalPartnerAttributionId="orderTransaction.customFields.swag_paypal_partner_attribution_id">
            </component>
        {% endblock %}

        {% block swag_paypal_payment_detail_error_page %}
            {% block swag_paypal_payment_detail_error_page_canceled %}
                <div v-if="showCanceledPaymentError">
                    <sw-empty-state
                            :title="$tc('swag-paypal-payment.errorPage.title')"
                            :subline="$tc('swag-paypal-payment.errorPage.canceledPaymentContent')"
                            icon="regular-shopping-bag"
                            color="#A092F0">
                    </sw-empty-state>
                </div>
            {% endblock %}

            {% block swag_paypal_payment_detail_error_page_sandbox_live %}
                <div v-if="showSandboxLiveError">
                    <sw-empty-state
                            :title="$tc('swag-paypal-payment.errorPage.title')"
                            :subline="$tc('swag-paypal-payment.errorPage.sandboxLiveContent')"
                            icon="regular-shopping-bag"
                            color="#A092F0">
                    </sw-empty-state>
                </div>
            {% endblock %}

            {% block swag_paypal_payment_detail_error_page_other %}
                <div v-if="showGeneralError">
                    <sw-empty-state
                            :title="$tc('swag-paypal-payment.errorPage.title')"
                            :subline="$tc('swag-paypal-payment.errorPage.other')"
                            icon="regular-shopping-bag"
                            color="#A092F0">
                    </sw-empty-state>
                </div>
            {% endblock %}
        {% endblock %}
    </div>
{% endblock %}
