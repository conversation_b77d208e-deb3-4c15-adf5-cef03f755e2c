{% block swag_paypal_payment_action_capture %}
    <sw-modal variant="small"
              :title="$tc(`swag-paypal-payment.modal.title.capture`)"
              @modal-close="$emit('modal-close')">

        {% block swag_paypal_payment_action_capture_max_amount %}
            <sw-text-field v-model="maxCaptureValue"
                           :label="$tc('swag-paypal-payment.captureAction.maxAmount')"
                           :disabled="true">
            </sw-text-field>
        {% endblock %}

        {% block swag_paypal_payment_action_capture_value %}
            <sw-number-field v-model="captureValue"
                             :max="maxCaptureValue"
                             :min="0"
                             :label="$tc('swag-paypal-payment.captureAction.currentAmount')">
                <template #suffix>
                    {{ currency }}
                </template>
            </sw-number-field>
        {% endblock %}

        {% block swag_paypal_payment_action_capture_is_final_capture %}
            <sw-checkbox-field v-model="isFinalCapture"
                               :label="$tc('swag-paypal-payment.captureAction.isFinal')">
            </sw-checkbox-field>
        {% endblock %}

        {% block swag_paypal_payment_action_capture_hint %}
            <sw-alert v-if="showHint">
                {{ $tc('swag-paypal-payment.captureAction.isFinalHint') }}
            </sw-alert>
        {% endblock %}

        {% block swag_paypal_payment_action_capture_submit_button %}
            <template #modal-footer>
                <sw-button variant="primary"
                           @click="capture">
                    {{ $tc('swag-paypal-payment.captureAction.button.text') }}
                </sw-button>
            </template>
        {% endblock %}

        <sw-loader v-if="isLoading"></sw-loader>
    </sw-modal>
{% endblock %}
