<?php declare(strict_types=1);

namespace Bs\Odoo\Subscriber;

use Bs\Odoo\MessageQueue\Message\OrderDataMessage;
use Psr\Log\LoggerInterface;
use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Checkout\Order\OrderEvents;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\DataAbstractionLayer\EntityWriteResult;
use Shopware\Core\Framework\DataAbstractionLayer\Event\EntityWrittenEvent;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

/**
 * Class ListenToOrderChanges
 * @package BsOdoo
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ListenToOrderChanges implements EventSubscriberInterface
{
    public function __construct(
        private readonly LoggerInterface     $logger,
        protected MessageBusInterface        $messageBus,
        private readonly SystemConfigService $configService
    )
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::ORDER_WRITTEN_EVENT => 'OnOrderWritten'
        ];
    }

    /**
     * Add or update order in Odoo when order added or updated
     *
     * @param EntityWrittenEvent $event
     * @return void
     */
    public function OnOrderWritten(EntityWrittenEvent $event): void
    {
        try {
            // Check if the event is for the live version
            if (Defaults::LIVE_VERSION !== $event->getContext()->getVersionId()) {
                return;
            }

            // Check if the integration is enabled
            if ($this->configService->get('BsOdoo.config.enableIntegration') !== true) {
                return;
            }

            // Check if the event is for the Order entity
            if ($event->getEntityName() !== OrderDefinition::ENTITY_NAME) {
                return;
            }

            // Sync only if the order is created for the Order entity
            foreach ($event->getWriteResults() as $writeResult) {
                // Skip if the write result is not an insert operation
//                if ($writeResult->getOperation() === EntityWriteResult::OPERATION_INSERT) {
                    $this->messageBus->dispatch(
                        OrderDataMessage::init($writeResult->getPrimaryKey())
                    );
//                }
            }
        } catch (Throwable $exception) {
            $this->logger->error($exception);
        }
    }
}