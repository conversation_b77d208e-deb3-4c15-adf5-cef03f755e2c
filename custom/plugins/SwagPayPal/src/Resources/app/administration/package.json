{"name": "swagpaypal-admin", "version": "1.0.0", "description": "Administration for PayPal", "main": "index.js", "author": "shopware AG", "license": "MIT", "scripts": {"unit": "jest --config jest.config.js --ci", "unit-watch": "jest --config jest.config.js --watch", "lint": "eslint --ext .js,.ts,.vue src", "lint-fix": "eslint --ext .js,.ts,.vue src --fix", "lint-ci": "npm run lint -- --format junit --output-file eslint.junit.xml"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@babel/preset-env": "^7.20.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-runtime": "^7.19.6", "@shopware-ag/eslint-config-base": "^2.0.0", "@shopware-ag/jest-preset-sw6-admin": "4.0.1", "@vue/test-utils": "^1.3.3", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-internal-rules": "file:internal-rules", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-vue": "^7.20.0", "flush-promises": "^1.0.2", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "vuex": "3.6.2"}, "dependencies": {"vue": "2.6.14", "vue-template-compiler": "2.6.14"}, "optionalDependencies": {"administration": "^6.x"}}