{% block swag_paypal_payment_details_v1 %}
    <div class="swag-paypal-payment-details_v1">
        <sw-card position-identifier="swag-paypal-payment-details-v1"
                 :title="$tc('swag-paypal-payment.paymentDetails.cardTitle')">
            <template #grid>

                {% block swag_paypal_payment_actions_section %}
                    <sw-card-section secondary
                                     slim>
                        <swag-paypal-payment-actions :orderId="orderId" :paymentResource="paymentResource">
                        </swag-paypal-payment-actions>
                    </sw-card-section>
                {% endblock %}

                <sw-card-section divider="top">
                    <sw-container columns="1fr"
                                  gap="0px 30px">

                        {% block swag_paypal_payment_detail_invoice %}
                            {% block swag_paypal_payment_detail_invoice_heading %}
                                <h3>
                                    {{ $tc('swag-paypal-payment.paymentDetails.invoice.heading') }}
                                </h3>
                            {% endblock %}

                            {% block swag_paypal_payment_detail_invoice_list %}
                                <sw-description-list class="swag-paypal-payment-detail__data" grid="1fr 1fr">
                                    <dt>{{ $tc('swag-paypal-payment.paymentDetails.invoice.totalAmount') }}</dt>
                                    <dd>{{ amount.total }} {{ currency }}</dd>

                                    <dt>{{ $tc('swag-paypal-payment.paymentDetails.invoice.subTotal') }}</dt>
                                    <dd> {{ amount.details.subtotal }} {{ currency }}</dd>

                                    <dt>{{ $tc('swag-paypal-payment.paymentDetails.invoice.shipping') }}</dt>
                                    <dd> {{ amount.details.shipping }} {{ currency }}</dd>
                                </sw-description-list>
                            {% endblock %}

                            {% block swag_paypal_payment_detail_payment %}
                                {% block swag_paypal_payment_detail_payment_heading %}
                                    <h3 class="swag-paypal-payment-detail__heading">
                                        {{ $tc('swag-paypal-payment.paymentDetails.payment.heading') }}
                                    </h3>
                                {% endblock %}

                                {% block swag_paypal_payment_detail_payment_list %}
                                    <sw-description-list class="swag-paypal-payment-detail__data" grid="1fr 1fr">
                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.payment.intent') }}</dt>
                                        <dd>{{ $tc(`swag-paypal.settingForm.behavior.intent.${paymentResource.intent}`) }}</dd>

                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.payment.paymentId') }}</dt>
                                        <dd>{{ paymentResource.id }}</dd>

                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.payment.cartId') }}</dt>
                                        <dd>{{ paymentResource.cart }}</dd>

                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.payment.state') }}</dt>
                                        <dd>{{ paymentResource.state }}</dd>

                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.payment.createTime') }}</dt>
                                        <dd>{{ createDateTime }}</dd>

                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.payment.updateTime') }}</dt>
                                        <dd>{{ updateDateTime }}</dd>
                                    </sw-description-list>
                                {% endblock %}
                            {% endblock %}

                            {% block swag_paypal_payment_detail_customer %}
                                {% block swag_paypal_payment_detail_customer_heading %}
                                    <h3 class="swag-paypal-payment-detail__heading">
                                        {{ $tc('swag-paypal-payment.paymentDetails.customer.heading') }}
                                    </h3>
                                {% endblock %}

                                {% block swag_paypal_payment_detail_customer_list %}
                                    <sw-description-list class="swag-paypal-payment-detail__data" grid="1fr 1fr">
                                        <dt>{{ $tc('swag-paypal-payment.paymentDetails.customer.payerId') }}</dt>
                                        <dd>{{ payerId }}</dd>
                                    </sw-description-list>
                                {% endblock %}
                            {% endblock %}
                        {% endblock %}

                    </sw-container>
                </sw-card-section>
            </template>
        </sw-card>

        {% block swag_paypal_payment_transaction_history_card %}
            <sw-card position-identifier="swag-paypal-payment-transaction-history-v1"
                     :title="$tc('swag-paypal-payment.transactionHistory.cardTitle')">
                <template #grid>

                    {% block swag_paypal_payment_transaction_history_grid %}
                        <sw-data-grid :dataSource="relatedResources"
                                      :columns="relatedResourceColumns"
                                      :showActions="false"
                                      :showSelection="false">
                        </sw-data-grid>
                    {% endblock %}

                </template>
            </sw-card>
        {% endblock %}

    </div>
{% endblock %}
