{% block swag_paypal_content_card_channel_config_express %}
    <sw-card position-identifier="swag-paypal-card-channel-config-express"
             :title="$tc('swag-paypal.settingForm.express.cardTitle')"
             :subtitle="$tc('swag-paypal.settingForm.express.cardSubtitle')">

        {% block swag_paypal_content_card_channel_config_express_settings %}
            <div v-if="actualConfigData" class="swag-paypal-settings-express-fields">

                {% block swag_paypal_content_card_channel_config_express_settings_detail_enabled %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsDetailEnabled']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsDetailEnabled']"
                        :customInheritationCheckFunction="checkBoolFieldInheritance">
                        <template #content="props">
                            <sw-switch-field name="SwagPayPal.settings.ecsDetailEnabled"
                                             bordered
                                             :mapInheritance="props"
                                             :label="$tc('swag-paypal.settingForm.express.ecsDetailEnabled.label')"
                                             :helpText="$tc('swag-paypal.settingForm.express.ecsDetailEnabled.helpText')"
                                             :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                             :value="props.currentValue"
                                             @change="props.updateCurrentValue">
                            </sw-switch-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_cart_enabled %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsCartEnabled']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsCartEnabled']"
                        :customInheritationCheckFunction="checkBoolFieldInheritance">
                        <template #content="props">
                            <sw-switch-field name="SwagPayPal.settings.ecsCartEnabled"
                                             bordered
                                             :mapInheritance="props"
                                             :label="$tc('swag-paypal.settingForm.express.ecsCartEnabled.label')"
                                             :helpText="$tc('swag-paypal.settingForm.express.ecsCartEnabled.helpText')"
                                             :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                             :value="props.currentValue"
                                             @change="props.updateCurrentValue">
                            </sw-switch-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_off_canvas_enabled %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsOffCanvasEnabled']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsOffCanvasEnabled']"
                        :customInheritationCheckFunction="checkBoolFieldInheritance">
                        <template #content="props">
                            <sw-switch-field name="SwagPayPal.settings.ecsOffCanvasEnabled"
                                             bordered
                                             :mapInheritance="props"
                                             :label="$tc('swag-paypal.settingForm.express.ecsOffCanvasEnabled.label')"
                                             :helpText="$tc('swag-paypal.settingForm.express.ecsOffCanvasEnabled.helpText')"
                                             :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                             :value="props.currentValue"
                                             @change="props.updateCurrentValue">
                            </sw-switch-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}


                {% block swag_paypal_content_card_channel_config_express_settings_login_enabled %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsLoginEnabled']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsLoginEnabled']"
                        :customInheritationCheckFunction="checkBoolFieldInheritance">
                        <template #content="props">
                            <sw-switch-field name="SwagPayPal.settings.ecsLoginEnabled"
                                             bordered
                                             :mapInheritance="props"
                                             :label="$tc('swag-paypal.settingForm.express.ecsLoginEnabled.label')"
                                             :helpText="$tc('swag-paypal.settingForm.express.ecsLoginEnabled.helpText')"
                                             :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                             :value="props.currentValue"
                                             @change="props.updateCurrentValue">
                            </sw-switch-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_listing_enabled %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsListingEnabled']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsListingEnabled']"
                        :customInheritationCheckFunction="checkBoolFieldInheritance">
                        <template #content="props">
                            <sw-switch-field name="SwagPayPal.settings.ecsListingEnabled"
                                             bordered
                                             :mapInheritance="props"
                                             :label="$tc('swag-paypal.settingForm.express.ecsListingEnabled.label')"
                                             :helpText="$tc('swag-paypal.settingForm.express.ecsListingEnabled.helpText')"
                                             :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                             :value="props.currentValue"
                                             @change="props.updateCurrentValue">
                            </sw-switch-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_button_color %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsButtonColor']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsButtonColor']"
                        :customInheritationCheckFunction="checkTextFieldInheritance"
                        :label="$tc('swag-paypal.settingForm.express.ecsButtonColor.label')">
                        <template #content="props">
                            <sw-single-select name="SwagPayPal.settings.ecsButtonColor"
                                              labelProperty="name"
                                              valueProperty="id"
                                              :options="buttonColorOptions"
                                              :isInherited="props.isInherited"
                                              :disabled="props.isInherited || renderSettingsDisabled"
                                              :value="props.currentValue"
                                              @change="props.updateCurrentValue">
                            </sw-single-select>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_button_shape %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsButtonShape']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsButtonShape']"
                        :customInheritationCheckFunction="checkTextFieldInheritance"
                        :label="$tc('swag-paypal.settingForm.express.ecsButtonShape.label')">
                        <template #content="props">
                            <sw-single-select name="SwagPayPal.settings.ecsButtonShape"
                                              labelProperty="name"
                                              valueProperty="id"
                                              :options="buttonShapeOptions"
                                              :isInherited="props.isInherited"
                                              :disabled="props.isInherited || renderSettingsDisabled"
                                              :value="props.currentValue"
                                              @change="props.updateCurrentValue">
                            </sw-single-select>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_button_locale %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsButtonLanguageIso']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsButtonLanguageIso']"
                        :customInheritationCheckFunction="checkTextFieldInheritance">
                        <template #content="props">
                            <swag-paypal-locale-field name="SwagPayPal.settings.ecsButtonLanguageIso"
                                                    :mapInheritance="props"
                                                    :label="$tc('swag-paypal.settingForm.express.ecsButtonLanguageIso.label')"
                                                    :helpText="$tc('swag-paypal.settingForm.express.ecsButtonLanguageIso.helpText')"
                                                    :disabled="props.isInherited || renderSettingsDisabled"
                                                    :value="props.currentValue"
                                                    @change="props.updateCurrentValue"
                                                    @preventSave="preventSave">
                            </swag-paypal-locale-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}

                {% block swag_paypal_content_card_channel_config_express_settings_show_pay_later %}
                    <sw-inherit-wrapper
                        v-model="actualConfigData['SwagPayPal.settings.ecsShowPayLater']"
                        :inheritedValue="selectedSalesChannelId == null ? null : allConfigs['null']['SwagPayPal.settings.ecsShowPayLater']"
                        :customInheritationCheckFunction="checkBoolFieldInheritance">
                        <template #content="props">
                            <sw-switch-field name="SwagPayPal.settings.ecsShowPayLater"
                                             bordered
                                             :mapInheritance="props"
                                             :label="$tc('swag-paypal.settingForm.express.ecsShowPayLater.label')"
                                             :helpText="$tc('swag-paypal.settingForm.express.ecsShowPayLater.helpText')"
                                             :disabled="props.isInherited || renderSettingsDisabled"
                                             :value="props.currentValue"
                                             @change="props.updateCurrentValue">
                            </sw-switch-field>
                        </template>
                    </sw-inherit-wrapper>
                {% endblock %}
            </div>
        {% endblock %}
    </sw-card>
{% endblock %}
