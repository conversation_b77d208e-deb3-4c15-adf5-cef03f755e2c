<?php //Shopware%255CCore%255CFramework%255CApi%255CController%255CCacheController%2523clearContainerCache

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Shopware\\Core\\Framework\\Routing\\Annotation\\Since'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Shopware\\Core\\Framework\\Routing\\Annotation\\Since')),
        clone ($p['Symfony\\Component\\Routing\\Annotation\\Route'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Symfony\\Component\\Routing\\Annotation\\Route')),
    ],
    null,
    [
        'Shopware\\Core\\Framework\\Routing\\Annotation\\Since' => [
            'value' => [
                '6.2.0.0',
            ],
        ],
        'Symfony\\Component\\Routing\\Annotation\\Route' => [
            'path' => [
                1 => '/api/_action/container_cache',
            ],
            'name' => [
                1 => 'api.action.container-cache.delete',
            ],
            'defaults' => [
                1 => [
                    '_acl' => [
                        'system:clear:cache',
                    ],
                ],
            ],
            'methods' => [
                1 => [
                    'DELETE',
                ],
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
