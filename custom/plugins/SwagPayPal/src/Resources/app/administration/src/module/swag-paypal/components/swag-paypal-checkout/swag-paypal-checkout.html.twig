{% block swag_paypal_content_card_channel_config_checkout %}
<sw-card
    class="sw-card--grid"
    position-identifier="swag-paypal-checkout"
    :is-loading="isLoading || isLoadingPaymentMethods"
    :title="$tc('swag-paypal.settingForm.checkout.cardTitle')"
>
    <template #avatar>
        {% block swag_paypal_content_card_channel_config_checkout_avatar %}
        <img
            :src="'swagpaypal/static/img/paypal-logo-avatar.svg' | asset"
            :alt="$tc('swag-paypal.settingForm.checkout.appImageAlt')"
        >
        {% endblock %}
    </template>

    <template #header-right>
        {% block swag_paypal_content_card_channel_config_checkout_settings_link %}
        <sw-internal-link
            v-if="showSettingsLink"
            :routerLink="{ name: 'swag.paypal.index' }"
        >
            {{ $tc('swag-paypal.settingForm.checkout.settingsLink') }}
        </sw-internal-link>
        {% endblock %}
    </template>

    {% block swag_paypal_content_card_channel_config_checkout_container %}
    <div class="swag-paypal-checkout">

        {% block swag_paypal_content_card_channel_config_checkout_container_box %}
        <div :class="{'swag-paypal-checkout__box': showMerchantInformation }">

            {% block swag_paypal_content_card_channel_config_checkout_container_sandbox_switch %}
            <sw-inherit-wrapper v-if="showSandboxToggle"
                                v-model="actualConfigData['SwagPayPal.settings.sandbox']"
                                class="swag-paypal-checkout__sandbox-toggle"
                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.sandbox']"
                                :customInheritationCheckFunction="checkBoolFieldInheritance">
                <template #content="props">
                    <sw-switch-field v-tooltip.left="{
                                         message: isSandbox ? $tc('swag-paypal.settingForm.checkout.sandbox.onlySandboxTooltip') : $tc('swag-paypal.settingForm.checkout.sandbox.onlyLiveTooltip'),
                                         disabled: !sandboxToggleDisabled,
                                     }"
                                     name="SwagPayPal.settings.sandbox"
                                     :mapInheritance="props"
                                     :label="$tc('swag-paypal.settingForm.checkout.sandbox.label')"
                                     :helpText="$tc('swag-paypal.settingForm.checkout.sandbox.helpText')"
                                     :disabled="sandboxToggleDisabled || props.isInherited || !acl.can('swag_paypal.editor')"
                                     :value="props.currentValue"
                                     @change="props.updateCurrentValue">
                    </sw-switch-field>
                </template>
            </sw-inherit-wrapper>
            {% endblock %}

            {% block swag_paypal_content_card_channel_config_checkout_container_header %}
            <p
                v-if="!showMerchantInformation"
                class="swag-paypal-checkout__header"
            >
                {{ $tc('swag-paypal.settingForm.checkout.header') }}
            </p>
            {% endblock %}

            {% block swag_paypal_content_card_channel_config_checkout_container_description %}
            <p
                v-if="!showMerchantInformation"
                class="swag-paypal-checkout__description"
            >
                {{ $tc('swag-paypal.settingForm.checkout.description') }}
            </p>
            {% endblock %}

            {% block swag_paypal_content_card_channel_config_checkout_container_banner %}
            <sw-alert
                v-if="!isOnboardingPPCPFinished"
                variant="info"
                class="swag-paypal-checkout__banner"
            >
                <span v-html="$tc('swag-paypal.settingForm.checkout.banner')"></span>
            </sw-alert>
            {% endblock %}

            {% block swag_paypal_content_card_channel_config_checkout_container_merchant_information %}
            <p
                v-if="showMerchantInformation"
                class="swag-paypal-checkout__merchant-information"
            >
                {{ merchantEmail }}
                <br>
                {{ merchantInformation.merchantIntegrations.legal_name }}
            </p>
            {% endblock %}

            {% block swag_paypal_content_card_channel_config_checkout_container_onboarding %}
                {% block swag_paypal_content_card_channel_config_checkout_container_onboarding_button %}
                <a
                    class="sw-button sw-button--ghost"
                    target="_blank"
                    :href="onboardingUrlLive"
                    :disabled="!acl.can('swag_paypal.editor')"
                    data-paypal-onboard-complete="onboardingCallbackLive"
                    data-paypal-button="true"
                >
                    {{ liveButtonTitle }}
                </a>
                <a
                    class="swag-paypal-checkout__button-onboarding-sandbox"
                    target="_blank"
                    :href="onboardingUrlSandbox"
                    :disabled="!acl.can('swag_paypal.editor')"
                    data-paypal-onboard-complete="onboardingCallbackSandbox"
                    data-paypal-button="true"
                >
                    {{ sandboxButtonTitle }}
                </a>
                {% endblock %}
            {% endblock %}
        </div>
        {% endblock %}

        {% block swag_paypal_content_card_channel_config_checkout_container_payment_methods %}
        <p class="swag-paypal-checkout__payment-method-headline">
            {{ $tc('swag-paypal.settingForm.checkout.paymentMethodText') }}
        </p>
        {% endblock %}

        {% block swag_paypal_content_card_channel_config_checkout_container_payment_methods_items %}
        <div class="swag-paypal-checkout-payment-method_container">

            {% block swag_paypal_content_card_channel_config_checkout_container_payment_methods_items_item %}
            <swag-paypal-checkout-method
                v-for="paymentMethod in paymentMethods"
                :key="paymentMethod.id"
                :payment-method="paymentMethod"
                :onboarding-status="onboardingStatus(paymentMethod)"
            />
            {% endblock %}

        </div>
        {% endblock %}

        {% block swag_paypal_content_card_channel_config_checkout_container_switch_show_credentials %}
        <sw-switch-field
            v-if="allowShowCredentials"
            v-model="showCredentials"
            :label="$tc('swag-paypal.settingForm.checkout.showCredentials')"
            :disabled="!acl.can('swag_paypal.editor')"
            @change="updateShowCredentials"
        />
        {% endblock %}

        {% block swag_paypal_content_card_channel_config_checkout_container_credentials_slot %}
        <slot name="credentials"></slot>
        {% endblock %}

    </div>
    {% endblock %}

    {% block sw_deactivate_paypal_plus_modal %}
    <sw-modal
        v-if="plusDeprecationModalOpen"
        class="swag-paypal-checkout-modal"
        :title="$tc('swag-paypal.settingForm.checkout.deactivatePayPalPlusModal.title')"
        variant="default"
        @modal-close="closeModal"
    >
        {% block sw_deactivate_paypal_plus_modal_text %}
        <p class="swag-paypal-checkout-modal__text">
            {{ $tc('swag-paypal.settingForm.checkout.deactivatePayPalPlusModal.text') }}
        </p>
        {% endblock %}

        {% block sw_deactivate_paypal_plus_modal_warning %}
        <sw-alert
            class="swag-paypal-checkout-modal__warning"
            variant="warning"
        >
            {{ $tc('swag-paypal.settingForm.checkout.deactivatePayPalPlusModal.warning') }}
        </sw-alert>
        {% endblock %}

        {% block sw_deactivate_paypal_plus_modal_footer %}
        <template #modal-footer>
            {% block sw_deactivate_paypal_plus_modal_cancel_button %}
            <sw-button @click="closeModal">
                {{ $tc('swag-paypal.settingForm.checkout.deactivatePayPalPlusModal.button.cancel') }}
            </sw-button>
            {% endblock %}

            {% block ssw_deactivate_paypal_plus_modal_confirm_button %}
            <sw-button
                variant="primary"
                @click="deactivatePayPalPlus"
            >
                {{ $tc('swag-paypal.settingForm.checkout.deactivatePayPalPlusModal.button.deactivate') }}
            </sw-button>
            {% endblock %}

        </template>
        {% endblock %}
    </sw-modal>
    {% endblock %}

</sw-card>
{% endblock %}
