[2025-08-07T06:26:37.944543+00:00] bs_odoo_logs.ERROR: TypeError: Bs\Odoo\Service\CustomerSyncService::getCountryStateId(): Argument #1 ($address) must be of type Shopware\Core\Checkout\Customer\Aggregate\CustomerAddress\CustomerAddressEntity|Shopware\Core\Checkout\Order\Aggregate\OrderAddress\OrderAddressEntity, null given, called in /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php on line 80 and defined in /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php:214 Stack trace: #0 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php(80): Bs\Odoo\Service\CustomerSyncService->getCountryStateId(NULL) #1 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php(70): Bs\Odoo\Service\CustomerSyncService->sync('759cc9b4da9c4b7...') #2 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/OrderSyncService.php(46): Bs\Odoo\Service\CustomerSyncService->getOrCreateOdooId('759cc9b4da9c4b7...') #3 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/MessageQueue/Handler/OrderDataHandler.php(40): Bs\Odoo\Service\OrderSyncService->sync('4c9cf8314a7f4ce...') #4 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/HandleMessageMiddleware.php(97): Bs\Odoo\MessageQueue\Handler\OrderDataHandler->__invoke(Object(Bs\Odoo\MessageQueue\Message\OrderDataMessage)) #5 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/SendMessageMiddleware.php(73): Symfony\Component\Messenger\Middleware\HandleMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #6 /Users/<USER>/workspace/shopware64202/vendor/shopware/core/Framework/MessageQueue/Middleware/RetryMiddleware.php(49): Symfony\Component\Messenger\Middleware\SendMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #7 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/FailedMessageProcessingMiddleware.php(34): Shopware\Core\Framework\MessageQueue\Middleware\RetryMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #8 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/DispatchAfterCurrentBusMiddleware.php(68): Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #9 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/RejectRedeliveredMessageMiddleware.php(48): Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #10 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/AddBusNameStampMiddleware.php(37): Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #11 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/MessageBus.php(77): Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #12 /Users/<USER>/workspace/shopware64202/vendor/shopware/core/Framework/MessageQueue/Monitoring/MonitoringBusDecorator.php(45): Symfony\Component\Messenger\MessageBus->dispatch(Object(Symfony\Component\Messenger\Envelope), Array) #13 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/RoutableMessageBus.php(54): Shopware\Core\Framework\MessageQueue\Monitoring\MonitoringBusDecorator->dispatch(Object(Symfony\Component\Messenger\Envelope), Array) #14 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Worker.php(158): Symfony\Component\Messenger\RoutableMessageBus->dispatch(Object(Symfony\Component\Messenger\Envelope)) #15 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Worker.php(107): Symfony\Component\Messenger\Worker->handleMessage(Object(Symfony\Component\Messenger\Envelope), 'default') #16 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Command/ConsumeMessagesCommand.php(234): Symfony\Component\Messenger\Worker->run(Array) #17 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Command/Command.php(298): Symfony\Component\Messenger\Command\ConsumeMessagesCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #18 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(1058): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #19 /Users/<USER>/workspace/shopware64202/vendor/symfony/framework-bundle/Console/Application.php(96): Symfony\Component\Console\Application->doRunCommand(Object(Symfony\Component\Messenger\Command\ConsumeMessagesCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #20 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(301): Symfony\Bundle\FrameworkBundle\Console\Application->doRunCommand(Object(Symfony\Component\Messenger\Command\ConsumeMessagesCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #21 /Users/<USER>/workspace/shopware64202/vendor/symfony/framework-bundle/Console/Application.php(82): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #22 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(171): Symfony\Bundle\FrameworkBundle\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #23 /Users/<USER>/workspace/shopware64202/bin/console(77): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput)) #24 {main} [] []
[2025-08-07T06:26:38.242965+00:00] bs_odoo_logs.ERROR: TypeError: Bs\Odoo\Service\CustomerSyncService::getCountryStateId(): Argument #1 ($address) must be of type Shopware\Core\Checkout\Customer\Aggregate\CustomerAddress\CustomerAddressEntity|Shopware\Core\Checkout\Order\Aggregate\OrderAddress\OrderAddressEntity, null given, called in /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php on line 80 and defined in /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php:214 Stack trace: #0 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php(80): Bs\Odoo\Service\CustomerSyncService->getCountryStateId(NULL) #1 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php(70): Bs\Odoo\Service\CustomerSyncService->sync('759cc9b4da9c4b7...') #2 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/OrderSyncService.php(46): Bs\Odoo\Service\CustomerSyncService->getOrCreateOdooId('759cc9b4da9c4b7...') #3 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/MessageQueue/Handler/OrderDataHandler.php(40): Bs\Odoo\Service\OrderSyncService->sync('4c9cf8314a7f4ce...') #4 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/HandleMessageMiddleware.php(97): Bs\Odoo\MessageQueue\Handler\OrderDataHandler->__invoke(Object(Bs\Odoo\MessageQueue\Message\OrderDataMessage)) #5 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/SendMessageMiddleware.php(73): Symfony\Component\Messenger\Middleware\HandleMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #6 /Users/<USER>/workspace/shopware64202/vendor/shopware/core/Framework/MessageQueue/Middleware/RetryMiddleware.php(49): Symfony\Component\Messenger\Middleware\SendMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #7 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/FailedMessageProcessingMiddleware.php(34): Shopware\Core\Framework\MessageQueue\Middleware\RetryMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #8 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/DispatchAfterCurrentBusMiddleware.php(68): Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #9 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/RejectRedeliveredMessageMiddleware.php(48): Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #10 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/AddBusNameStampMiddleware.php(37): Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #11 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/MessageBus.php(77): Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #12 /Users/<USER>/workspace/shopware64202/vendor/shopware/core/Framework/MessageQueue/Monitoring/MonitoringBusDecorator.php(45): Symfony\Component\Messenger\MessageBus->dispatch(Object(Symfony\Component\Messenger\Envelope), Array) #13 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/RoutableMessageBus.php(54): Shopware\Core\Framework\MessageQueue\Monitoring\MonitoringBusDecorator->dispatch(Object(Symfony\Component\Messenger\Envelope), Array) #14 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Worker.php(158): Symfony\Component\Messenger\RoutableMessageBus->dispatch(Object(Symfony\Component\Messenger\Envelope)) #15 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Worker.php(107): Symfony\Component\Messenger\Worker->handleMessage(Object(Symfony\Component\Messenger\Envelope), 'default') #16 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Command/ConsumeMessagesCommand.php(234): Symfony\Component\Messenger\Worker->run(Array) #17 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Command/Command.php(298): Symfony\Component\Messenger\Command\ConsumeMessagesCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #18 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(1058): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #19 /Users/<USER>/workspace/shopware64202/vendor/symfony/framework-bundle/Console/Application.php(96): Symfony\Component\Console\Application->doRunCommand(Object(Symfony\Component\Messenger\Command\ConsumeMessagesCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #20 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(301): Symfony\Bundle\FrameworkBundle\Console\Application->doRunCommand(Object(Symfony\Component\Messenger\Command\ConsumeMessagesCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #21 /Users/<USER>/workspace/shopware64202/vendor/symfony/framework-bundle/Console/Application.php(82): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #22 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(171): Symfony\Bundle\FrameworkBundle\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #23 /Users/<USER>/workspace/shopware64202/bin/console(77): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput)) #24 {main} [] []
[2025-08-07T06:29:25.787424+00:00] bs_odoo_logs.ERROR: TypeError: Bs\Odoo\Service\OdooMappingService::updateMapping(): Argument #3 ($odooId) must be of type int, null given, called in /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php on line 108 and defined in /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/OdooMappingService.php:40 Stack trace: #0 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php(108): Bs\Odoo\Service\OdooMappingService->updateMapping('customer_addres...', '2aba4efeab104d0...', NULL) #1 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/CustomerSyncService.php(70): Bs\Odoo\Service\CustomerSyncService->sync('759cc9b4da9c4b7...') #2 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/Service/OrderSyncService.php(46): Bs\Odoo\Service\CustomerSyncService->getOrCreateOdooId('759cc9b4da9c4b7...') #3 /Users/<USER>/workspace/shopware64202/custom/plugins/BsOdoo/src/MessageQueue/Handler/OrderDataHandler.php(40): Bs\Odoo\Service\OrderSyncService->sync('4c9cf8314a7f4ce...') #4 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/HandleMessageMiddleware.php(97): Bs\Odoo\MessageQueue\Handler\OrderDataHandler->__invoke(Object(Bs\Odoo\MessageQueue\Message\OrderDataMessage)) #5 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/SendMessageMiddleware.php(73): Symfony\Component\Messenger\Middleware\HandleMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #6 /Users/<USER>/workspace/shopware64202/vendor/shopware/core/Framework/MessageQueue/Middleware/RetryMiddleware.php(49): Symfony\Component\Messenger\Middleware\SendMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #7 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/FailedMessageProcessingMiddleware.php(34): Shopware\Core\Framework\MessageQueue\Middleware\RetryMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #8 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/DispatchAfterCurrentBusMiddleware.php(68): Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #9 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/RejectRedeliveredMessageMiddleware.php(48): Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #10 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Middleware/AddBusNameStampMiddleware.php(37): Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #11 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/MessageBus.php(77): Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware->handle(Object(Symfony\Component\Messenger\Envelope), Object(Symfony\Component\Messenger\Middleware\StackMiddleware)) #12 /Users/<USER>/workspace/shopware64202/vendor/shopware/core/Framework/MessageQueue/Monitoring/MonitoringBusDecorator.php(45): Symfony\Component\Messenger\MessageBus->dispatch(Object(Symfony\Component\Messenger\Envelope), Array) #13 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/RoutableMessageBus.php(54): Shopware\Core\Framework\MessageQueue\Monitoring\MonitoringBusDecorator->dispatch(Object(Symfony\Component\Messenger\Envelope), Array) #14 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Worker.php(158): Symfony\Component\Messenger\RoutableMessageBus->dispatch(Object(Symfony\Component\Messenger\Envelope)) #15 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Worker.php(107): Symfony\Component\Messenger\Worker->handleMessage(Object(Symfony\Component\Messenger\Envelope), 'default') #16 /Users/<USER>/workspace/shopware64202/vendor/symfony/messenger/Command/ConsumeMessagesCommand.php(234): Symfony\Component\Messenger\Worker->run(Array) #17 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Command/Command.php(298): Symfony\Component\Messenger\Command\ConsumeMessagesCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #18 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(1058): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #19 /Users/<USER>/workspace/shopware64202/vendor/symfony/framework-bundle/Console/Application.php(96): Symfony\Component\Console\Application->doRunCommand(Object(Symfony\Component\Messenger\Command\ConsumeMessagesCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #20 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(301): Symfony\Bundle\FrameworkBundle\Console\Application->doRunCommand(Object(Symfony\Component\Messenger\Command\ConsumeMessagesCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #21 /Users/<USER>/workspace/shopware64202/vendor/symfony/framework-bundle/Console/Application.php(82): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #22 /Users/<USER>/workspace/shopware64202/vendor/symfony/console/Application.php(171): Symfony\Bundle\FrameworkBundle\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput)) #23 /Users/<USER>/workspace/shopware64202/bin/console(77): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput)) #24 {main} [] []
