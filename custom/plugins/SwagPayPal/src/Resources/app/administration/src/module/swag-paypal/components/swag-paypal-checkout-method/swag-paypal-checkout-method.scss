@import "~scss/variables";
@import "../../.././../app/assets/scss/variables";

.swag-paypal-checkout-method {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    align-items: center;
    border: 1px solid $color-gray-300;
    border-radius: $border-radius-default;
    margin-bottom: 16px;
    column-gap: 16px;
    padding: 10px 16px;
    font-size: $font-size-xs;

    &__name {
        font-weight: $font-weight-semi-bold;
    }

    &__icon {
        width: 32px;
        max-height: 24px;
    }

    &__dynamic {
        display: grid;
        grid-auto-flow: column;
        align-items: center;
        justify-items: end;
        justify-content: right;
        grid-gap: 12px;
        margin: 0 12px;

        .sw-label {
            margin: 0;
        }
    }

    &__show-detail-link {
        font-size: $font-size-xs;
        color: $color-shopware-brand-500;
    }

    .sw-help-text {
        position: relative;
    }

    .swag-plugin-box-with-onboarding__status-label {
        &.sw-label:not(.sw-label--appearance-badged) {
            padding-left: 6px;
            border-color: transparent;
        }
    }

    .swag-plugin-box-with-onboarding__status-badge {
        margin: 0;
    }

    .swag-plugin-box-with-onboarding__status-text {
        padding-left: 4px;
        font-weight: 600;
    }
}
