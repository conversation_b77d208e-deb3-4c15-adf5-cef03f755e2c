{% block sw_sales_channel_modal_detail_header_icon %}
    <span v-if="isPayPalPosSalesChannel(detailType.id)"
          class="sw-sales-channel-modal-detail__header-icon">
         <img class="swag-paypal-pos-modal-detail__icon"
              :src="'swagpaypal/static/img/paypal-pos-logo.svg' | asset">
    </span>

    <template v-else>
        {% parent %}
    </template>
{% endblock %}

{% block sw_sales_channel_modal_detail_header_meta %}
    <div v-if="isPayPalPosSalesChannel(detailType.id)"
         class="sw-sales-channel-modal-detail__header-meta">
        <h4 class="sw-sales-channel-modal-detail__header-name">
            {{ detailType.translated.name }}
        </h4>
        <div class="sw-sales-channel-modal-detail__header-manufacturer">
            {{ $tc('swag-paypal-pos.general.salesChannelDetailDescription.manufacturer') }}
        </div>
    </div>

    <template v-else>
        {% parent %}
    </template>
{% endblock %}

{% block sw_sales_channel_modal_detail_description %}
    <template v-if="isPayPalPosSalesChannel(detailType.id)">
        <h4 class="sw-sales-channel-modal-detail__description-long-title">
            {{ $tc('swag-paypal-pos.general.salesChannelDetailDescription.title') }}
        </h4>
        <div v-if="detailType.translated.descriptionLong"
             class="sw-sales-channel-modal-detail__description-long-text">
            {{ detailType.translated.descriptionLong }}
        </div>
        <div v-else
             class="sw-sales-channel-modal-detail__description-long-text">
            {{ detailType.translated.description }}
        </div>
    </template>

    <template v-else>
        {% parent %}
    </template>
{% endblock %}
