{% block sw_settings_payment_list_column_active_editor %}
<sw-icon
    v-if="needsOnboarding(item)"
    v-tooltip.left="{
        message: $tc('sw-settings-payment-list.needOnboardingTooltip'),
    }"
    name="regular-lock"
    small
/>
<template v-else>
    {% parent %}
</template>
{% endblock %}

{% block sw_settings_payment_list_column_active_label %}
<sw-icon
    v-if="needsOnboarding(item)"
    v-tooltip.left="{
        message: $tc('sw-settings-payment-list.needOnboardingTooltip'),
    }"
    name="regular-lock"
    small
/>
<template v-else>
    {% parent %}
</template>
{% endblock %}

{% block sw_settings_payment_list_content_inner %}
    <swag-paypal-created-component-helper
        @on-created-component="fetchMerchantIntegrations"
    />

    {% parent %}
{% endblock %}
