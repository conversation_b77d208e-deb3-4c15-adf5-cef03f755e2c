<?php //Shopware%255CCore%255CFramework%255CApi%255CController%255CInfoController%2523queue

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Shopware\\Core\\Framework\\Routing\\Annotation\\Since'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Shopware\\Core\\Framework\\Routing\\Annotation\\Since')),
        clone ($p['Symfony\\Component\\Routing\\Annotation\\Route'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Symfony\\Component\\Routing\\Annotation\\Route')),
    ],
    null,
    [
        'Shopware\\Core\\Framework\\Routing\\Annotation\\Since' => [
            'value' => [
                '6.4.6.0',
            ],
        ],
        'Symfony\\Component\\Routing\\Annotation\\Route' => [
            'path' => [
                1 => '/api/_info/queue.json',
            ],
            'name' => [
                1 => 'api.info.queue',
            ],
            'methods' => [
                1 => [
                    'GET',
                ],
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
