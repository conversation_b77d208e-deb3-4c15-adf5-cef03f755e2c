<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BsOdoo Plugin Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #0066cc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #0066cc;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .header h2 {
            color: #666;
            font-size: 18px;
            font-weight: normal;
        }
        .meta-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #0066cc;
            margin-bottom: 30px;
        }
        .meta-info p {
            margin: 5px 0;
        }
        h2 {
            color: #0066cc;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h3 {
            color: #495057;
            margin-top: 25px;
        }
        h4 {
            color: #6c757d;
            margin-top: 20px;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .toc ol {
            margin: 0;
            padding-left: 20px;
        }
        .toc li {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #e9ecef;
            font-weight: bold;
            color: #495057;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        .feature-list {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .step-number {
            background-color: #0066cc;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .footer {
            border-top: 2px solid #e9ecef;
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BsOdoo Plugin Documentation</h1>
        <h2>Shopware 6 - Odoo Integration Plugin</h2>
    </div>

    <div class="meta-info">
        <p><strong>Version:</strong> 1.0.0</p>
        <p><strong>Developer:</strong> BrainStream Technolabs</p>
        <p><strong>Website:</strong> https://brainstreamtechnolabs.com</p>
        <p><strong>License:</strong> MIT</p>
        <p><strong>Author:</strong> Bhavesh &lt;<EMAIL>&gt;</p>
    </div>

    <div class="toc">
        <h3>Table of Contents</h3>
        <ol>
            <li>Overview</li>
            <li>System Requirements</li>
            <li>Installation Steps</li>
            <li>Configuration Setup</li>
            <li>Entity Mapping</li>
            <li>Sync Details</li>
            <li>API Endpoints</li>
            <li>Database Schema</li>
            <li>Troubleshooting</li>
            <li>Support</li>
        </ol>
    </div>

    <h2>1. Overview</h2>
    <p>The BsOdoo plugin provides seamless integration between Shopware 6 and Odoo ERP systems. It enables automatic synchronization of customers, products, and orders from Shopware to Odoo, maintaining data consistency across both platforms.</p>

    <div class="feature-list">
        <h3>Key Features</h3>
        <ul>
            <li><strong>Real-time Order Synchronization:</strong> Automatically sync orders from Shopware to Odoo</li>
            <li><strong>Customer Management:</strong> Sync customer data including addresses and contact information</li>
            <li><strong>Product Integration:</strong> Synchronize product catalog between systems</li>
            <li><strong>Bi-directional Mapping:</strong> Maintain ID mapping between Shopware and Odoo entities</li>
            <li><strong>Configuration Verification:</strong> Built-in connection testing and validation</li>
            <li><strong>Message Queue Support:</strong> Asynchronous processing for better performance</li>
            <li><strong>Comprehensive Logging:</strong> Detailed logging for monitoring and debugging</li>
        </ul>
    </div>

    <h2>2. System Requirements</h2>

    <h3>Shopware Requirements</h3>
    <ul>
        <li><strong>Shopware Version:</strong> 6.4.* or higher</li>
        <li><strong>PHP Extensions:</strong>
            <ul>
                <li>ext-curl</li>
                <li>ext-libxml</li>
                <li>ext-iconv</li>
                <li>ext-simplexml</li>
                <li>ext-dom</li>
            </ul>
        </li>
    </ul>

    <h3>Odoo Requirements</h3>
    <ul>
        <li><strong>Odoo Version:</strong> 13.0 or higher</li>
        <li><strong>XML-RPC API:</strong> Enabled and accessible</li>
        <li><strong>API User:</strong> Dedicated user account with appropriate permissions</li>
    </ul>

    <h3>Server Requirements</h3>
    <ul>
        <li><strong>PHP:</strong> 7.4 or higher</li>
        <li><strong>MySQL:</strong> 5.7 or higher / MariaDB 10.3 or higher</li>
        <li><strong>Memory:</strong> Minimum 512MB PHP memory limit</li>
        <li><strong>Network:</strong> HTTP/HTTPS access to Odoo server</li>
    </ul>

    <h2>3. Installation Steps</h2>

    <h3><span class="step-number">1</span>Download and Extract</h3>
    <ol>
        <li>Download the BsOdoo plugin package</li>
        <li>Extract the plugin to your Shopware installation directory:</li>
    </ol>
    <div class="code-block">
/path/to/shopware/custom/plugins/BsOdoo/
    </div>

    <h3><span class="step-number">2</span>Install via Command Line</h3>
    <div class="code-block">
# Navigate to Shopware root directory
cd /path/to/shopware

# Refresh plugin list
bin/console plugin:refresh

# Install the plugin
bin/console plugin:install --activate BsOdoo

# Clear cache
bin/console cache:clear
    </div>

    <h3><span class="step-number">3</span>Install via Administration Panel</h3>
    <ol>
        <li>Log in to Shopware Administration</li>
        <li>Navigate to <strong>Extensions > My Extensions</strong></li>
        <li>Click <strong>Upload extension</strong></li>
        <li>Select the BsOdoo plugin ZIP file</li>
        <li>Click <strong>Install</strong> and then <strong>Activate</strong></li>
    </ol>

    <div class="info">
        <strong>Note:</strong> The plugin automatically creates the required database table during installation.
    </div>

    <h2>4. Configuration Setup</h2>

    <h3>Access Configuration</h3>
    <ol>
        <li>Navigate to <strong>Settings > System > Plugins</strong></li>
        <li>Find <strong>BsOdoo</strong> plugin and click <strong>Configure</strong></li>
    </ol>

    <h3>General Configuration</h3>
    <table>
        <tr>
            <th>Field</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>Enable Integration</td>
            <td>Boolean</td>
            <td>No</td>
            <td>Master switch to activate/deactivate the Odoo integration</td>
        </tr>
    </table>

    <h3>Odoo Configuration</h3>
    <table>
        <tr>
            <th>Field</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>Server URL</td>
            <td>URL</td>
            <td>Yes</td>
            <td>Complete URL to your Odoo server (e.g., https://your-odoo-instance.com)</td>
        </tr>
        <tr>
            <td>Username</td>
            <td>Text</td>
            <td>Yes</td>
            <td>Username of the Odoo API user</td>
        </tr>
        <tr>
            <td>API Key/Password</td>
            <td>Password</td>
            <td>Yes</td>
            <td>API key or password for the Odoo user (API key recommended)</td>
        </tr>
        <tr>
            <td>Database Name</td>
            <td>Text</td>
            <td>Yes</td>
            <td>Name of the Odoo database to connect to</td>
        </tr>
    </table>

    <div class="highlight">
        <strong>Connection Verification:</strong> Use the built-in API verify button to test your connection settings before saving.
    </div>

    <h3>Additional Configuration</h3>
    <table>
        <tr>
            <th>Field</th>
            <th>Type</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>Company ID</td>
            <td>Integer</td>
            <td>ID of the Odoo company for data synchronization</td>
        </tr>
        <tr>
            <td>Default Category ID</td>
            <td>Integer</td>
            <td>Default category for new products from Shopware</td>
        </tr>
        <tr>
            <td>Default Tax ID</td>
            <td>Integer</td>
            <td>Default tax configuration for new products</td>
        </tr>
        <tr>
            <td>Default Pricelist ID</td>
            <td>Integer</td>
            <td>Default pricelist for new customers</td>
        </tr>
    </table>

    <h2>5. Entity Mapping</h2>
    <p>The plugin maintains ID mappings between Shopware and Odoo entities in the <code>bs_odoo</code> table.</p>

    <h3>Supported Entity Types</h3>
    <table>
        <tr>
            <th>Module</th>
            <th>Shopware Entity</th>
            <th>Odoo Model</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>customer</td>
            <td>Customer</td>
            <td>res.partner</td>
            <td>Customer records</td>
        </tr>
        <tr>
            <td>customer_address</td>
            <td>Customer Address</td>
            <td>res.partner</td>
            <td>Customer addresses</td>
        </tr>
        <tr>
            <td>product</td>
            <td>Product</td>
            <td>product.product</td>
            <td>Product catalog</td>
        </tr>
        <tr>
            <td>order</td>
            <td>Order</td>
            <td>sale.order</td>
            <td>Sales orders</td>
        </tr>
        <tr>
            <td>currency</td>
            <td>Currency</td>
            <td>res.currency</td>
            <td>Currency definitions</td>
        </tr>
        <tr>
            <td>country</td>
            <td>Country</td>
            <td>res.country</td>
            <td>Country data</td>
        </tr>
        <tr>
            <td>state</td>
            <td>Country State</td>
            <td>res.country.state</td>
            <td>State/Province data</td>
        </tr>
    </table>

    <h2>6. Sync Details</h2>

    <h3>Order Synchronization</h3>
    <div class="info">
        <strong>Trigger Events:</strong> Order creation/update in Shopware<br>
        <strong>Processing:</strong> Asynchronous via message queue<br>
        <strong>Condition:</strong> Integration must be enabled
    </div>

    <h4>Sync Process</h4>
    <ol>
        <li><strong>Order Data Extraction:</strong> Retrieve order with all associations</li>
        <li><strong>Customer Sync:</strong> Ensure customer exists in Odoo</li>
        <li><strong>Product Sync:</strong> Verify all products exist in Odoo</li>
        <li><strong>Address Handling:</strong> Sync billing and shipping addresses</li>
        <li><strong>Order Creation:</strong> Create sale.order in Odoo</li>
        <li><strong>Mapping Storage:</strong> Store Shopware-Odoo ID relationship</li>
    </ol>

    <h3>Customer Synchronization</h3>
    <h4>Customer Data Mapping</h4>
    <ul>
        <li><strong>Name:</strong> Company name or full name</li>
        <li><strong>Email:</strong> Customer email address</li>
        <li><strong>Address:</strong> Street, city, postal code</li>
        <li><strong>Country/State:</strong> Mapped to Odoo location data</li>
        <li><strong>Phone:</strong> Contact number</li>
        <li><strong>VAT:</strong> Tax identification number</li>
        <li><strong>Customer Number:</strong> Reference field</li>
    </ul>

    <h4>Address Handling</h4>
    <ul>
        <li><strong>Billing Address:</strong> Primary contact in Odoo</li>
        <li><strong>Shipping Address:</strong> Child contact with type 'delivery'</li>
        <li><strong>Multiple Addresses:</strong> Supported via parent-child relationship</li>
    </ul>

    <h3>Product Synchronization</h3>
    <h4>Product Data Mapping</h4>
    <ul>
        <li><strong>Name:</strong> Product name</li>
        <li><strong>SKU:</strong> Product number (default_code in Odoo)</li>
        <li><strong>Description:</strong> Product description</li>
        <li><strong>Price:</strong> List price and cost price</li>
        <li><strong>Category:</strong> Product category</li>
        <li><strong>Tax:</strong> Tax configuration</li>
        <li><strong>Company:</strong> Multi-company support</li>
    </ul>

    <div class="highlight">
        <strong>Custom Products:</strong> For non-catalog items (shipping, discounts), the plugin creates generic products in Odoo using item name and price from Shopware with default tax and category settings.
    </div>

    <h2>7. API Endpoints</h2>

    <h3>Verification Endpoint</h3>
    <table>
        <tr>
            <th>Property</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>URL</td>
            <td>/api/bs-odoo/verify</td>
        </tr>
        <tr>
            <td>Method</td>
            <td>POST</td>
        </tr>
        <tr>
            <td>Purpose</td>
            <td>Test Odoo connection and authentication</td>
        </tr>
    </table>

    <h4>Request Parameters</h4>
    <ul>
        <li>BsOdoo.config.serverUrl</li>
        <li>BsOdoo.config.database</li>
        <li>BsOdoo.config.username</li>
        <li>BsOdoo.config.password</li>
    </ul>

    <h4>Response Format</h4>
    <div class="code-block">
{
    "success": true,
    "message": "success",
    "uid": 123
}
    </div>

    <h2>8. Database Schema</h2>

    <h3>Migration Details</h3>
    <table>
        <tr>
            <th>Property</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>File</td>
            <td>Migration1754291203BsOdoo.php</td>
        </tr>
        <tr>
            <td>Timestamp</td>
            <td>1754291203</td>
        </tr>
        <tr>
            <td>Purpose</td>
            <td>Create mapping table and indexes</td>
        </tr>
    </table>

    <h3>Table Structure</h3>
    <div class="code-block">
CREATE TABLE `bs_odoo` (
    `id` BINARY(16) NOT NULL,
    `record_id` BINARY(16) NOT NULL,    -- Shopware entity ID
    `odoo_id` VARCHAR(36) NOT NULL,     -- Odoo record ID
    `module` VARCHAR(20) NOT NULL,      -- Entity type
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3),
    PRIMARY KEY (`id`),
    INDEX `idx_record_module` (`record_id`, `module`),
    INDEX `idx_odoo_module` (`odoo_id`, `module`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </div>

    <h2>9. Troubleshooting</h2>

    <h3>Common Issues</h3>

    <div class="warning">
        <h4>Connection Failed</h4>
        <ul>
            <li>Verify Odoo server URL is accessible</li>
            <li>Check firewall settings</li>
            <li>Ensure XML-RPC is enabled in Odoo</li>
        </ul>
    </div>

    <div class="warning">
        <h4>Authentication Failed</h4>
        <ul>
            <li>Verify username and password/API key</li>
            <li>Check user permissions in Odoo</li>
            <li>Ensure database name is correct</li>
        </ul>
    </div>

    <div class="warning">
        <h4>Sync Failures</h4>
        <ul>
            <li>Check plugin logs in <code>var/log/bs_odoo_logs.log</code></li>
            <li>Verify required Odoo modules are installed</li>
            <li>Check data integrity and required fields</li>
        </ul>
    </div>

    <h3>Log Files</h3>
    <table>
        <tr>
            <th>Property</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>Location</td>
            <td>var/log/bs_odoo_logs.log</td>
        </tr>
        <tr>
            <td>Rotation</td>
            <td>Daily rotation enabled</td>
        </tr>
        <tr>
            <td>Levels</td>
            <td>Error, Warning, Info, Debug</td>
        </tr>
    </table>

    <h2>10. Support</h2>

    <h3>Developer Information</h3>
    <table>
        <tr>
            <th>Property</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>Company</td>
            <td>BrainStream Technolabs</td>
        </tr>
        <tr>
            <td>Website</td>
            <td>https://brainstreamtechnolabs.com</td>
        </tr>
        <tr>
            <td>Email</td>
            <td><EMAIL></td>
        </tr>
        <tr>
            <td>Author</td>
            <td>Bhavesh</td>
        </tr>
    </table>

    <h3>Documentation Features</h3>
    <ul>
        <li>Plugin source code includes comprehensive PHPDoc comments</li>
        <li>Configuration XML provides field descriptions</li>
        <li>Service classes implement clear interfaces</li>
        <li>Message queue integration for asynchronous processing</li>
        <li>Comprehensive logging system</li>
    </ul>

    <h3>Version Information</h3>
    <table>
        <tr>
            <th>Property</th>
            <th>Value</th>
        </tr>
        <tr>
            <td>Current Version</td>
            <td>1.0.0</td>
        </tr>
        <tr>
            <td>Shopware Compatibility</td>
            <td>6.4.*</td>
        </tr>
        <tr>
            <td>License</td>
            <td>MIT License</td>
        </tr>
        <tr>
            <td>Plugin Class</td>
            <td>Bs\Odoo\BsOdoo</td>
        </tr>
    </table>

    <div class="footer">
        <p><em>This documentation covers the complete setup and usage of the BsOdoo plugin for Shopware 6.</em></p>
        <p><em>For additional support or custom requirements, please contact BrainStream Technolabs.</em></p>
        <p><strong>Document Version:</strong> 1.0 | <strong>Last Updated:</strong> January 2025</p>
    </div>
</body>
</html>
