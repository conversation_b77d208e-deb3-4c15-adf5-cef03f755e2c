<?php declare(strict_types=1);

namespace Shopware\Core\Framework\Test\TestCaseHelper;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Event\WorkerRunningEvent;

/**
 * @internal
 */
class StopWorkerWhenIdleListener implements EventSubscriberInterface
{
    public static function getSubscribedEvents()
    {
        return [
            WorkerRunningEvent::class => 'stopWorkerWhenIdle',
        ];
    }

    public function stopWorkerWhenIdle(WorkerRunningEvent $event): void
    {
        if ($event->isWorkerIdle()) {
            $event->getWorker()->stop();
        }
    }
}
