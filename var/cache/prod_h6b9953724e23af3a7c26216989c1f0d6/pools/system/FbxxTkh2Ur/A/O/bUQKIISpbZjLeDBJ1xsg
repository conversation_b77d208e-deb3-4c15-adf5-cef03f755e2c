<?php //Shopware%255CCore%255CFramework%255CApi%255CController%255CAuthController%2523token

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Shopware\\Core\\Framework\\Routing\\Annotation\\Since'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Shopware\\Core\\Framework\\Routing\\Annotation\\Since')),
        clone ($p['Symfony\\Component\\Routing\\Annotation\\Route'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Symfony\\Component\\Routing\\Annotation\\Route')),
    ],
    null,
    [
        'Shopware\\Core\\Framework\\Routing\\Annotation\\Since' => [
            'value' => [
                '6.0.0.0',
            ],
        ],
        'Symfony\\Component\\Routing\\Annotation\\Route' => [
            'path' => [
                1 => '/api/oauth/token',
            ],
            'name' => [
                1 => 'api.oauth.token',
            ],
            'defaults' => [
                1 => [
                    'auth_required' => false,
                ],
            ],
            'methods' => [
                1 => [
                    'POST',
                ],
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
