{% block swag_paypal_content_card_channel_config_credentials %}
    <sw-card position-identifier="swag-paypal-credentials"
             :title="$tc('swag-paypal.settingForm.credentials.cardTitle')">

        {% block swag_paypal_content_card_channel_config_credentials_card_container %}
            <sw-container>

                {% block swag_paypal_content_card_channel_config_credentials_card_container_settings %}
                    <div v-if="actualConfigData" class="swag-paypal-settings-credentials-fields">

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_client_id %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.clientId']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.clientId']"
                                                :customInheritationCheckFunction="checkTextFieldInheritance">
                                <template #content="props">
                                    <sw-text-field name="SwagPayPal.settings.clientId"
                                                   :mapInheritance="props"
                                                   :label="$tc('swag-paypal.settingForm.credentials.clientId.label')"
                                                   :helpText="$tc('swag-paypal.settingForm.credentials.clientId.tooltipText')"
                                                   :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                   :value="props.currentValue"
                                                   :error="clientIdErrorState"
                                                   @change="props.updateCurrentValue">
                                    </sw-text-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_client_secret %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.clientSecret']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.clientSecret']"
                                                :customInheritationCheckFunction="checkTextFieldInheritance">
                                <template #content="props">
                                    <sw-text-field name="SwagPayPal.settings.clientSecret"
                                                   :mapInheritance="props"
                                                   :label="$tc('swag-paypal.settingForm.credentials.clientSecret.label')"
                                                   :helpText="$tc('swag-paypal.settingForm.credentials.clientSecret.tooltipText')"
                                                   :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                   :value="props.currentValue"
                                                   :error="clientSecretErrorState"
                                                   @change="props.updateCurrentValue">
                                    </sw-text-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_merchant_id %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.merchantPayerId']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.merchantPayerId']"
                                                :customInheritationCheckFunction="checkTextFieldInheritance">
                                <template #content="props">
                                    <sw-text-field name="SwagPayPal.settings.merchantPayerId"
                                                   :mapInheritance="props"
                                                   :label="$tc('swag-paypal.settingForm.credentials.merchantPayerId.label')"
                                                   :helpText="$tc('swag-paypal.settingForm.credentials.merchantPayerId.tooltipText')"
                                                   :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                   :value="props.currentValue"
                                                   @change="props.updateCurrentValue">
                                    </sw-text-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_actions_test %}
                            <sw-button-process v-model="isTestLiveSuccessful"
                                               class="swag-paypal-settings-credentials-fields__test-button"
                                               :isLoading="isTestingLive"
                                               :disabled="testLiveButtonDisabled"
                                               @click="onTest(false)">
                                {{ $tc('swag-paypal.settingForm.testLive') }}
                            </sw-button-process>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_sandbox %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.sandbox']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.sandbox']"
                                                :customInheritationCheckFunction="checkBoolFieldInheritance">
                                <template #content="props">
                                    <sw-switch-field name="SwagPayPal.settings.sandbox"
                                                     :mapInheritance="props"
                                                     :label="$tc('swag-paypal.settingForm.credentials.sandbox.label')"
                                                     :helpText="$tc('swag-paypal.settingForm.credentials.sandbox.tooltipText')"
                                                     :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                     :value="props.currentValue"
                                                     @change="props.updateCurrentValue">
                                    </sw-switch-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_client_id_sandbox %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.clientIdSandbox']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.clientIdSandbox']"
                                                :customInheritationCheckFunction="checkTextFieldInheritance">
                                <template #content="props">
                                    <sw-text-field name="SwagPayPal.settings.clientIdSandbox"
                                                   :mapInheritance="props"
                                                   :label="$tc('swag-paypal.settingForm.credentials.clientIdSandbox.label')"
                                                   :helpText="$tc('swag-paypal.settingForm.credentials.clientIdSandbox.tooltipText')"
                                                   :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                   :value="props.currentValue"
                                                   :error="clientIdSandboxErrorState"
                                                   @change="props.updateCurrentValue">
                                    </sw-text-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_client_secret_sandbox %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.clientSecretSandbox']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.clientSecretSandbox']"
                                                :customInheritationCheckFunction="checkTextFieldInheritance">
                                <template #content="props">
                                    <sw-text-field name="SwagPayPal.settings.clientSecretSandbox"
                                                   :mapInheritance="props"
                                                   :label="$tc('swag-paypal.settingForm.credentials.clientSecretSandbox.label')"
                                                   :helpText="$tc('swag-paypal.settingForm.credentials.clientSecretSandbox.tooltipText')"
                                                   :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                   :value="props.currentValue"
                                                   :error="clientSecretSandboxErrorState"
                                                   @change="props.updateCurrentValue">
                                    </sw-text-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_merchant_id_sandbox %}
                            <sw-inherit-wrapper v-model="actualConfigData['SwagPayPal.settings.merchantPayerIdSandbox']"
                                                :inheritedValue="selectedSalesChannelId === null ? null : allConfigs['null']['SwagPayPal.settings.merchantPayerIdSandbox']"
                                                :customInheritationCheckFunction="checkTextFieldInheritance">
                                <template #content="props">
                                    <sw-text-field name="SwagPayPal.settings.merchantPayerIdSandbox"
                                                   :mapInheritance="props"
                                                   :label="$tc('swag-paypal.settingForm.credentials.merchantPayerIdSandbox.label')"
                                                   :helpText="$tc('swag-paypal.settingForm.credentials.merchantPayerIdSandbox.tooltipText')"
                                                   :disabled="props.isInherited || !acl.can('swag_paypal.editor')"
                                                   :value="props.currentValue"
                                                   @change="props.updateCurrentValue">
                                    </sw-text-field>
                                </template>
                            </sw-inherit-wrapper>
                        {% endblock %}

                        {% block swag_paypal_content_card_channel_config_credentials_card_container_settings_actions_test_sandbox %}
                            <sw-button-process v-model="isTestSandboxSuccessful"
                                               :isLoading="isTestingSandbox"
                                               :disabled="testSandboxButtonDisabled"
                                               @click="onTest(true)">
                                {{ $tc('swag-paypal.settingForm.testSandbox') }}
                            </sw-button-process>
                        {% endblock %}
                    </div>
                {% endblock %}
            </sw-container>
        {% endblock %}
    </sw-card>
{% endblock %}
