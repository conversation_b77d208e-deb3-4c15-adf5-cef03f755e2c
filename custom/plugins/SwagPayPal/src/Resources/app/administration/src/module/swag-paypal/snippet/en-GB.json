{"locale": {"en-AU": "English (AU)"}, "sw-privileges": {"permissions": {"parents": {"swag_paypal": "PayPal"}, "swag_paypal": {"label": "PayPal settings"}, "swag_paypal_disputes": {"label": "PayPal disputes"}}}, "sw-settings-payment-list": {"needOnboardingTooltip": "Complete the onboarding to activate this payment method"}, "sw-settings-payment-detail": {"tooltip": "Go to the <a class=\"swag-paypal-go-to-settings-link\" href=\"#/swag/paypal/index\">PayPal extension settings →</a> and activate PayPal Commerce Platform to activate this payment method."}, "swag-paypal": {"header": "PayPal", "saleschannelCard": {"button": {"description": "Click this button to enable PayPal and select it as default in the selected Sales Channel", "label": "Set PayPal as default"}}, "tabs": {"general": "General", "storefront": "Storefront presentation"}, "general": {"mainMenuItemGeneral": "PayPal", "descriptionTextModule": "PayPal settings"}, "webhook": {"cardTitle": "Webhook", "buttonRefresh": "Refresh webhook", "refreshFailed": {"title": "Failed to refresh", "errorUnknown": "Unknown error"}, "status": {"unknown": "Loading...", "missing": "Not registered", "invalid": "Registered, but possible domain mismatch", "valid": "Registered"}, "infoText": "The webhook is used for updating the status of a transaction asynchronously. Incorrectly registered webhooks, e.g. on a different or not publicly available server, may result in unconfirmed transactions, regardless whether the transaction has been processed by PayPal."}, "cross-border": {"cardTitle": "Pay Later cross-border messaging", "infoText": "Pay Later messaging is a feature that allows you to display a Pay Later message in the language of the customer. This feature is only available for certain countries. {0}", "warningText": "Please contact your PayPal representative to enable this feature for your account.", "crossBorderBuyerCountryOverride": "Localization", "crossBorderBuyerCountryAuto": "Automatic determination", "crossBorderMessagingEnable": "Enable cross-border localization of Pay Later message"}, "messageNotBlank": "This value should not be blank.", "settingForm": {"testLive": "Test API credentials", "testSandbox": "Test sandbox API credentials", "checkout": {"cardTitle": "PayPal Checkout", "settingsLink": "Settings", "header": "More payment methods with one solution from PayPal", "description": "PayPal Checkout is the new all-in-one solution that lets you offer powerful flexibile payment processing features on the marketplaces and other commerce platforms.", "banner": "With PayPal Checkout, you can offer your customers payment by invoice, credit card and other local payment methods. PayPal Checkout supports you with the latest technology and brings you the highest flexibility. You keep your existing payment methods, and the fees remain the same! Activate PayPal Checkout and deactivate PayPal PLUS to avoid duplicate payment methods in your shop. <a href=\"https://www.shopware.com/en/news/paypal-launches-new-product/?utm_source=product&utm_medium=sw6-onprem&utm_campaign=paypal-checkout&utm_content=link-en-blog\" target=\"_blank\">More information</a>", "button": {"liveTitle": "Connect PayPal account", "sandboxTitle": "Connect sandbox account", "changeLiveTitle": "Connect different account", "changeSandboxTitle": "Connect different sandbox account", "onboardingLiveTitle": "Start PayPal Checkout onboarding", "onboardingSandboxTitle": "Start PayPal Checkout sandbox onboarding", "restartOnboardingLiveTitle": "Restart PayPal Checkout onboarding", "restartOnboardingSandboxTitle": "Restart PayPal Checkout sandbox onboarding"}, "sandbox": {"label": "Sandbox", "onlySandboxTooltip": "You are only connected a sandbox PayPal account so far.", "onlyLiveTooltip": "You are only connected a live PayPal account so far.", "helpText": "Enable, if you want to test the PayPal integration."}, "switch": {"label": "Active"}, "paymentMethodText": "Payment methods", "showCredentials": "Show credentials", "appImageAlt": "PayPal", "editDetail": "Edit details", "startOnboardingButtonLabel": "Start onboarding", "availabilityToolTip": {"bancontactapmhandler": "Bancontact is available for the following countries: Belgium", "blikapmhandler": "BLIK is available for the following countries: Poland", "boletobancarioapmhandler": "Boleto Bancário is available for the following countries: Brazil", "epsapmhandler": "eps is available for the following countries: Austria", "idealapmhandler": "iDEAL is available for the following countries: Netherlands", "multibancoapmhandler": "Multibanco is available for the following countries: Portugal", "mybankapmhandler": "MyBank is available for the following countries: Italy", "oxxoapmhandler": "OXXO is available for the following countries: Mexico", "p24apmhandler": "Przelewy24 is available for the following countries: Poland", "paylaterhandler": "Pay Later is available for the following countries: Australia, France, Germany, Italy, Spain, United Kingdom, United States", "puihandler": "Pay upon invoice is available for the following countries: Germany", "sepahandler": "SEPA Lastschrift is available for the following countries: Germany", "venmohandler": "Venmo is available for the following countries: United States", "trustlyapmhandler": "Trustly is available for the following countries: Estonia, Finland, Netherlands, Sweden"}, "onboardingStatusText": {"active": "Authorized", "limited": "Limited", "pending": "Authorization pending", "ineligible": "Ineligible", "inactive": "Onboarding needed", "mybank": "Limited"}, "onboardingStatusTooltip": {"ineligible": "PayPal informed us, that this payment method is currently ineligible for your account.", "limited": "PayPal informed us, that this payment method has some limitations for your account.", "mybank": "Merchants enabling MyBank after February 2023 will need manual approval by PayPal. Reach out to PayPal support for further information on this."}, "paymentMethodStatusChangedSuccess": {"active": "Payment method \"{name}\" is now active.", "inactive": "Payment method \"{name}\" is now inactive."}, "deactivatePayPalPlusModal": {"title": "Deactivate PayPal PLUS", "text": "Congratulations! By switching to PayPal Checkout, you keep on using all relevant payment methods – now based on the latest technology.", "warning": "Avoid displaying duplicate payment methods and deactivate PayPal PLUS.", "button": {"deactivate": "Deactivate PayPal PLUS", "cancel": "Cancel"}}}, "credentials": {"cardTitle": "API credentials", "clientId": {"label": "Client ID", "tooltipText": "The REST API client ID is used to authenticate this plugin with the PayPal API."}, "clientSecret": {"label": "Client secret", "tooltipText": "The REST API client secret is used to authenticate this plugin with the PayPal API."}, "merchantPayerId": {"label": "PayPal Merchant ID", "tooltipText": "The PayPal Merchant ID assigned to your PayPal account (see Business Information in PayPal account settings)."}, "clientIdSandbox": {"label": "Sandbox client ID", "tooltipText": "The REST API client ID is used while testing to authenticate this plugin with the PayPal API."}, "clientSecretSandbox": {"label": "Sandbox client secret", "tooltipText": "The REST API client secret is used while testing to authenticate this plugin with the PayPal API."}, "merchantPayerIdSandbox": {"label": "Sandbox PayPal Merchant ID", "tooltipText": "The PayPal Merchant ID assigned to your PayPal sandbox account (see Business Information in PayPal account settings)."}, "sandbox": {"label": "Enable sandbox", "tooltipText": "Enable, if you want to test the PayPal integration."}, "button": {"title": "Get API credentials", "titleSandbox": "Get sandbox API credentials", "messageFetchedError": "Try again or enter your credentials manually."}}, "behavior": {"cardTitle": "Behaviour", "merchantLocation": {"label": "Merchant location", "germany": "Germany", "other": "Other merchant location"}, "intent": {"label": "Payment acquisition", "CAPTURE": "Automatic payment collection (intent CAPTURE)", "AUTHORIZE": "Manual payment collection (intent AUTHORIZE)"}, "submitCart": {"label": "Submit cart", "helpText": "If this option is active, cart data will be submitted to PayPal at checkout."}, "brandName": {"label": "Your own brand name on PayPal page", "tooltipText": "This text will be displayed as the brand name on the PayPal payment page."}, "landingPage": {"label": "PayPal landing page", "helpText": {"login": "Login: The PayPal site displays a login screen as landing page.", "billing": "Registration: The PayPal site displays a registration form as landing page.", "no_preference": "No preference: PayPal decides which page is shown, depending on the previous interaction of the customer with PayPal."}, "options": {"login": "<PERSON><PERSON>", "billing": "Registration", "no_preference": "No preference"}}, "sendOrderNumber": {"label": "Submit order number", "helpText": "If this option is active, the order number will be submitted to PayPal as invoice ID at checkout."}, "orderNumberPrefix": {"label": "Order number prefix", "tooltipText": "This text is placed before the original order number (e.g MyShop_SW20001). This helps to identify the shop where the payment was made. You can find it as invoice ID in your PayPal dashboard."}, "orderNumberSuffix": {"label": "Order number suffix", "tooltipText": "This text is placed after the original order number (e.g SW20001_MyShop). This helps to identify the shop where the payment was made. You can find it as invoice ID in your PayPal dashboard."}, "loggingLevel": {"label": "Debugging", "tooltipText": "When using advanced debugging, additional data is recorded during checkout to detect potential errors. For temporary use only.", "options": {"basic": "Basic", "advanced": "Advanced"}}, "excludedProductIds": {"label": "Excluded products", "tooltipText": "Products selected here cannot be purchased with PayPal."}, "excludedProductStreamIds": {"label": "Excluded dynamic product groups", "tooltipText": "Products included in the dynamic product groups selected here cannot be purchased with PayPal."}}, "express": {"cardTitle": "Express Checkout Shortcut", "cardSubtitle": "Express Checkouts increase the conversion rate in your shop and pose no financial risk to you as a merchant. It is recommended to keep them activated. ", "ecsDetailEnabled": {"label": "'PayPal Checkout' on detail page", "helpText": "If this option is active, the Express Checkout button will be shown on each product detail page."}, "ecsCartEnabled": {"label": "'PayPal Checkout' on cart", "helpText": "If this option is active, the Express Checkout button will be shown on the cart."}, "ecsOffCanvasEnabled": {"label": "'PayPal Checkout' on off-canvas cart", "helpText": "If this option is active, the Express Checkout button will be shown on the off-canvas cart."}, "ecsLoginEnabled": {"label": "'PayPal Checkout' on login page", "helpText": "If this option is active, the Express Checkout button will be shown on the login and register page."}, "ecsListingEnabled": {"label": "'PayPal Checkout' on listing pages", "helpText": "If this option is active, the Express Checkout button will be shown on listing pages."}, "ecsButtonColor": {"label": "Button color", "options": {"blue": "Blue", "black": "Black", "gold": "Gold (recommended)", "silver": "Silver", "white": "White"}}, "ecsButtonShape": {"label": "Button shape", "options": {"pill": "Round", "rect": "Rectangular"}}, "ecsSubmitCart": {"label": "Submit cart", "helpText": "If this option is active, the cart will be submitted to PayPal for Express orders."}, "ecsButtonLanguageIso": {"label": "Button locale", "helpText": "If not set, the sales channel language will be used."}, "ecsShowPayLater": {"label": "Display 'Pay Later' button next to the 'PayPal Checkout' button", "helpText": "The 'Pay Later' button will be displayed on the same pages and in the same design as the 'PayPal Checkout' button."}}, "installment": {"cardTitle": "'Pay Later' banner", "cardSubtitle": "Banners increase the conversion rate in your shop and pose no financial risk to you as a merchant. It is recommended to keep them activated.", "installmentBannerEnabled": {"detailPage": {"label": "'Pay Later' banner on detail page", "helpText": "If this option is active, the 'Pay Later' banner will be shown on each product detail page."}, "cart": {"label": "'Pay Later' banner on cart", "helpText": "If this option is active, the 'Pay Later' banner will be shown on the cart."}, "offCanvasCart": {"label": "'Pay Later' banner on off-canvas cart", "helpText": "If this option is active, the 'Pay Later' banner will be shown on the off-canvas cart."}, "loginPage": {"label": "'Pay Later' banner on login page", "helpText": "If this option is active, the 'Pay Later' banner will be shown on the login and register page."}, "footer": {"label": "'Pay Later' banner on footer", "helpText": "If this option is active, the 'Pay Later' banner will be shown on the footer."}}}, "acdc": {"cardTitle": "Credit- or debit card", "acdcForce3DS": {"label": "Block payments from non-3DS countries", "helpText": "PayPal checks whether 3DS (Strong Customer Authentication) is required based on the credit or debit card presented. Setting this option will reject payment attempts without a 3DS check."}}, "pui": {"cardTitle": "Pay upon invoice", "customerServiceInstructions": {"label": "Customer service instructions for Pay upon invoice", "helpText": "These instructions will be submitted to PayPal & RatePay and shown in emails for the customer."}}, "spb": {"cardTitle": "Smart Payment Buttons", "spbCheckoutEnabled": {"label": "Enable Smart Payment Buttons"}, "spbButtonLanguageIso": {"label": "Button locale", "helpText": "If not set, the sales channel language will be used."}, "spbAlternativePaymentMethodsEnabled": {"label": "Enable the alternative payment methods for the Smart Payment Buttons.", "helpText": "Alternative payment methods are credit- and debit cards and more."}, "spbShowPayLater": {"label": "Display 'Pay Later' button next to PayPal button"}}, "plus": {"cardTitle": "PayPal PLUS", "introduction": "<strong>PayPal PLUS:</strong> Your complete all-in-one payment solution including PayPal, Direct Debit and Credit Card. PayPal PLUS offers your customers full flexibility in choosing their preferred payment method. Your advantages as a seller? Easier accounting with only one contracting party, one account and one transaction overview showing all incoming payments. You receive your money directly after customers complete the order process.<br>You can apply for PayPal PLUS <a href='https://www.paypal.com/de/webapps/mpp/paypal-plus' target='_blank'>here</a>.", "plusCheckoutEnabled": {"label": "Enable PayPal PLUS"}, "hint": "Note: PayPal PLUS only works with the payment acquisition \"Sale\". It will be set automatically.", "warning": {"active": "PayPal Checkout provides the same payment methods to your customers – based on the latest technology. Activate PayPal Checkout now and deactivate PayPal PLUS to avoid displaying duplicate payment methods. <a href=\"https://www.shopware.com/en/news/paypal-launches-new-product/?utm_source=product&utm_medium=sw6-onprem&utm_campaign=paypal-checkout&utm_content=link-en-blog\" target=\"_blank\">More information</a>", "inactive": "Avoid displaying duplicate payment methods and deactivate PayPal PLUS. <a href=\"https://www.shopware.com/en/news/paypal-launches-new-product/?utm_source=product&utm_medium=sw6-onprem&utm_campaign=paypal-checkout&utm_content=link-en-blog\" target=\"_blank\">More information</a>"}}, "locale-field": {"infobox": {"text": "To see a list of valid locale codes click ", "href": "here"}, "error": {"detail": "The locale code you provided doesn't match the format required by PayPal. A valid format is \"en_GB\"."}}, "messageSaveSuccess": "The PayPal settings have been saved.", "messageTestSuccess": "The API credentials have been validated.", "messageTestError": "The API credentials could not be validated.", "messageWebhookCreated": "The webhook has been registered.", "messageWebhookUpdated": "The webhook has been updated.", "messageWebhookError": "The webhook could not be saved. The following errors occurred:"}}}