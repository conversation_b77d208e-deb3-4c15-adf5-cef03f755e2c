<?php //Shopware%255CCore%255CFramework%255CApi%255CController%255CCacheController

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Symfony\\Component\\Routing\\Annotation\\Route'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Symfony\\Component\\Routing\\Annotation\\Route')),
    ],
    null,
    [
        'Symfony\\Component\\Routing\\Annotation\\Route' => [
            'defaults' => [
                [
                    '_routeScope' => [
                        'api',
                    ],
                ],
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
