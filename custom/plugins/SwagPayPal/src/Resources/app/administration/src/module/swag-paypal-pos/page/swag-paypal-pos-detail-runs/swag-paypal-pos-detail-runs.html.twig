{% block swag_paypal_pos_detail_runs %}
    <div class="swag-paypal-pos-detail-runs">

        {% block swag_paypal_pos_detail_runs_card %}
            <sw-card position-identifier="swag-paypal-pos-detail-runs"
                     :title="$tc('swag-paypal-pos.detail.runs.title')"
                     :isLoading="isLoading">

                {% block swag_paypal_pos_detail_runs_card_grid %}
                    <template #grid>

                        <sw-data-grid v-if="total > 0"
                                      identifier="swag-paypal-pos-detail-runs"
                                      :columns="columns"
                                      :dataSource="runs"
                                      :isLoading="isLoading || total === 0"
                                      :showActions="true"
                                      :showSelection="false"
                                      :showSettings="true"
                                      :allowColumnEdit="true"
                                      :sortBy="sortBy"
                                      :sortDirection="sortDirection"
                                      :skeletonItemAmount="limit"
                                      @column-sort="onSortColumn">

                            {% block swag_paypal_pos_detail_runs_card_grid_pagination %}
                                <template #pagination>
                                    <sw-pagination :limit="limit"
                                                   :page="page"
                                                   :total="total"
                                                   :total-visible="7"
                                                   @page-change="onPageChange">
                                    </sw-pagination>
                                </template>
                            {% endblock %}

                            {% block swag_paypal_pos_detail_runs_other_grid_column_log %}
                                <template #column-task="{ item }">
                                    {{ $tc(`swag-paypal-pos.detail.runs.${item.task}Task`) }}
                                </template>
                            {% endblock %}

                            {% block swag_paypal_pos_detail_runs_card_grid_column_state %}
                                <template #column-state="{ item }">

                                    {% block swag_paypal_pos_detail_runs_card_grid_column_state_label %}
                                        <sw-label appearance="pill"
                                                  :variant="getLabelVariant(item)">
                                            {{ $tc(getLabel(item)) }}
                                        </sw-label>
                                    {% endblock %}

                                </template>
                            {% endblock %}

                            {% block swag_paypal_pos_detail_runs_card_grid_column_date %}
                                <template #column-date="{ item }">

                                    {% block swag_paypal_pos_detail_runs_card_grid_column_date_formatted %}
                                        {{ item.finishedAt | date({
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            day: '2-digit',
                                            month: '2-digit',
                                            year: '2-digit'
                                        }) }}
                                    {% endblock %}

                                </template>
                            {% endblock %}

                            {% block swag_paypal_pos_detail_runs_card_grid_actions %}
                                <template #actions="{ item }">

                                    {% block swag_paypal_pos_detail_runs_card_grid_actions_details %}
                                        <sw-context-menu-item :disabled="item.logs.length <= 0"
                                                              @click="onShowModal(item.id)">
                                            {{ $tc('swag-paypal-pos.detail.runs.actions.showDetails') }}
                                        </sw-context-menu-item>
                                    {% endblock %}

                                </template>
                            {% endblock %}

                        </sw-data-grid>
                    </template>
                {% endblock %}

                {% block swag_paypal_pos_detail_runs_card_empty_state %}
                    <template #default>
                        <sw-empty-state v-if="total < 1"
                                        class="swag-paypal-pos-detail-runs__empty-state"
                                        :title="$tc('swag-paypal-pos.detail.runs.emptyState.title')"
                                        :subline="$tc('swag-paypal-pos.detail.runs.emptyState.message')">
                        </sw-empty-state>
                    </template>
                {% endblock %}

                {% block swag_paypal_pos_detail_runs_card_toolbar %}
                    <template #toolbar v-if="total > 0">

                        {% block swag_paypal_pos_detail_runs_card_toolbar_container %}
                            <sw-container columns="1fr min-content"
                                          align="center">

                                {% block swag_paypal_pos_detail_runs_card_toolbar_container_text %}
                                    <p>{{ $tc('swag-paypal-pos.detail.runs.toolbarText') }}</p>
                                {% endblock %}

                                {% block swag_paypal_pos_detail_runs_card_toolbar_container_button %}
                                    <sw-button
                                            :disabled="!acl.can('sales_channel.editor')"
                                            @click="onClearLogs">
                                        {{ $tc('swag-paypal-pos.detail.cleanLog') }}
                                    </sw-button>
                                {% endblock %}

                            </sw-container>
                        {% endblock %}

                    </template>
                {% endblock %}

            </sw-card>
        {% endblock %}

        {% block swag_paypal_pos_detail_runs_log_modal %}
            <sw-modal v-if="showModal"
                      class="swag-paypal-pos-detail-runs__modal"
                      :title="$tc('swag-paypal-pos.detail.runs.title')"
                      :isLoading="isLoading"
                      variant="large"
                      @modal-close="onCloseModal">

                {% block swag_paypal_pos_detail_runs_log_modal_logs %}
                    <swag-paypal-pos-detail-logs :runId="currentRunId"></swag-paypal-pos-detail-logs>
                {% endblock %}

                {% block swag_paypal_pos_detail_runs_log_modal_footer %}
                    <template #modal-footer>

                        {% block swag_paypal_pos_detail_runs_log_modal_footer_close_modal_button %}
                            <sw-button variant="primary"
                                       @click="onCloseModal">
                                {{ $tc('global.default.cancel') }}
                            </sw-button>
                        {% endblock %}

                    </template>
                {% endblock %}

            </sw-modal>
        {% endblock %}

    </div>
{% endblock %}
