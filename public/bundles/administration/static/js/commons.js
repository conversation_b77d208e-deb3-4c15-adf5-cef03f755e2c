/*! For license information please see commons.js.LICENSE.txt */
(this.webpackJsonpAdministration=this.webpackJsonpAdministration||[]).push([["commons"],{"+ZSk":function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return b}));var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("2iSf");function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var b=function(t){l()(n,t);var e=m(n);function n(t,r){var i;return a()(this,n),(i=e.call(this,t,r,null,"application/json")).name="productStreamPreviewService",i}return s()(n,[{key:"preview",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.httpClient.post("_admin/product-stream-preview/".concat(t),y(y({},e),{filter:n}),{headers:this.getBasicHeaders(r)}).then((function(t){return v.a.handleResponse(t)}))}}]),n}(v.a)},0:function(t,e,n){t.exports=n("H1NO")},1:function(t,e){},"2aLV":function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=function(){function t(){i()(this,t),this.EntityDefinition=Shopware.EntityDefinition,this.ShopwareError=Shopware.Classes.ShopwareError,this.merge=Shopware.Utils.object.merge}return a()(t,[{key:"resetApiErrors",value:function(){return Shopware.State.dispatch("error/resetApiErrors")}},{key:"handleWriteErrors",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.errors,n=arguments.length>1?arguments[1]:void 0;if(!e)throw new Error("[error-resolver] handleWriteError was called without errors");var r=this.reduceErrorsByWriteIndex(e);this.handleErrors(r,n),this.addSystemErrors(r.system)}},{key:"handleDeleteError",value:function(t){var e=this;t.forEach((function(t){var n=t.error,r=t.entityName,i=t.id,o=new e.ShopwareError(n);Shopware.State.dispatch("error/addSystemError",{error:o}),Shopware.State.dispatch("error/addApiError",{expression:"".concat(r,".").concat(i),error:o})}))}},{key:"reduceErrorsByWriteIndex",value:function(t){var e=this,n={system:[]};return t.forEach((function(t){if(t.source&&t.source.pointer){var r=t.source.pointer.split("/");""===r[0]&&r.shift();var i={},o=r.length-1;r.reduce((function(n,i,a){return"translations"===i||"translations"===r[a-1]?n:(n[i]=a===o?new e.ShopwareError(t):{},n[i])}),i),n=e.merge(n,i)}else n.system.push(new e.ShopwareError(t))})),n}},{key:"addSystemErrors",value:function(t){t.forEach((function(t){Shopware.State.dispatch("error/addSystemError",t)}))}},{key:"handleErrors",value:function(t,e){var n=this;e.forEach((function(e,r){var i=e.entity,o=e.changes,a=t[r];if(a){var c=n.EntityDefinition.get(i.getEntityName());Object.keys(a).forEach((function(t){n.resolveError(t,a[t],c,i,o)}))}}))}},{key:"resolveError",value:function(t,e,n,r,i){var o=n.getField(t);if(o)if(n.isToManyAssociation(o)){var a=this.buildAssociationChangeset(r,i,e,t);this.handleErrors(e,a)}else n.isToOneAssociation(o)?this.resolveOneToOneFieldError("".concat(r.getEntityName(),".").concat(r.id,".").concat(t),e):n.isJsonField(o)?this.resolveJsonFieldError("".concat(r.getEntityName(),".").concat(r.id,".").concat(t),e):Shopware.State.dispatch("error/addApiError",{expression:this.getErrorPath(r,t),error:new this.ShopwareError(e)});else this.errorStore.addSystemError(e)}},{key:"buildAssociationChangeset",value:function(t,e,n,r){return e&&Shopware.Utils.object.hasOwnProperty(e,r)?e[r].map((function(e){return{entity:t[r].find((function(t){return t.id===e.id})),changes:e}})):(Shopware.State.dispatch("error/addApiError",{expression:this.getErrorPath(t,r),error:new this.ShopwareError(n)}),[])}},{key:"resolveJsonFieldError",value:function(t,e){var n=this;Object.keys(e).forEach((function(r){var i="".concat(t,".").concat(r);e[r]instanceof n.ShopwareError?Shopware.State.dispatch("error/addApiError",{expression:i,error:e[r]}):n.resolveJsonFieldError(i,e[r])}))}},{key:"resolveOneToOneFieldError",value:function(t,e){var n=this;Object.keys(e).forEach((function(r){var i="".concat(t,".").concat(r);e[r]instanceof n.ShopwareError&&Shopware.State.dispatch("error/addApiError",{expression:i,error:e[r]})}))}},{key:"getErrorPath",value:function(t,e){return"".concat(t.getEntityName(),".").concat(t.id,".").concat(e)}}]),t}()},"2iSf":function(t,e,n){"use strict";var r=n("cDf5"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("lSNA"),l=n.n(u),f=n("CkOj"),h=function(){function t(e,n,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/vnd.api+json";a()(this,t),l()(this,"client",{}),l()(this,"loginService",void 0),l()(this,"endpoint",""),l()(this,"type","application/vnd.api+json"),l()(this,"name",""),this.httpClient=e,this.loginService=n,this.apiEndpoint=r,this.contentType=i}return s()(t,[{key:"getApiBasePath",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n="";return null!=e&&e.length&&(n+="".concat(e,"/")),t&&"number"==typeof t||"string"==typeof t&&t.length>0?"".concat(n).concat(this.apiEndpoint,"/").concat(t):"".concat(n).concat(this.apiEndpoint)}},{key:"getBasicHeaders",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={Accept:this.contentType,Authorization:"Bearer ".concat(this.loginService.getToken()),"Content-Type":"application/json"};return Object.assign({},e,t)}},{key:"apiEndpoint",get:function(){return this.endpoint},set:function(t){this.endpoint=t}},{key:"httpClient",get:function(){return this.client},set:function(t){this.client=t}},{key:"contentType",get:function(){return this.type},set:function(t){this.type=t}}],[{key:"handleResponse",value:function(e){if(null===e.data||void 0===e.data)return e;var n=e.headers;return"object"===i()(n)&&null!==n&&"application/vnd.api+json"===n["content-type"]?t.parseJsonApiData(e.data):e.data}},{key:"parseJsonApiData",value:function(t){return Object(f.a)(t)}},{key:"getVersionHeader",value:function(t){return{"sw-version-id":t}}},{key:"makeQueryParams",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=Object.keys(t).filter((function(e){return"string"==typeof t[e]})).map((function(e){return"".concat(e,"=").concat(t[e])}));return e.length?"?".concat(e.join("&")):""}}]),t}();e.a=h},"3STq":function(t,e,n){var r={"./acl.api.service.js":"A45n","./app-action-button.service.js":"s5md","./app-cms-blocks.service.js":"ZnZ0","./app-modules.service.ts":"rh9H","./app-url-change.service.js":"Cv5K","./business-events.api.service.js":"8jby","./cache.api.service.js":"juyG","./calculate-price.api.service.js":"jVYu","./cart-store-api.api.service.ts":"szaz","./checkout-store.api.service.ts":"EL6e","./config.api.service.js":"Mt2o","./custom-snippet.api.service.ts":"n2ml","./customer-group-registration.api.service.js":"KpoE","./customer-validation.api.service.js":"wqx3","./document.api.service.js":"SnjH","./excludedSearchTerm.api.service.js":"p0GD","./extension-sdk.service.ts":"egkI","./first-run-wizard.api.service.js":"lnsc","./flow-actions.api.service.js":"in9x","./import-export.api.service.js":"JUxB","./integration.api.service.js":"tGtW","./known-ips.api.service.js":"Lz4t","./language-plugin.api.service.js":"J5bW","./mail.api.service.js":"umIt","./marketing.service.js":"UPBq","./media-folder.api.service.js":"RT+Y","./media.api.service.js":"SWCa","./message-queue.api.service.js":"m9g7","./notifications.service.js":"NOZM","./number-range.api.service.js":"T+rS","./order-document.api.service.js":"XwGQ","./order-state-machine.api.service.ts":"doAR","./order.api.service.js":"cNT7","./product-export.api.service.js":"A0sK","./product-stream-preview.service.js":"+ZSk","./promotion-sync.api.service.js":"lWuk","./recommendations.api.service.js":"vVie","./rule-conditions-config.api.service.js":"4C/Z","./sales-channel.api.service.js":"OUny","./scheduled-task.api.service.js":"earh","./search.api.service.js":"JMVm","./seo-url-template.api.service.js":"OW7U","./seo-url.api.service.js":"iXTH","./snippet-set.api.service.js":"7g5+","./snippet.api.service.js":"tTBJ","./state-machine.api.service.js":"gNKo","./store-context.api.service.ts":"7t4C","./store.api.service.ts":"IXFE","./sync.api.service.js":"6CVo","./system-config.api.service.js":"hFIz","./tag.api.service.js":"D7Q7","./update.api.service.js":"fLuy","./user-activity.service.js":"C0V+","./user-config.api.service.js":"wjWR","./user-input-sanitize.service.js":"AXza","./user-recovery.api.service.js":"yoR4","./user-validation.api.service.js":"cPrm","./user.api.service.js":"uaRI"};function i(t){var e=o(t);return n(e)}function o(t){if(!n.o(r,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return r[t]}i.keys=function(){return Object.keys(r)},i.resolve=o,t.exports=i,i.id="3STq"},"4C/Z":function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return v}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o;return i()(this,n),(o=e.call(this,t,r,null,"application/json")).name="ruleConditionsConfigApiService",o}return a()(n,[{key:"load",value:function(){return null!==Shopware.State.getters["ruleConditionsConfig/getConfig"]()?Promise.resolve():this.httpClient.get("_info/rule-config",{headers:this.getBasicHeaders()}).then((function(t){Shopware.State.commit("ruleConditionsConfig/setConfig",p.a.handleResponse(t))}))}}]),n}(p.a)},"6CVo":function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"sync";return i()(this,n),(o=e.call(this,t,r,a)).name="syncService",o}return a()(n,[{key:"sync",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=e,i=this.getBasicHeaders(n);return this.httpClient.post("/_action/".concat(this.apiEndpoint),t,{params:r,headers:i}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},"7g5+":function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p);function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function y(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var m=Shopware.Classes.ApiService,b=function(t){l()(n,t);var e=y(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"snippet-set";return a()(this,n),(i=e.call(this,t,r,o)).name="snippetSetService",i}return s()(n,[{key:"getCustomList",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=this.getBasicHeaders(),o={sortBy:"id",sortDirection:"ASC"};return r=g(g({},o),r),this.httpClient.post("/_action/".concat(this.getApiBasePath()),{page:t,limit:e,filters:n,sort:r},{headers:i}).then((function(t){return m.handleResponse(t)}))}},{key:"getBaseFiles",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/baseFile"),{params:{},headers:t}).then((function(t){return m.handleResponse(t)}))}},{key:"getAuthors",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/author"),{headers:t}).then((function(t){return m.handleResponse(t)}))}}]),n}(m);e.default=b},"7t4C":function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p);function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function y(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var m=function(t){l()(n,t);var e=y(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"sales-channel-context";return a()(this,n),(i=e.call(this,t,r,o,"application/json")).name="contextStoreService",i}return s()(n,[{key:"updateCustomerContext",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:["allowProductPriceOverwrites"],a="_proxy/switch-customer",c=this.getBasicHeaders(g(g({},i),{},{"sw-context-token":n}));return this.httpClient.patch(a,{customerId:t,salesChannelId:e,permissions:o},g(g({},r),{},{headers:c}))}},{key:"updateContext",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_proxy/store-api/".concat(e,"/context"),a=this.getBasicHeaders(g(g({},i),{},{"sw-context-token":n}));return this.httpClient.patch(o,t,g(g({},r),{},{headers:a}))}},{key:"getContext",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="_proxy/store-api/".concat(t,"/").concat(e),o=this.getBasicHeaders(r);return this.httpClient.post(i,{},g(g({},n),{},{headers:o}))}},{key:"getSalesChannelContext",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="_proxy/store-api/".concat(t,"/context"),o=this.getBasicHeaders(g(g({},r),{},{"sw-context-token":e}));return this.httpClient.get(i,g(g({},n),{},{headers:o}))}}]),n}(n("2iSf").a);e.default=m},"8jby":function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"business-events";return i()(this,n),(o=e.call(this,t,r,a)).name="businessEventService",o}return a()(n,[{key:"getBusinessEvents",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_info/events.json",{params:n,headers:r}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},A0sK:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"product-export";return i()(this,n),(o=e.call(this,t,r,a)).name="productExportService",o}return a()(n,[{key:"validateProductExportTemplate",value:function(t){var e="/_action/".concat(this.getApiBasePath(),"/validate");return this.httpClient.post(e,t,{headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}},{key:"previewProductExport",value:function(t){var e="/_action/".concat(this.getApiBasePath(),"/preview");return this.httpClient.post(e,t,{headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}},{key:"generateKey",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/access-key/product-export",{params:n,headers:r}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},A45n:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=function(){function t(e,n){i()(this,t),this.httpClient=e,this.loginService=n,this.name="aclApiService"}return a()(t,[{key:"additionalPrivileges",value:function(){var t=this.getHeaders();return this.httpClient.get("/_action/acl/additional_privileges",{headers:t}).then((function(t){return Object.values(t.data)}))}},{key:"allPrivileges",value:function(){var t=this.getHeaders();return this.httpClient.get("/_action/index",{},{headers:t}).then((function(t){return Object.values(t.data)}))}},{key:"getHeaders",value:function(){return{Accept:"application/json",Authorization:"Bearer ".concat(this.loginService.getToken()),"Content-Type":"application/json"}}}]),t}();e.default=c},AF7S:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var r=["/search/user-config","/search/product","/search/product-review","/search/property-group","/search/newsletter-recipient","/search/salutation","/search/product-search-config","/search/product-search-config-field","/app-system/action-button/product/list","app-system/action-button/product/list","/search/currency","/search/order","/search/customer","/_info/me"],i=["/user-config","user-config","/_action/sync","_action/sync","/product-visibility","product-visibility"];function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(n){var o=["delete","patch"].includes(null==n?void 0:n.method),c=i.includes(null==n?void 0:n.url);if(o||c)return Object.keys(e).forEach((function(t){delete e[t]})),t(n);if(!r.includes(null==n?void 0:n.url))return t(n);var s=JSON.stringify(n),u=e[s];return u?(Shopware.Utils.debug.warn("http.factory","Duplicated requests happening in short amount of time: ",n,"This duplicated request should be fixed."),a(u)):(e[s]=t(n),setTimeout((function(){e[s]&&delete e[s]}),1500),a(e[s]))}}function a(t){return t.then((function(t){return JSON.parse(JSON.stringify(t))}))}},AXza:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return v}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o;return i()(this,n),(o=e.call(this,t,r,null,"application/json")).name="userInputSanitizeService",o}return a()(n,[{key:"sanitizeInput",value:function(t){var e=t.html,n=t.field;return this.httpClient.post("_admin/sanitize-html",{html:e,field:null!=n?n:null},{headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a)},"C0V+":function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"increment/user_activity";return i()(this,n),(o=e.call(this,t,r,a)).name="userActivityApiService",o}return a()(n,[{key:"increment",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this.getBasicHeaders(n);return this.httpClient.post("/_action/increment/user_activity",t,{additionalParams:e,headers:r}).then((function(t){return p.a.handleResponse(t)}))}},{key:"getIncrement",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/increment/user_activity",{params:n,headers:r}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},CkOj:function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));var r=n("lSNA"),i=n.n(r),o=n("KzIn"),a=n("M7+a");function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function u(t){var e=function(t){var e;if(o.a.isString(t))try{e=JSON.parse(t)}catch(t){return!1}else{if(!o.a.isObject(t)||o.a.isArray(t))return!1;e=t}return e}(t);if(!e)return null;if(!0===e.parsed||!function(t){return void 0!==t.data||void 0!==t.errors||void 0!==t.links||void 0!==t.meta}(e))return e;var n=function(t){var e={links:null,errors:null,data:null,associations:null,aggregations:null};if(t.errors)return e.errors=t.errors,e;var n=function(t){var e=new Map;if(!t||!t.length)return e;return t.forEach((function(t){var n="".concat(t.type,"-").concat(t.id);e.set(n,t)})),e}(t.included);if(o.a.isArray(t.data))e.data=t.data.map((function(t){var r=l(t,n);return Object(a.g)(r,"associationLinks")&&(e.associations=s(s({},e.associations),r.associationLinks),delete r.associationLinks),r}));else if(o.a.isObject(t.data)){var r=l(t.data,n);Object.prototype.hasOwnProperty.call(r,"associationLinks")&&(e.associations=s(s({},e.associations),r.associationLinks),delete r.associationLinks),e.data=r}else e.data=null;t.meta&&Object.keys(t.meta).length&&(e.meta=f(t.meta));t.links&&Object.keys(t.links).length&&(e.links=t.links);t.aggregations&&Object.keys(t.aggregations).length&&(e.aggregations=t.aggregations);return e}(e);return n.parsed=!0,n}function l(t,e){var n={id:t.id,type:t.type,links:t.links||{},meta:t.meta||{}};if(t.attributes&&Object.keys(t.attributes).length>0){var r=f(t.attributes);n=s(s({},n),r)}if(t.relationships){var i=function(t,e){var n={},r={};return Object.keys(t).forEach((function(i){var a=t[i];if(a.links&&Object.keys(a.links).length&&(r[i]=a.links.related),a.data){var c=a.data;o.a.isArray(c)?n[i]=c.map((function(t){return h(t,e)})):o.a.isObject(c)?n[i]=h(c,e):n[i]=null}})),{mappedRelations:n,associationLinks:r}}(t.relationships,e);n=s(s(s({},n),i.mappedRelations),{associationLinks:i.associationLinks})}return n}function f(t){var e={};return Object.keys(t).forEach((function(n){var r=t[n],i=n.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()}));e[i]=r})),e}function h(t,e){var n="".concat(t.type,"-").concat(t.id);return e.has(n)?l(e.get(n),e):t}},Cv5K:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return g}));var r=n("J4zp"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p);function v(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var g=function(t){l()(n,t);var e=v(n);function n(t,r){var i;return a()(this,n),(i=e.call(this,t,r,null,"application/json")).name="appUrlChangeService",i}return s()(n,[{key:"fetchResolverStrategies",value:function(){return this.httpClient.get("app-system/app-url-change/strategies",{headers:this.getBasicHeaders()}).then((function(t){var e=t.data;return Object.entries(e).map((function(t){var e=i()(t,2);return{name:e[0],description:e[1]}}))}))}},{key:"resolveUrlChange",value:function(t){var e=t.name;return this.httpClient.post("app-system/app-url-change/resolve",{strategy:e},{headers:this.getBasicHeaders()})}},{key:"getUrlDiff",value:function(){return this.httpClient.get("app-system/app-url-change/url-difference",{headers:this.getBasicHeaders()}).then((function(t){return 204===t.status?null:t.data}))}}]),n}(n("2iSf").a)},D7Q7:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return R}));var r=n("cDf5"),i=n.n(r),o=n("J4zp"),a=n.n(o),c=n("yXPU"),s=n.n(c),u=n("lSNA"),l=n.n(u),f=n("lwsE"),h=n.n(f),p=n("W8MJ"),d=n.n(p),v=n("7W2i"),g=n.n(v),y=n("a1gu"),m=n.n(y),b=n("Nsbk"),w=n.n(b),O=n("2iSf");function k(){k=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,i){var o=e&&e.prototype instanceof p?e:p,a=Object.create(o.prototype),c=new P(i||[]);return r(a,"_invoke",{value:j(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var h={};function p(){}function d(){}function v(){}var g={};u(g,a,(function(){return this}));var y=Object.getPrototypeOf,m=y&&y(y(R([])));m&&m!==e&&n.call(m,a)&&(g=m);var b=v.prototype=p.prototype=Object.create(g);function w(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function o(r,a,c,s){var u=f(t[r],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==i()(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){o("next",t,c,s)}),(function(t){o("throw",t,c,s)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return o("throw",t,c,s)}))}s(u.arg)}var a;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function j(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return C()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=E(a,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=f(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===h)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function E(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,E(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=f(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function R(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:C}}function C(){return{value:void 0,done:!0}}return d.prototype=v,r(b,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:d,configurable:!0}),d.displayName=u(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},w(O.prototype),u(O.prototype,c,(function(){return this})),t.AsyncIterator=O,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new O(l(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},w(b),u(b,s,"Generator"),u(b,a,(function(){return this})),u(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=R,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:R(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t}function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function E(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){l()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function S(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=w()(t);if(e){var i=w()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return m()(this,n)}}var x=Shopware.Service,P=Shopware.Data.Criteria,R=function(t){g()(r,t);var e,n=S(r);function r(t,e){var i;return h()(this,r),(i=n.call(this,t,e,null,"application/json")).name="tagApiService",i}return d()(r,[{key:"filterIds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.httpClient.post("_admin/tag-filter-ids",E(E({},t),e),{headers:this.getBasicHeaders(n)}).then((function(t){return O.a.handleResponse(t)}))}},{key:"merge",value:(e=s()(k().mark((function t(e,n,r,i){var o,c,s,u,l,f,h,p,d,v,g,y,m,b;return k().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=200,c=this.getRepository("tag"),i.isRunning=!0,(s=c.create()).name=n,t.next=7,c.save(s);case 7:s._isNew=!1,u=0,l=Object.entries(r);case 9:if(!(u<l.length)){t.next=37;break}if(f=a()(l[u],2),h=f[0],"many_to_many"===(p=f[1]).relation){t.next=13;break}return t.abrupt("continue",34);case 13:d=1,i.currentAssignment=h,i.progress=0,i.total=0,v=this.getRepository(p.entity);case 18:return(g=new P(d,o)).addFilter(P.equalsAny("tags.id",e)),t.next=22,v.searchIds(g,Shopware.Context.api);case 22:if(y=t.sent,m=y.data,b=y.total,s[h]=m.map((function(t){return{id:t}})),0===b){t.next=30;break}return i.total=b,t.next=30,c.save(s);case 30:s[h]=[],i.progress+=m.length,d+=1;case 33:if(i.isRunning&&i.progress<i.total){t.next=18;break}case 34:u++,t.next=9;break;case 37:if(i.isRunning){t.next=39;break}return t.abrupt("return");case 39:return t.next=41,c.syncDeleted(e,Shopware.Context.api);case 41:case"end":return t.stop()}}),t,this)}))),function(t,n,r,i){return e.apply(this,arguments)})},{key:"getRepository",value:function(t){return x("repositoryFactory").create(t)}}]),r}(O.a)},DPbZ:function(t,e,n){"use strict";var r=n("vIi1");e.a=r.a},"E+Sq":function(t,e,n){"use strict";n.d(e,"a",(function(){return p}));var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("KzIn");function l(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?l(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function h(t){return""===t||void 0===t?null:t}var p=function(){function t(){a()(this,t)}return s()(t,[{key:"getPrimaryKeyData",value:function(t){var e=Shopware.EntityDefinition.get(t.getEntityName()).getPrimaryKeyFields(),n={};return Object.keys(e).forEach((function(e){n[e]=t[e]})),n}},{key:"generate",value:function(t){var e=[];return{changes:this.recursion(t,e),deletionQueue:e}}},{key:"recursion",value:function(t,e){var n=this,r=Shopware.EntityDefinition.get(t.getEntityName()),i={},o=t.getOrigin(),a=t.getDraft();return r.forEachField((function(c,s){if(!c.readOnly&&!c.flags.write_protected){var l=h(a[s]),f=h(o[s]);if(r.isScalarField(c))l!==f&&(i[s]=l);else if(c.flags.extension&&(l=h(a.extensions[s]),f=h(o.extensions[s])),r.isJsonField(c)){if(!u.a.isEqual(f,l)){if(Array.isArray(l)&&l.length<=0)return void(i[s]=[]);i[s]=l}}else if("association"===c.type)switch(c.relation){case"one_to_many":var p=n.handleOneToMany(c,l,f,e);p.length>0&&(i[s]=p);break;case"many_to_many":var d=n.handleManyToMany(l,f,e,c,t);d.length>0&&(i[s]=d);break;case"one_to_one":if(!l)return;var v=n.recursion(l,e);null!==v&&(v.id=l.id,i[s]=v)}else l!==f&&(i[s]=l)}})),Object.keys(i).length>0?f(f({},this.getPrimaryKeyData(t)),i):null}},{key:"handleManyToMany",value:function(t,e,n,r,o){var a=[],c=e.getIds();return t.forEach((function(t){c.includes(t.id)||a.push({id:t.id})})),c.forEach((function(e){if(!t.has(e)){var a,c=(a={},i()(a,r.local,o.id),i()(a,r.reference,e),a);n.push({route:t.source,key:e,entity:r.mapping,primary:c})}})),a}},{key:"handleOneToMany",value:function(t,e,n,r){var o,a=this,c=[],s=n.getIds();return e.forEach((function(t){if(!s.includes(t.id)){var e=a.recursion(t,[]);return null===e?e={id:t.id}:e.id=t.id,void c.push(e)}var n=a.recursion(t,r);null!==n&&(n.id=t.id,c.push(n))})),null!==(o=t.flags)&&void 0!==o&&o.cascade_delete?(s.forEach((function(n){if(!e.has(n)){var o=i()({},t.primary,n);r.push({route:e.source,key:n,entity:t.entity,primary:o})}})),c):t.referenceField?(s.forEach((function(n){if(!e.has(n)){var r={id:n};r[t.referenceField]=null,c.push(r)}})),c):c}}]),t}()},EL6e:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p);function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function y(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var m=function(t){l()(n,t);var e=y(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"checkout";return a()(this,n),(i=e.call(this,t,r,o)).name="checkoutStoreService",i}return s()(n,[{key:"checkout",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="_proxy-order/".concat(t),o=g(g({},this.getBasicHeaders(r)),{},{"sw-context-token":e});return this.httpClient.post(i,{},g(g({},n),{},{headers:o}))}}]),n}(n("2iSf").a);e.default=m},Eagv:function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("wMS7"),s=n.n(c),u=["beforeSanitizeElements","uponSanitizeElement","afterSanitizeElements","beforeSanitizeAttributes","uponSanitizeAttribute","afterSanitizeAttributes","beforeSanitizeShadowDOM","uponSanitizeShadowNode","afterSanitizeShadowDOM"],l=function(){function t(){i()(this,t)}return a()(t,null,[{key:"setConfig",value:function(t){return s.a.setConfig(t)}},{key:"clearConfig",value:function(){return s.a.clearConfig()}},{key:"addMiddleware",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};return u.includes(t)?(s.a.addHook(t,e),!0):(Shopware.Utils.debug.warn("Sanitizer",'No middleware found for name "'.concat(t,'",\n                the following are available: ').concat(u.join(", "))),!1)}},{key:"removeMiddleware",value:function(t){return u.includes(t)?(s.a.removeHooks(t),!0):(Shopware.Utils.debug.warn("Sanitizer",'No middleware found for name "'.concat(t,'",\n                the following are available: ').concat(u.join(", "))),!1)}},{key:"sanitize",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s.a.sanitize(t,e)}}]),t}()},H1NO:function(t,e,n){"use strict";n.r(e),n.d(e,"ShopwareClass",(function(){return yn}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("lSNA"),s=n.n(c),u=n("D6G9"),l=n("J4zp"),f=n.n(l),h=n("gyhU"),p=n("M7+a"),d=n("KzIn"),v=function(){function t(){i()(this,t),this._stack=[]}return a()(t,[{key:"stack",get:function(){return this._stack}},{key:"use",value:function(t){if("function"!=typeof t)throw new Error("Middleware must be a function.");return this._stack.push(t),this}},{key:"go",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];this.stack.forEach((function(t){return t.apply(void 0,[function(){}].concat(e))}))}}]),t}(),g={getModuleRoutes:function(){var t=[];return y.forEach((function(e){e.routes.forEach((function(e){var n;Object(p.g)(e,"flag")&&!Shopware.Feature.isActive(null!==(n=e.flag)&&void 0!==n?n:"")||e.isChildren||(m.go(e),t.push(e))}))})),t},registerModule:function(t,e){var n=e.type||"plugin",r=new Map;if(!t)return Object(h.b)("ModuleFactory",'Module has no unique identifier "id". Abort registration.',e),!1;if(y.has(t))return Object(h.b)("ModuleFactory",'A module with the identifier "'.concat(t,'" is registered already. Abort registration.'),y.get(t)),!1;var i=t.split("-");if(i.length<2)return Object(h.b)("ModuleFactory",'Module identifier does not match the necessary format "[namespace]-[name]":',t,"Abort registration."),!1;Object(p.g)(e,"display")||(e.display=!0);if(!e.display)return!1;if(!Object(p.g)(e,"routes")&&!e.routeMiddleware)return Object(h.b)("ModuleFactory",'Module "'.concat(t,'" has no configured routes or a routeMiddleware.'),"The module will not be accessible in the administration UI.","Abort registration.",e),!1;Object(p.g)(e,"routes")&&Object.keys(e.routes).forEach((function(o){var a,c,s=e.routes[o],u=e.routePrefixName?e.routePrefixName:i.join(".");s.name="".concat(u,".").concat(o);var l=e.routePrefixPath?e.routePrefixPath:i.join("/");if(s.coreRoute||(s.path="/".concat(l,"/").concat(s.path)),s.type=n,s=function(t,e,n){var r;Object(p.g)(n,"flag")&&(t.flag=n.flag);t.component&&(t.components={default:t.component},delete t.component);var i={},o=null!==(r=t.components)&&void 0!==r?r:{};return Object.keys(o).forEach((function(t){var n=o[t];n?i[t]=n:Object(h.b)("ModuleFactory",'The route definition of module "'.concat(e,'" is not valid.\n                    A route needs an assigned component name.'))})),t.components=i,t}(s,t,e),s){var f=null!==(a=s.children)&&void 0!==a?a:{};Object(p.g)(s,"children")&&Object.keys(f).length&&(s=w(s),r=b(s,r)),s.alias&&"string"==typeof s.alias&&s.alias.length>0&&!s.coreRoute&&(s.alias="/".concat(i.join("/"),"/").concat(s.alias)),s.isChildren=!1,s.routeKey=o,r.set(null!==(c=s.name)&&void 0!==c?c:"",s)}}));if(e.routeMiddleware&&d.a.isFunction(e.routeMiddleware))m.use(e.routeMiddleware);else if(0===r.size)return Object(h.b)("ModuleFactory",'The module "'.concat(t,"\" was not registered cause it hasn't a valid route definition"),"Abort registration.",e.routes),!1;var o={routes:r,manifest:e,type:n};if(Object(p.g)(e,"navigation")&&e.navigation){if(!d.a.isArray(e.navigation))return Object(h.b)("ModuleFactory","The route definition has to be an array.",e.navigation),!1;e.navigation=e.navigation.filter((function(t){return t.moduleType=e.type,"plugin"!==e.type||t.parent?t.id||t.path||t.parent||t.link?t.label&&t.label.length?("plugin"===e.type&&(t.position?t.position+=1e3:t.position=1e3),!0):(Object(h.b)("ModuleFactory",'The navigation entry needs a property called "label"'),!1):(Object(h.b)("ModuleFactory","The navigation entry does not contains the necessary properties","Abort registration of the navigation entry",t),!1):(Object(h.b)("ModuleFactory","Navigation entries from plugins are not allowed on the first level.",'Set a property "parent" to register your navigation entry'),!1)})),o.navigation=e.navigation}Object(p.g)(e,"settingsItem")&&e.settingsItem&&function(t,e){var n;if(Object(p.g)(e,"flag")&&!Shopware.Feature.isActive(null!==(n=e.flag)&&void 0!==n?n:""))return;if(!e.settingsItem)return;Array.isArray(e.settingsItem)||(e.settingsItem=[e.settingsItem]);e.settingsItem.forEach((function(n){n.group&&n.to&&(n.icon||n.iconComponent)?(Object(p.g)(n,"id")&&n.id||(n.id=t),Object(p.g)(n,"name")&&n.name||(n.name=e.name),Object(p.g)(n,"label")&&n.label||(n.label=e.title),Shopware.State.commit("settingsItems/addItem",n)):Object(h.b)("ModuleFactory","The settingsItem entry does not contain the necessary properties","Abort registration of settingsItem entry",n)}))}(t,e);Object(p.g)(e,"extensionEntryRoute")&&e.extensionEntryRoute&&function(t){if("string"===t.extensionName)return void Object(h.b)("ModuleFactory","extensionEntryRoute.extensionName needs to be an string");if("string"===t.route)return void Object(h.b)("ModuleFactory","extensionEntryRoute.route needs to be an string");Shopware.State.commit("extensionEntryRoutes/addItem",t)}(e.extensionEntryRoute);return y.set(t,o),o},getModuleRegistry:function(){return y.forEach((function(t,e){var n,r;Object(p.g)(t.manifest,"flag")&&!Shopware.Feature.isActive(null!==(n=null==t||null===(r=t.manifest)||void 0===r?void 0:r.flag)&&void 0!==n?n:"")&&y.delete(e)})),y},getModuleByEntityName:function(t){return Array.from(y.values()).find((function(e){return t===e.manifest.entity}))},getModuleSnippets:function(){return Array.from(y.values()).reduce((function(t,e){var n,r=e.manifest;if(!Object(p.g)(r,"snippets"))return t;var i=Object.keys(null!==(n=r.snippets)&&void 0!==n?n:{});return i.length?(i.forEach((function(e){if(Object(p.g)(t,e)||(t[e]={}),r.snippets){var n=r.snippets[e];t[e]=Object(p.h)(t[e],n)}})),t):t}),{})},getModuleByKey:function(t,e){return Array.from(y.values()).find((function(n){return e===n.manifest[t]}))}},y=new Map,m=new v;function b(t,e){var n;return Object.values(null!==(n=t.children)&&void 0!==n?n:{}).map((function(t){var n,r;return Object(p.g)(t,"children")&&Object.keys(null!==(n=t.children)&&void 0!==n?n:{}).length&&(e=b(t,e)),e.set(null!==(r=t.name)&&void 0!==r?r:"",t),t})),e}function w(t){var e=t.children;return e?(t.children=Object.entries(e).map((function(e){var n,r,i=f()(e,2),o=i[0],a=i[1];return a.path&&0===a.path.length?a.path="":a.path="".concat(t.path,"/").concat(a.path),a.name="".concat(null!==(n=t.name)&&void 0!==n?n:"",".").concat(o),a.isChildren=!0,Object(p.g)(a,"children")&&Object.keys(null!==(r=a.children)&&void 0!==r?r:{}).length&&(a=w(a)),a})),t):t}var O=n("RIqP"),k=n.n(O),j=n("cDf5"),E=n.n(j),S=n("uorI"),x=n.n(S);function P(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function R(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?P(Object(n),!0).forEach((function(e){s()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var C={registerComponentTemplate:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=T.get(t)||{},r=n.overrides?n.overrides:[];return T.set(t,{name:t,raw:e,extend:null,overrides:r}),!0},extendComponentTemplate:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=T.get(t)||{},i=r.overrides?r.overrides:[];n||(n="");return T.set(t,{name:t,raw:n,extend:e,overrides:i}),!0},registerTemplateOverride:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=T.get(t)||{name:t,raw:null,extend:null,overrides:[]};return r.overrides.push({index:n,raw:e}),T.set(t,r),!0},getRenderedTemplate:function(t){if(Shopware.Feature.isActive("FEATURE_NEXT_19822")){var e=T.get(t);if(!e)return null;D(e);var n=L.get(t);return n?n.html:null}var r=L.get(t);return r?r.html:null},resolveTemplates:function(){return Array.from(T.values()).forEach(D),L},clearTwigCache:function(){x.a.clearRegistry()},getTwigCache:function(){return x.a.getRegistry()},disableTwigCache:function(){x.a.cache(!1)},getTemplateRegistry:function(){return T},getNormalizedTemplateRegistry:function(){return L},getTemplateOverrides:function(t){if(!T.has(t))return[];return T.get(t).overrides||[]}},_=null;x.a.extend((function(t){t.token.definitions=[t.token.definitions[0],t.token.definitions[1],t.token.definitions[5],t.token.definitions[6],t.token.definitions[7],t.token.definitions[9],t.token.definitions[10]],t.exports.extendTag({type:"parent",regex:/^parent/,next:[],open:!0,parse:function(e,n,r){return{chain:r,output:t.placeholders.parent}}}),t.exports.placeholders=t.placeholders,t.exports.getRegistry=function(){return t.Templates.registry},t.exports.clearRegistry=function(){t.Templates.registry={}},_=t.Templates,t.cache=!1}));var A=x.a.placeholders.parent.replace(/\|/g,"\\|"),B=new RegExp(A,"gm"),T=new Map,L=new Map;function D(t){var e=U(t);if(e){(function t(e){return!!e.extend&&(e.extend.overrides.length>0||t(e.extend))})(e=R(R({},e),{},{html:""}))&&Shopware.Feature.isActive("FEATURE_NEXT_17978")&&D(T.get(e.extend.name));var n=I(e.template.tokens,e);e.template.tokens=n,L.set(e.name,e),e=function(t){var e=L.get(t);if(!e.overrides.length){var n=e.template.render({}),r=R(R({},e),{},{html:n});return L.set(r.name,r),r}var i=L.get(e.name);e.overrides.forEach((function(t,e){G("".concat(i.name,"-").concat(e),t.raw).tokens.forEach((function(t){"logic"===t.type&&(i.template.tokens=N(i.template.tokens,[t]))}))})),L.set(i.name,i);var o=L.get(e.name),a=o.template.render({});return o=R(R({},o),{},{html:a}),L.set(o.name,o),o}(e.name),e.html=e.html.replace(B,""),L.set(e.name,e)}else L.delete(t.name)}function N(t,e){return t?t.reduce((function(t,n){if("logic"!==n.type||!n.token||!n.token.block)return[].concat(k()(t),[n]);var r=M(n.token.block,e);if(r)return"logic"===r.type&&(r.token.output=F(n,r.token.output)),[].concat(k()(t),[r]);var i=N(n.token.output,e);return n.token.output=i,[].concat(k()(t),[n])}),[]):[]}function F(t,e){return e.reduce((function(e,n){var r,i,o;return"logic"===n.type&&"parent"===n.token.type?[].concat(k()(e),k()(t.token.output)):(null!==(r=n.token)&&void 0!==r&&r.output&&(n.token.output=(i=n.token.output,o=t.token.output,i.reduce((function(t,e){return"logic"===e.type&&"parent"===e.token.type?[].concat(k()(t),k()(o)):[].concat(k()(t),[e])}),[]))),[].concat(k()(e),[n]))}),[])}function I(t,e){if(!e.extend)return t;var n;n=L.has(e.extend.name)&&Shopware.Feature.isActive("FEATURE_NEXT_17978")?Object(p.a)(L.get(e.extend.name).template.tokens):e.extend.template.tokens;var r=Array.from(I(n,e.extend)),i=function(t,e){var n=t.reduce((function(t,n){return n.token&&!H(n.token.block,e)?[].concat(k()(t),k()(n.token.output)):[].concat(k()(t),[n])}),[]);return n}(Array.from(t),r);return t=r.map((function(t){return W(t,i,e.name)}))}function H(t,e){return e.find((function(e){return e.token&&e.token.block===t||e.token&&H(t,e.token.output)}))}function M(t,e){return e.find((function(e){return e.token&&e.token.block===t}))}function W(t,e,n){if("logic"!==t.type)return t;var r=M(t.token.block,e);return r?("logic"!==r.type||(r.token.output=F(t,r.token.output)),r):(t.token.output=t.token.output.map((function(t){return W(t,e,n)})),t)}function U(t){if(!t)return null;if(t.extend){var e=U(T.get(t.extend));return e?R(R({},t),{},{template:G(t.name,t.raw),extend:e}):null}return R(R({},t),{},{template:G(t.name,t.raw)})}function G(t,e){return _.parsers.twig({id:"".concat(t,"-baseTemplate"),data:e,path:!1,options:{}})}function J(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function z(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?J(Object(n),!0).forEach((function(e){s()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):J(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var q={register:function(t,e){var n=e;if(!t||!t.length)return Object(h.b)("ComponentFactory","A component always needs a name.",e),!1;if($.has(t))return Object(h.b)("ComponentFactory",'The component "'.concat(t,'" is already registered. Please select a unique name for your component.'),n),!1;if(n.name=t,n.template)C.registerComponentTemplate(t,n.template),delete n.template;else if(!n.functional&&"function"!=typeof n.render)return Object(h.b)("ComponentFactory",'The component "'.concat(n.name,'" needs a template to be functional.'),'Please add a "template" property to your component definition',n),!1;return $.set(t,n),n},extend:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{name:""},r=n;r.template?(C.extendComponentTemplate(t,e,r.template),delete r.template):C.extendComponentTemplate(t,e);return r.name=t,r.extends=e,$.set(t,r),r},override:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=e;r.name=t,r.template&&(C.registerTemplateOverride(t,r.template,n),delete r.template);var i=K.get(t)||[];null!==n&&n>=0&&i.length>0?i.splice(n,0,r):i.push(r);return K.set(t,i),r},build:function t(e){var n,r,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];V||at();var o=$.get(e);if(!o)throw new Error('The component registry has not found a component with the name "'.concat(e,'".'));if(!(o=Object.create(o)))throw new Error('The config of the component "'.concat(e,'" is invalid.'));if(o.extends){var a;if("string"==typeof o.extends){var c=t(o.extends,!0);"boolean"!=typeof c&&(a=c)}a?o.extends=a:delete o.extends}if(K.has(e)){var s=Object(p.a)(K.get(e)),u=Z(s);u.forEach((function(t){t.extends=o,t._isOverride=!0,o=t}))}var l=Q(o);if(ot(l)&&o){var f,h,d=it(o)?"#".concat(e):"string"!=typeof o.extends&&(null===(f=o)||void 0===f||null===(h=f.extends)||void 0===h?void 0:h.name);o.methods=z(z({},o.methods),et(d,l))}if("function"==typeof(null===(n=o)||void 0===n?void 0:n.render))return delete o.template,o;if(i&&o)return delete o.template,o;var v=X(e);o&&"string"==typeof v&&(o.template=v);if("string"!=typeof(null===(r=o)||void 0===r?void 0:r.template))return!1;return o},getComponentTemplate:X,getComponentRegistry:function(){return $},getOverrideRegistry:function(){return K},getComponentHelper:function(){return Y},registerComponentHelper:function(t,e){if(!t||!t.length)return Object(h.b)("ComponentFactory/ComponentHelper","A ComponentHelper always needs a name.",e),!1;if(Y.hasOwnProperty(t))return Object(h.b)("ComponentFactory/ComponentHelper","A ComponentHelper with the name ".concat(t," already exists."),e),!1;return Y[t]=e,!0},resolveComponentTemplates:at,markComponentTemplatesAsNotResolved:function(){return C.getNormalizedTemplateRegistry().clear(),V=!1,!0}},V=!1,$=new Map,K=new Map,Y={};function X(t){return V||at(),C.getRenderedTemplate(t)}function Z(t){return t?t.reduceRight((function(t,e){if(0===t.length)return[e];var n=t.shift();return Object.entries(e).forEach((function(t){var r=f()(t,2),i=r[0],o=r[1];if(n&&n.hasOwnProperty(i)){if(Array.isArray(o))return;"object"===E()(o)&&Object.entries(o).forEach((function(t){var r=f()(t,2),o=r[0],a=r[1];n[i].hasOwnProperty(o)||(n[i][o]=a,delete e[i][o])}))}else n[i]=o,delete e[i]})),[e].concat([n],k()(t))}),[]):[]}function Q(t){var e={};return t._isOverride&&t.extends&&"string"!=typeof t.extends&&(e=Q(t.extends)),["computed","methods"].forEach((function(n){var r=t[n];r&&Object.entries(r).forEach((function(r){var i=f()(r,2),o=i[0],a=i[1];"computed"===n&&"object"===E()(a)?Object.entries(a).forEach((function(r){var i=f()(r,2),a=i[0],c=i[1],s="".concat(o,".").concat(a);e=tt(e,s,c,n,t)})):e=tt(e,o,a,n,t)}))})),e}function tt(t,e,n,r,i){var o="function"==typeof n&&n.toString();if(!(o&&/\.\$super/g.test(o)))return t;t.hasOwnProperty(e)||(t[e]={});var a=it(i)?"#":"";return t[e]=nt(i,e,r,a),t}function et(t,e){var n={$super:function(t){this._initVirtualCallStack(t);var e=this._findInSuperRegister(t),n=e[this._virtualCallStack[t]];this._virtualCallStack[t]=n.parent;for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];var a=n.func.bind(this).apply(void 0,i);return n.parent&&(this._virtualCallStack[t]=this._inheritedFrom()),a},_initVirtualCallStack:function(t){this._virtualCallStack||(this._virtualCallStack={name:t}),this._virtualCallStack[t]||(this._virtualCallStack[t]=this._inheritedFrom())},_findInSuperRegister:function(t){return this._superRegistry()[t]},_superRegistry:function(){return e},_inheritedFrom:function(){return t}};return n}function nt(t,e){var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"methods",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=t.extends;if(!a||"string"==typeof a)return{};var c="".concat(o).concat(null!==(n=a.name)&&void 0!==n?n:""),s="object"===E()(a.extends)&&a.extends?"".concat(o).concat(null!==(r=a.extends.name)&&void 0!==r?r:""):null;if(c===s){(o.length>0||a._isOverride)&&(o="#".concat(o));var u=a&&a.extends&&"string"!=typeof a.extends&&a.extends.name,l="string"==typeof u?u:"";s="".concat(o).concat(l)}var f=rt(a,e,i),h={};h[c]={parent:s,func:f};var p=nt(a,e,i,o),d=z(z({},p),h);return d}function rt(t,e,n){var r,i=e.split(".");return i.length>1?function(t,e,n){var r=f()(e,2),i=r[0],o=r[1];if(!t[n])return rt(t.extends,i,n);if(!t[n][i])return rt(t.extends,i,n);return t[n][i][o]}(t,i,n):null!==(r=t[n])&&void 0!==r&&r[e]?t[n][e]:t.extends?rt(t.extends,e,n):null}function it(t){return!(!t.extends||"string"==typeof t.extends)&&t.extends.name===t.name}function ot(t){return 0!==Object.keys(t).length&&t.constructor===Object}function at(){return C.resolveTemplates(),V=!0,!0}var ct=n("yXPU"),st=n.n(ct);function ut(){ut=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function u(t,e,n,i){var o=e&&e.prototype instanceof h?e:h,a=Object.create(o.prototype),c=new x(i||[]);return r(a,"_invoke",{value:O(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var f={};function h(){}function p(){}function d(){}var v={};s(v,o,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(P([])));y&&y!==e&&n.call(y,o)&&(v=y);var m=d.prototype=h.prototype=Object.create(v);function b(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function i(r,o,a,c){var s=l(t[r],t,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==E()(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){i("next",t,a,c)}),(function(t){i("throw",t,a,c)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return i("throw",t,a,c)}))}c(s.arg)}var o;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){i(t,n,e,r)}))}return o=o?o.then(r,r):r()}})}function O(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return R()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=k(a,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=l(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function k(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var i=l(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,f;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function P(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:R}}function R(){return{value:void 0,done:!0}}return p.prototype=d,r(m,"constructor",{value:d,configurable:!0}),r(d,"constructor",{value:p,configurable:!0}),p.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},b(w.prototype),s(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new w(u(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(m),s(m,c,"Generator"),s(m,o,(function(){return this})),s(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=P,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;S(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:P(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},t}function lt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ft(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?lt(Object(n),!0).forEach((function(e){s()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):lt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var ht={register:function(t,e){if(!t||!t.length)return Object(h.b)("ComponentFactory","A component always needs a name.",e),!1;if(pt.has(t))return Object(h.b)("ComponentFactory",'The component "'.concat(t,'" is already registered. Please select a unique name for your component.'),e),!1;var n=function(){var n=st()(ut().mark((function n(){var r,i,o;return ut().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r="function"==typeof e?e:function(){return Promise.resolve(e)},n.next=3,r();case 3:if((i=n.sent).hasOwnProperty("default")&&(i=i.default),(o=ft({},i)).name=t,!o.template){n.next=12;break}C.registerComponentTemplate(t,o.template),delete o.template,n.next=15;break;case 12:if(o.functional||"function"==typeof o.render){n.next=15;break}return Object(h.b)("ComponentFactory",'The component "'.concat(o.name,'" needs a template to be functional.'),'Please add a "template" property to your component definition',o),n.abrupt("return",!1);case 15:return n.abrupt("return",o);case 16:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}();return pt.set(t,n),n},extend:function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{name:""},i=function(){var i=st()(ut().mark((function i(){var o,a;return ut().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(!n){i.next=2;break}return i.abrupt("return",n);case 2:return o="function"==typeof r?r:function(){return Promise.resolve(r)},i.next=5,o();case 5:return(a=i.sent).hasOwnProperty("default")&&(a=a.default),(n=ft({},a)).template?(C.extendComponentTemplate(t,e,n.template),delete n.template):C.extendComponentTemplate(t,e),n.name=t,n.extends=e,i.abrupt("return",n);case 12:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}();return pt.set(t,i),i},override:function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=function(){var i=st()(ut().mark((function i(){var o;return ut().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(!n){i.next=2;break}return i.abrupt("return",n);case 2:return o="function"==typeof e?e:function(){return Promise.resolve(e)},i.next=5,o();case 5:return(n=i.sent).hasOwnProperty("default")&&(n=n.default),n.name=t,n.template&&(C.registerTemplateOverride(t,n.template,r),delete n.template),i.abrupt("return",n);case 10:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}(),o=dt.get(t)||[];null!==r&&r>=0&&o.length>0?o.splice(r,0,i):o.push(i);return dt.set(t,o),i},build:Ot,wrapComponentConfig:function(t){return t},getComponentTemplate:yt,getComponentRegistry:function(){return pt},getOverrideRegistry:function(){return dt},getComponentHelper:function(){return vt},_clearComponentHelper:function(){Object.keys(vt).forEach((function(t){delete vt[t]}))},registerComponentHelper:function(t,e){if(!t||!t.length)return Object(h.b)("ComponentFactory/ComponentHelper","A ComponentHelper always needs a name.",e),!1;if(vt.hasOwnProperty(t))return Object(h.b)("ComponentFactory/ComponentHelper","A ComponentHelper with the name ".concat(t," already exists."),e),!1;return vt[t]=e,!0},resolveComponentTemplates:function(){return C.resolveTemplates(),!0},markComponentTemplatesAsNotResolved:function(){return C.getNormalizedTemplateRegistry().clear(),!0},isSyncComponent:function(t){return gt.has(t)},markComponentAsSync:function(t){gt.add(t)}},pt=new Map,dt=new Map,vt={},gt=new Set;function yt(t){return mt.apply(this,arguments)}function mt(){return(mt=st()(ut().mark((function t(e){return ut().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,bt(e);case 2:return t.abrupt("return",C.getRenderedTemplate(e));case 3:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function bt(t){return wt.apply(this,arguments)}function wt(){return(wt=st()(ut().mark((function t(e){var n,r;return ut().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=pt.get(e),r=dt.get(e),!n){t.next=5;break}return t.next=5,n();case 5:if(!r){t.next=8;break}return t.next=8,Promise.all(r.map((function(t){return t()})));case 8:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function Ot(t){return kt.apply(this,arguments)}function kt(){return kt=st()(ut().mark((function t(e){var n,r,i,o,a,c,s,u,l,f,h,d,v,g,y=arguments;return ut().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=y.length>1&&void 0!==y[1]&&y[1],o=pt.get(e)){t.next=4;break}throw new Error('The component registry has not found a component with the name "'.concat(e,'".'));case 4:return t.next=6,o();case 6:if("boolean"!=typeof(a=t.sent)){t.next=9;break}throw new Error('The component registry could not build the component with the name "'.concat(e,'".'));case 9:if(c=ft({},a)){t.next=12;break}throw new Error('The config of the component "'.concat(e,'" is invalid.'));case 12:if(!c.extends){t.next=19;break}if("string"!=typeof c.extends){t.next=18;break}return t.next=16,Ot(c.extends,!0);case 16:"boolean"!=typeof(u=t.sent)&&(s=u);case 18:s?c.extends=s:delete c.extends;case 19:if(!dt.has(e)){t.next=25;break}return l=Object(p.a)(dt.get(e)),t.next=23,jt(l);case 23:t.sent.forEach((function(t){t.extends=c,t._isOverride=!0,c=ft({},t)}));case 25:if(At(f=St(c))&&c&&(v=_t(c)?"#".concat(e):"string"!=typeof c.extends&&(null===(h=c)||void 0===h||null===(d=h.extends)||void 0===d?void 0:d.name),c.methods=ft(ft({},c.methods),Pt(v,f))),"function"!=typeof(null===(n=c)||void 0===n?void 0:n.render)){t.next=30;break}return delete c.template,t.abrupt("return",c);case 30:if(!i||!c){t.next=33;break}return delete c.template,t.abrupt("return",c);case 33:return t.next=35,yt(e);case 35:if(g=t.sent,c&&"string"==typeof g&&(c.template=g),"string"==typeof(null===(r=c)||void 0===r?void 0:r.template)){t.next=39;break}return t.abrupt("return",!1);case 39:return t.abrupt("return",c);case 40:case"end":return t.stop()}}),t)}))),kt.apply(this,arguments)}function jt(t){return Et.apply(this,arguments)}function Et(){return(Et=st()(ut().mark((function t(e){var n;return ut().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return",[]);case 2:return t.next=4,Promise.all(e.map((function(t){return t()})));case 4:return n=t.sent,t.abrupt("return",n.reduceRight((function(t,e){if(0===t.length)return[e];var n=t.shift();return Object.entries(e).forEach((function(t){var r=f()(t,2),i=r[0],o=r[1];if(n&&n.hasOwnProperty(i)){if(Array.isArray(o))return;"object"===E()(o)&&Object.entries(o).forEach((function(t){var r=f()(t,2),o=r[0],a=r[1];n[i].hasOwnProperty(o)||(n[i][o]=a,delete e[i][o])}))}else n[i]=o,delete e[i]})),[e].concat([n],k()(t))}),[]));case 6:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function St(t){var e={};return t._isOverride&&t.extends&&"string"!=typeof t.extends&&(e=St(t.extends)),["computed","methods"].forEach((function(n){var r=t[n];r&&Object.entries(r).forEach((function(r){var i=f()(r,2),o=i[0],a=i[1];"computed"===n&&"object"===E()(a)?Object.entries(a).forEach((function(r){var i=f()(r,2),a=i[0],c=i[1],s="".concat(o,".").concat(a);e=xt(e,s,c,n,t)})):e=xt(e,o,a,n,t)}))})),e}function xt(t,e,n,r,i){var o="function"==typeof n&&n.toString();if(!(o&&/\.\$super/g.test(o)))return t;t.hasOwnProperty(e)||(t[e]={});var a=_t(i)?"#":"";return t[e]=Rt(i,e,r,a),t}function Pt(t,e){var n={$super:function(t){this._initVirtualCallStack(t);var e=this._findInSuperRegister(t),n=e[this._virtualCallStack[t]];this._virtualCallStack[t]=n.parent;for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];var a=n.func.bind(this).apply(void 0,i);return n.parent&&(this._virtualCallStack[t]=this._inheritedFrom()),a},_initVirtualCallStack:function(t){this._virtualCallStack||(this._virtualCallStack={name:t}),this._virtualCallStack[t]||(this._virtualCallStack[t]=this._inheritedFrom())},_findInSuperRegister:function(t){return this._superRegistry()[t]},_superRegistry:function(){return e},_inheritedFrom:function(){return t}};return n}function Rt(t,e){var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"methods",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=t.extends;if(!a||"string"==typeof a)return{};var c="".concat(o).concat(null!==(n=a.name)&&void 0!==n?n:""),s="object"===E()(a.extends)&&a.extends?"".concat(o).concat(null!==(r=a.extends.name)&&void 0!==r?r:""):null;if(c===s){(o.length>0||a._isOverride)&&(o="#".concat(o));var u=a&&a.extends&&"string"!=typeof a.extends&&a.extends.name,l="string"==typeof u?u:"";s="".concat(o).concat(l)}var f=Ct(a,e,i),h={};h[c]={parent:s,func:f};var p=Rt(a,e,i,o),d=ft(ft({},p),h);return d}function Ct(t,e,n){var r,i=e.split(".");return i.length>1?function(t,e,n){var r=f()(e,2),i=r[0],o=r[1];if(!t[n])return Ct(t.extends,i,n);if(!t[n][i])return Ct(t.extends,i,n);return t[n][i][o]}(t,i,n):null!==(r=t[n])&&void 0!==r&&r[e]?t[n][e]:t.extends?Ct(t.extends,e,n):null}function _t(t){return!(!t.extends||"string"==typeof t.extends)&&t.extends.name===t.name}function At(t){return 0!==Object.keys(t).length&&t.constructor===Object}var Bt={addEntityDefinition:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t.length)return!1;return Tt.set(t,e),!0},getEntityDefinition:Lt,getDefinitionRegistry:function(){return Tt},getRawEntityObject:Dt,getPropertyBlacklist:Ft,getRequiredProperties:function(t){if(!Tt.has(t))return[];var e=Tt.get(t),n=["createdAt","updatedAt","uploadedAt","childCount","versionId","links","extensions","mimeType","fileExtension","metaData","fileSize","fileName","mediaType","mediaFolder"],r=[];return e.required.forEach((function(t){n.includes(t)||r.push(t)})),r},getAssociatedProperties:function(t){var e=Tt.get(t);return Object.keys(e.properties).reduce((function(t,n){var r=e.properties[n];return"array"===r.type&&Object(p.g)(r,"entity")&&t.push(n),t}),[])},getTranslatableProperties:function(t){if(!Tt.has(t))return[];return Tt.get(t).translatable}},Tt=new Map;function Lt(t){return Tt.get(t)}function Dt(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=t.properties,r={};return Object.keys(n).forEach((function(t){var i=n[t];r[t]=Nt(i,e)})),r}function Nt(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return"boolean"===t.type?null:"string"===t.type?"":"number"===t.type||"integer"===t.type?null:"array"===t.type?[]:"object"===t.type&&t.entity?!0===e?Dt(Lt(t.entity),!1):{}:"object"===t.type?!0===e&&t.properties?Dt(t,!1):{}:"string"===t.type&&"date-time"===t.format?"":null}function Ft(){return["createdAt","updatedAt","uploadedAt","childCount","versionId","links","extensions","mimeType","fileExtension","metaData","fileSize","fileName","mediaType","mediaFolder"]}var It=function(){function t(){i()(this,t)}return a()(t,[{key:"_registerProperty",value:function(t,e){return Object.defineProperty(this,t,{value:e,writable:!1,enumerable:!0,configurable:!0}),this}},{key:"_registerPrivateProperty",value:function(t,e){return Object.defineProperty(this,t,{value:e,writable:!1,enumerable:!0,configurable:!0}),this}},{key:"_registerGetterMethod",value:function(t,e,n){return Object.defineProperty(this,t,{get:e,set:n,enumerable:!0,configurable:!0}),this}}]),t}();function Ht(){return new It}var Mt=function(t){if(t)return Shopware.Application.getContainer("service")[t];var e={get:function(t){return Shopware.Application.getContainer("service")[t]},list:function(){return Shopware.Application.getContainer("service").$list()},register:function(t,e){return Shopware.Application.addServiceProvider(t,e)},registerMiddleware:function(){var t;return(t=Shopware.Application).addServiceProviderMiddleware.apply(t,arguments)},registerDecorator:function(){var t;return(t=Shopware.Application).addServiceProviderDecorator.apply(t,arguments)}};return e},Wt=function(t,e){return(n=function(){var e=this;Object.entries(t).forEach((function(t){var n=f()(t,2),r=n[0],i=n[1];Object.defineProperty(e,r,{value:i,configurable:!0,enumerable:!0,writable:!0})}))}).prototype=e,new n;var n},Ut={register:function(t,e){if(!t||!t.length)return Object(h.b)("MixinFactory","A mixin always needs a name.",e),!1;if(Gt.has(t))return Object(h.b)("MixinFactory",'The mixin "'.concat(t,'" is already registered. Please select a unique name for your mixin.'),e),!1;return Gt.set(t,e),e},getByName:function(t){if(!Gt.has(t))throw new Error('The mixin "'.concat(t,'" is not registered.'));return Gt.get(t)},getMixinRegistry:function(){return Gt}},Gt=new Map;var Jt={getRegistry:function(){return zt},register:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:qt;if(!t||!t.length)return Object(h.b)(Vt,"A filter always needs a name"),!1;if(zt.has(t))return Object(h.b)(Vt,'The filter "'.concat(t,'" is already registered. Please select a unique name for your filter.')),!1;return zt.set(t,e),!0},getByName:function(t){return zt.get(t)}},zt=new Map,qt=function(){},Vt="FilterFactory";var $t={registerDirective:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t.length)return Object(h.b)("DirectiveFactory","A directive always needs a name.",e),!1;if(Kt.has(t))return Object(h.b)("DirectiveFactory","A directive with the name ".concat(t," already exists."),e),!1;return Kt.set(t,e),!0},getDirectiveByName:function(t){return Kt.get(t)},getDirectiveRegistry:function(){return Kt}},Kt=new Map;var Yt=n("aGfj"),Xt={getLocaleByName:function(t){return Zt.get(t)||!1},getLocaleRegistry:function(){return Zt},register:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t.length)return Object(h.b)("LocaleFactory","A locale always needs a name"),!1;if(t.split("-").length<2)return Object(h.b)("LocaleFactory",'The locale name should follow the RFC-4647 standard e.g. [languageCode-countryCode] for example "en-US"'),!1;if(Zt.has(t))return Object(h.b)("LocaleFactory",'The locale "'.concat(t,'" is registered already.'),"Please use the extend method to extend and override certain keys"),!1;return Zt.set(t,e),t},extend:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t.split("-").length<2)return Object(h.b)("LocaleFactory",'The locale name should follow the RFC-4647 standard e.g. [languageCode-countryCode]] for example "en-US"'),!1;if(!Zt.has(t))return Object(h.b)("LocaleFactory",'The locale "'.concat(t,"\" doesn't exists. Please use the register method to register a new locale")),!1;var n=Zt.get(t);return Zt.set(t,Yt.e.merge(n,e)),t},getBrowserLanguage:te,getBrowserLanguages:ee,getLastKnownLocale:function(){var t=te();null!==window.localStorage.getItem(Qt)&&(t=window.localStorage.getItem(Qt));return t},storeCurrentLocale:function(t){if("object"===("undefined"==typeof document?"undefined":E()(document))){var e,n=t.split("-")[0];null===(e=document.querySelector("html"))||void 0===e||e.setAttribute("lang",n)}return window.localStorage.setItem(Qt,t),t}},Zt=new Map,Qt="sw-admin-locale";function te(){var t=new Map;Zt.forEach((function(e,n){var r=n.split("-")[0];t.set(r.toLowerCase(),n)}));var e=null;return ee().forEach((function(n){!e&&Zt.has(n)&&(e=n),!e&&t.has(n)&&(e=t.get(n)||null)})),e||"en-GB"}function ee(){var t,e=[];return navigator.language&&e.push(navigator.language),null!==(t=navigator.languages)&&void 0!==t&&t.length&&navigator.languages.forEach((function(t){e.push(t)})),navigator.userLanguage&&e.push(navigator.userLanguage),navigator.systemLanguage&&e.push(navigator.systemLanguage),e}var ne={getPathByCombination:function(t){if(!re.has(t))return!1;return re.get(t)},getShortcutRegistry:function(){return re},register:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!t||!t.length)return Object(h.b)("ShortcutFactory","A combination can't be blank."),!1;if(re.has(t))return Object(h.b)("ShortcutFactory",'The combination "'.concat(t,'" is registered already.')),!1;return re.set(t,e),t}},re=new Map;var ie={addBootPromise:function(){var t;return oe.push(new Promise((function(e){t=e}))),t},getBootPromises:function(){return oe}},oe=[];var ae={getRegistry:function(){return ce},register:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!t||!t.length)return Object(h.b)(se,"A apiService always needs a name"),!1;if(ue(t))return Object(h.b)(se,'The apiService "'.concat(t,'" is already registered. Please select a unique name for your apiService.')),!1;return ce.set(t,e),!0},getByName:function(t){return ce.get(t)},getServices:function(){return Array.from(ce).reduce((function(t,e){var n=f()(e,2),r=n[0],i=n[1];return t[r]=i,t}),{})},has:ue},ce=new Map,se="ApiServiceFactory";function ue(t){return ce.has(t)}var le=["uuid","int","text","password","float","string","blob","boolean","date"],fe=["json_list","json_object"];var he=function(){function t(e){var n=e.entity,r=e.properties;i()(this,t),this.entity=n,this.properties=r}return a()(t,[{key:"getEntity",value:function(){return this.entity}},{key:"getPrimaryKeyFields",value:function(){return this.filterProperties((function(t){return!0===t.flags.primary_key}))}},{key:"getAssociationFields",value:function(){return this.filterProperties((function(t){return"association"===t.type}))}},{key:"getToManyAssociations",value:function(){return this.filterProperties((function(t){return"association"===t.type&&["one_to_many","many_to_many"].includes(t.relation)}))}},{key:"getToOneAssociations",value:function(){return this.filterProperties((function(t){return"association"===t.type&&["one_to_one","many_to_one"].includes(t.relation)}))}},{key:"getTranslatableFields",value:function(){var t=this;return this.filterProperties((function(e){return t.isTranslatableField(e)}))}},{key:"getRequiredFields",value:function(){return this.filterProperties((function(t){return!0===t.flags.required}))}},{key:"filterProperties",value:function(t){var e=this;if("function"!=typeof t)return{};var n={};return Object.keys(this.properties).forEach((function(r){!0===t(e.properties[r])&&(n[r]=e.properties[r])})),n}},{key:"getField",value:function(t){return this.properties[t]}},{key:"forEachField",value:function(t){var e=this;"function"==typeof t&&Object.keys(this.properties).forEach((function(n){t(e.properties[n],n,e.properties)}))}},{key:"isScalarField",value:function(t){return le.includes(t.type)}},{key:"isJsonField",value:function(t){return fe.includes(t.type)}},{key:"isJsonObjectField",value:function(t){return"json_object"===t.type}},{key:"isJsonListField",value:function(t){return"json_list"===t.type}},{key:"isToManyAssociation",value:function(t){return"association"===t.type&&["one_to_many","many_to_many"].includes(t.relation)}},{key:"isToOneAssociation",value:function(t){return"association"===t.type&&["many_to_one","one_to_one"].includes(t.relation)}},{key:"isTranslatableField",value:function(t){return("string"===t.type||"text"===t.type)&&!0===t.flags.translatable}}]),t}(),pe={getScalarTypes:function(){return le},getJsonTypes:function(){return fe},getDefinitionRegistry:function(){return de},has:function(t){return de.has(t)},get:ve,add:function(t,e){de.set(t,new he(e))},remove:function(t){return de.delete(t)},getTranslatedFields:function(t){return ve(t).getTranslatableFields()},getAssociationFields:function(t){return ve(t).getAssociationFields()},getRequiredFields:function(t){return ve(t).getRequiredFields()}},de=new Map;function ve(t){var e=de.get(t);if(void 0===e)throw new Error("[EntityDefinitionRegistry] No definition found for entity type ".concat(t));return e}function ge(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ye(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ge(Object(n),!0).forEach((function(e){s()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ge(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var me={getRegistry:ke,register:function(t,e){if(!t||!t.length)return!1;if(be.has(t))return!1;if(!je(e))return!1;return be.set(t,e),!0},remove:function(t){if(!t||!t.length)return!1;if(!be.has(t))return!1;return be.delete(t),!0},override:function(t,e){if(!be.has(t))return!1;if(!je(e))return!1;return be.set(t,e),!0},initialize:function(){if(Oe)return we;return Oe=!0,ke().forEach((function(t){var e=t.fn,n=t.name;we.use(function(t,e){return function(n,r){var i=r.queue.find((function(e){return e.name===t}))||null,o=ye(ye({},r),{entry:i,name:t});null===i?n():e.call(null,n,o)}}(n,e))})),we},resetHelper:function(){return we=new v,Oe=!1,!0}},be=new Map,we=new v,Oe=!1;function ke(){return be}function je(t){return Object(p.g)(t,"name")&&t.name.length>0&&Object(p.g)(t,"fn")&&d.a.isFunction(t.fn)}var Ee=n("bNBJ"),Se=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.code,r=e.meta,o=void 0===r?{}:r,a=e.status,c=void 0===a?"":a,s=e.detail,u=void 0===s?"":s;if(i()(this,t),"string"!=typeof n||""===n)throw new Error("[ShopwareError] can not identify error by code");this._id=Yt.b.createId(),this._code=n,this._parameters=o.parameters,this._status=c,this._detail=u}return a()(t,[{key:"id",get:function(){return this._id}},{key:"code",get:function(){return this._code},set:function(t){this._code=t}},{key:"parameters",get:function(){return this._parameters},set:function(t){this._parameters=t}},{key:"status",get:function(){return this._status},set:function(t){this._status=t}},{key:"detail",get:function(){return this._detail},set:function(t){this._trace=t}}]),t}(),xe=n("2iSf"),Pe=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return 0},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;i()(this,t),this._registeredNodes=new Map,this._defaultPosition=n,this._sorting=e}return a()(t,[{key:"convertToTree",value:function(){return this._tree(this._registeredNodes)}},{key:"_tree",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0,i=[];return t.forEach((function(o){if(o.parent===r){o.level=n;var a=o.id||o.path;o.children=e._tree(t,n+1,a),i.push(o)}})),i.sort(this._sorting)}},{key:"add",value:function(t){var e=t.id||t.path;return e?(Object(p.g)(t,"link")&&!Object(p.g)(t,"target")&&(t.target="_self"),t.position||(t.position=this._defaultPosition),this._registeredNodes.has(e)?(Object(h.b)("FlatTree","Tree contains node with unique identifier ".concat(e," already."),"Please remove it first before adding a new one.",this._registeredNodes.get(e)),this):(this._registeredNodes.set(e,t),this)):(Object(h.b)("FlatTree",'The node needs an "id" or "path" property. Abort registration.',t),this)}},{key:"remove",value:function(t){return this._registeredNodes.delete(t),this}},{key:"getRegisteredNodes",value:function(){return this._registeredNodes}},{key:"defaultPosition",get:function(){return this._defaultPosition},set:function(t){this._defaultPosition=t}}]),t}(),Re=Pe,Ce=n("Eagv");function _e(){this.listeners=[],window.addEventListener("resize",this.resize.bind(this))}_e.prototype=Object.assign(_e.prototype,{resize:Yt.b.debounce((function(t){this.listeners.forEach((function(e){e.listener.call(e.scope,t)}))}),100),onResize:function(t){var e=t.listener,n=t.scope,r=t.component;return n||(n=window),this.listeners.push({listener:e,scope:n,component:r}),this.listeners.length-1},removeResizeListener:function(t){return this.listeners=this.listeners.filter((function(e){return t!==e.component})),!0},getUserAgent:function(){return window.navigator.userAgent},getViewportWidth:function(){return window.innerWidth},getViewportHeight:function(){return window.innerHeight},getDevicePixelRatio:function(){return window.devicePixelRatio},getScreenWidth:function(){return window.screen.width},getScreenHeight:function(){return window.screen.height},getScreenOrientation:function(){return window.screen.orientation},getBrowserLanguage:function(){return window.navigator.language},getPlatform:function(){return window.navigator.platform},getSystemKey:function(){return this.getPlatform().indexOf("Mac")>-1?"CTRL":"ALT"}});var Ae=_e,Be=n("E+Sq"),Te=n("gJ+d"),Le=n("QjUk");Object(Le.a)((function(t,e,n){Shopware.Application.view.setReactive(t,e,n)}));var De=Le.b,Ne=n("DPbZ"),Fe=function(){function t(){i()(this,t)}return a()(t,[{key:"create",value:function(t,e,n){var r=this;e=e||Yt.b.createId();var i=Shopware.EntityDefinition.get(t);if(!i)return Object(h.b)("Entity factory","No schema found for entity ".concat(t)),null;var o={extensions:{}},a=i.getToManyAssociations();Object.keys(a).forEach((function(i){var c=a[i].entity;a[i].flags.extension?o.extensions[i]=r.createCollection(t,"".concat(e,"/extensions"),i,c,n):o[i]=r.createCollection(t,e,i,c,n)}));var c=new De(e,t,o);return c.markAsNew(),c}},{key:"createCollection",value:function(t,e,n,r,i){var o=n.replace(/_/g,"-"),a=t.replace(/_/g,"-"),c="/".concat(a,"/").concat(e,"/").concat(o);return new Ne.a(c,r,i,new Te.a(1,10))}}]),t}(),Ie=function(){function t(){i()(this,t),s()(this,"cache",{})}return a()(t,[{key:"hydrateSearchResult",value:function(t,e,n,r,i){var o,a=this;this.cache={};var c=[];return n.data.data.forEach((function(t){var o=a.hydrateEntity(e,t,n.data,r,i);null!==o&&c.push(o)})),new Ne.a(t,e,r,i,c,null===(o=n.data.meta)||void 0===o?void 0:o.total,n.data.aggregations)}},{key:"hydrate",value:function(t,e,n,r,i){var o=this;this.cache={};var a=new Ne.a(t,e,r,i);return n.data.forEach((function(t){var c=o.hydrateEntity(e,t,n,r,i);null!==c&&a.add(c)})),a}},{key:"hydrateEntity",value:function(t,e,n,r,i){var o=this;if(!e)return null;var a=e.id,c="".concat(t,"-").concat(a);if(this.cache[c])return this.cache[c];var s=Shopware.EntityDefinition.get(t);if(!s)return null;var u=e.attributes;u.id=a,Object.entries(u).forEach((function(t){var e=f()(t,2),n=e[0],r=e[1],i=s.getField(n);if(i&&s.isJsonField(i))if(Array.isArray(r)&&r.length<=0&&s.isJsonObjectField(i))u[n]={};else{var o=!Array.isArray(r)&&"object"===E()(r)&&null!==r&&Object.keys(r).length<=0;s.isJsonListField(i)&&(o||null===r)&&(u[n]=[])}})),Object.keys(e.relationships).forEach((function(t){var c=e.relationships[t];"extensions"===t&&(u[t]=o.hydrateExtensions(a,c,s,n,r,i));var l=s.properties[t];if(!l)return!0;if(s.isToManyAssociation(l))return u[t]=o.hydrateToMany(i,t,c,l.entity,r,n),!0;if(s.isToOneAssociation(l)&&d.a.isObject(c.data)){var f=o.hydrateToOne(i,t,c,n,r);f&&(u[t]=f)}return!0}));var l=new De(a,t,u);return this.cache[c]=l,l}},{key:"hydrateToOne",value:function(t,e,n,r,i){var o=this.getAssociationCriteria(t,e),a=this.getIncluded(n.data.type,n.data.id,r);return this.hydrateEntity(n.data.type,a,r,i,o)}},{key:"getAssociationCriteria",value:function(t,e){return t.hasAssociation(e)?t.getAssociation(e):new Te.a(1,25)}},{key:"hydrateToMany",value:function(t,e,n,r,i,o){var a,c=this,s=this.getAssociationCriteria(t,e),u=null!==(a=null==i?void 0:i.apiResourcePath)&&void 0!==a?a:"",l=n.links.related.substr(n.links.related.indexOf(u)+u.length),f=new Ne.a(l,r,i,s);return null===n.data||n.data.forEach((function(t){var e=c.getIncluded(t.type,t.id,o),n=c.hydrateEntity(t.type,e,o,i,s);n&&f.add(n)})),f}},{key:"getIncluded",value:function(t,e,n){return n.included.find((function(n){return n.id===e&&n.type===t}))}},{key:"hydrateExtensions",value:function(t,e,n,r,i,o){var a=this,c=this.getIncluded("extension",t,r),s=Object.assign({},c.attributes);return Object.keys(c.relationships).forEach((function(t){var e=c.relationships[t],u=n.properties[t];if(!u)return!0;if(n.isToManyAssociation(u))return s[t]=a.hydrateToMany(o,t,e,u.entity,i,r),!0;if(n.isToOneAssociation(u)&&d.a.isObject(e.data)){var l=a.hydrateToOne(o,t,e,r,i);l&&(s[t]=l)}return!0})),s}}]),t}();function He(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Me(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?He(Object(n),!0).forEach((function(e){s()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):He(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function We(){We=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function u(t,e,n,i){var o=e&&e.prototype instanceof h?e:h,a=Object.create(o.prototype),c=new x(i||[]);return r(a,"_invoke",{value:O(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var f={};function h(){}function p(){}function d(){}var v={};s(v,o,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(P([])));y&&y!==e&&n.call(y,o)&&(v=y);var m=d.prototype=h.prototype=Object.create(v);function b(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function i(r,o,a,c){var s=l(t[r],t,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==E()(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){i("next",t,a,c)}),(function(t){i("throw",t,a,c)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return i("throw",t,a,c)}))}c(s.arg)}var o;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){i(t,n,e,r)}))}return o=o?o.then(r,r):r()}})}function O(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return R()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=k(a,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=l(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function k(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var i=l(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,f;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function P(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:R}}function R(){return{value:void 0,done:!0}}return p.prototype=d,r(m,"constructor",{value:d,configurable:!0}),r(d,"constructor",{value:p,configurable:!0}),p.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},b(w.prototype),s(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new w(u(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(m),s(m,c,"Generator"),s(m,o,(function(){return this})),s(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=P,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;S(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:P(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},t}var Ue=function(){function t(e,n,r,o,a,c,u,l){i()(this,t),s()(this,"route",void 0),s()(this,"entityName",void 0),s()(this,"httpClient",void 0),s()(this,"hydrator",void 0),s()(this,"changesetGenerator",void 0),s()(this,"entityFactory",void 0),s()(this,"errorResolver",void 0),s()(this,"options",void 0),this.route=e,this.entityName=n,this.httpClient=r,this.hydrator=o,this.changesetGenerator=a,this.entityFactory=c,this.errorResolver=u,this.options=l}var e,n,r;return a()(t,[{key:"schema",get:function(){return Shopware.EntityDefinition.get(this.entityName)}},{key:"searchIds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,n=this.buildHeaders(e),r="/search-ids".concat(this.route);return this.httpClient.post(r,t.parse(),{headers:n}).then((function(t){return t.data}))}},{key:"search",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,r=this.buildHeaders(n),i="/search".concat(this.route);return this.httpClient.post(i,t.parse(),{headers:r}).then((function(r){return e.hydrator.hydrateSearchResult(e.route,e.entityName,r,n,t)}))}},{key:"get",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return(n=n||new Te.a(1,1)).setIds([t]),this.search(n,e).then((function(e){return e.get(t)}))}},{key:"save",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api;return!0===this.options.useSync?this.saveWithSync(t,e):this.saveWithRest(t,e)}},{key:"saveWithRest",value:(r=st()(We().mark((function t(e,n){var r,i,o;return We().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.changesetGenerator.generate(e),i=r.changes,o=r.deletionQueue,this.options.keepApiErrors){t.next=4;break}return t.next=4,this.errorResolver.resetApiErrors();case 4:return t.next=6,this.sendDeletions(o,n);case 6:return t.abrupt("return",this.sendChanges(e,i,n));case 7:case"end":return t.stop()}}),t,this)}))),function(t,e){return r.apply(this,arguments)})},{key:"saveWithSync",value:(n=st()(We().mark((function t(e,n){var r,i,o,a,c,s=this;return We().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.changesetGenerator.generate(e),i=r.changes,o=r.deletionQueue,e.isNew()&&Object.assign(i||{},{id:e.id}),a=[],o.length>0&&a.push.apply(a,k()(this.buildDeleteOperations(o))),null!==i&&a.push({key:"write",action:"upsert",entity:e.getEntityName(),payload:[i]}),(c=this.buildHeaders(n))["single-operation"]=!0,!(a.length<=0)){t.next=9;break}return t.abrupt("return",Promise.resolve());case 9:if(this.options.keepApiErrors){t.next=12;break}return t.next=12,this.errorResolver.resetApiErrors();case 12:return t.abrupt("return",this.httpClient.post("_action/sync",a,{headers:c}).catch((function(t){var n,r,o,a=[];throw(null!==(n=null==t||null===(r=t.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.errors)&&void 0!==n?n:[]).forEach((function(t){t.source.pointer.startsWith("/write/")&&(t.source.pointer=t.source.pointer.substring(6),a.push(t))})),s.errorResolver.handleWriteErrors({errors:a},[{entity:e,changes:i}]),t})));case 13:case"end":return t.stop()}}),t,this)}))),function(t,e){return n.apply(this,arguments)})},{key:"clone",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,n=arguments.length>2?arguments[2]:void 0;return t?this.httpClient.post("/_action/clone".concat(this.route,"/").concat(t),n,{headers:this.buildHeaders(e)}).then((function(t){return t.data})):Promise.reject(new Error("Missing required argument: id"))}},{key:"hasChanges",value:function(t){var e=this.changesetGenerator.generate(t),n=e.changes,r=e.deletionQueue;return null!==n||r.length>0}},{key:"saveAll",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,r=[];return t.forEach((function(t){r.push(e.save(t,n))})),Promise.all(r)}},{key:"sync",value:(e=st()(We().mark((function t(e){var n,r,i,o,a,c=arguments;return We().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=c.length>1&&void 0!==c[1]?c[1]:Shopware.Context.api,r=!(c.length>2&&void 0!==c[2])||c[2],i=this.getSyncChangeset(e),o=i.changeset,a=i.deletions,this.options.keepApiErrors){t.next=6;break}return t.next=6,this.errorResolver.resetApiErrors();case 6:return t.next=8,this.sendDeletions(a,n);case 8:return t.abrupt("return",this.sendUpserts(o,r,n));case 9:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"discard",value:function(t){if(t){var e=this.changesetGenerator.generate(t).changes;if(e){var n=t.getOrigin();Object.keys(e).forEach((function(e){t[e]=n[e]}))}}}},{key:"sendUpserts",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Shopware.Context.api;if(t.length<=0)return Promise.resolve();var i=t.map((function(t){return t.changes})),o=this.buildHeaders(r);return o["fail-on-error"]=e,this.httpClient.post("_action/sync",s()({},this.entityName,{entity:this.entityName,action:"upsert",payload:i}),{headers:o}).then((function(t){var e=t.data;if(!1===e.success)throw e;return Promise.resolve()})).catch((function(e){var r=n.getSyncErrors(e);throw n.errorResolver.handleWriteErrors({errors:r},t),e}))}},{key:"getSyncErrors",value:function(t){var e,n;if(Shopware.Feature.isActive("FEATURE_NEXT_15815")){var r,i,o,a=null!==(r=null==t||null===(i=t.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.errors)&&void 0!==r?r:[];return a.forEach((function(t){if(t.source&&t.source.pointer){var e=t.source.pointer.split("/");""===e[0]&&e.shift(),e.shift(),t.source.pointer=e.join("/")}})),a}var c=null==t||null===(e=t.response)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.data[this.entityName];return c?c.result.reduce((function(t,e){return t.push.apply(t,k()(e.errors)),t}),[]):[]}},{key:"getSyncChangeset",value:function(t){var e=this;return t.reduce((function(t,n){var r,i=e.changesetGenerator.generate(n),o=i.changes,a=i.deletionQueue;if((r=t.deletions).push.apply(r,k()(a)),null===o)return t;var c=e.changesetGenerator.getPrimaryKeyData(n);return Object.assign(o,c),t.changeset.push({entity:n,changes:o}),t}),{changeset:[],deletions:[]})}},{key:"assign",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,n=this.buildHeaders(e);return this.httpClient.post("".concat(this.route),{id:t},{headers:n})}},{key:"delete",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,r=this.buildHeaders(n),i="".concat(this.route,"/").concat(t);return this.httpClient.delete(i,{headers:r}).catch((function(n){var r,i,o,a=null==n||null===(r=n.response)||void 0===r||null===(i=r.data)||void 0===i||null===(o=i.errors)||void 0===o?void 0:o.map((function(n){return{error:n,id:t,entityName:e.entityName}}));throw e.errorResolver.handleDeleteError(a),n}))}},{key:"iterateIds",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Shopware.Context.api;return null===t.getLimit()&&t.setLimit(50),t.setTotalCountMode(1),this.searchIds(t,r).then((function(r){var i=r.data;return i.length<=0?Promise.resolve():e(i).then((function(){return i.length<t.getLimit()?Promise.resolve():(t.setPage(t.getPage()+1),n.iterateIds(t,e))}))}))}},{key:"syncDeleted",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,r=this.buildHeaders(n);r["fail-on-error"]=!0;var i=t.map((function(t){return{id:t}}));return this.httpClient.post("_action/sync",s()({},this.entityName,{entity:this.entityName,action:"delete",payload:i}),{headers:r}).then((function(t){var e=t.data;if(!1===e.success)throw e;return Promise.resolve()})).catch((function(n){var r,i,o=null==n||null===(r=n.response)||void 0===r||null===(i=r.data)||void 0===i?void 0:i.data[e.entityName].result;if(o){var a=o.reduce((function(n,r,i){return r.errors&&r.errors.forEach((function(r){n.push({error:r,entityName:e.entityName,id:t[i]})})),n}),[]);throw e.errorResolver.handleDeleteError(a),n}}))}},{key:"create",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Shopware.Context.api,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.entityFactory.create(this.entityName,e,t)}},{key:"createVersion",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=this.buildHeaders(e),o={};n&&(o.versionId=n),r&&(o.versionName=r);var a="_action/version/".concat(this.entityName.replace(/_/g,"-"),"/").concat(t);return this.httpClient.post(a,o,{headers:i}).then((function(t){return Me(Me({},e),{versionId:t.data.versionId})}))}},{key:"mergeVersion",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,n=this.buildHeaders(e),r="_action/version/merge/".concat(this.entityName.replace(/_/g,"-"),"/").concat(t);return this.httpClient.post(r,{},{headers:n})}},{key:"deleteVersion",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Shopware.Context.api,r=this.buildHeaders(n),i="/_action/version/".concat(e,"/").concat(this.entityName.replace(/_/g,"-"),"/").concat(t);return this.httpClient.post(i,{},{headers:r})}},{key:"sendChanges",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Shopware.Context.api,i=this.buildHeaders(r);return t.isNew()?(e=e||{},Object.assign(e,{id:t.id}),this.httpClient.post("".concat(this.route),e,{headers:i}).catch((function(r){var i,o,a=null==r||null===(i=r.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.errors;if(a)throw n.errorResolver.handleWriteErrors({errors:a},[{entity:t,changes:e}]),r}))):null==e?Promise.resolve():this.httpClient.patch("".concat(this.route,"/").concat(t.id),e,{headers:i}).catch((function(r){var i,o,a=null==r||null===(i=r.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.errors;if(a)throw n.errorResolver.handleWriteErrors({errors:a},[{entity:t,changes:e}]),r}))}},{key:"sendDeletions",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Shopware.Context.api,r=this.buildHeaders(n),i=t.map((function(t){return e.httpClient.delete("".concat(t.route,"/").concat(t.key),{headers:r}).catch((function(t){throw e.errorResolver.handleDeleteError(t),t}))}));return Promise.all(i)}},{key:"buildHeaders",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Shopware.Context.api,e=Shopware.Utils.object.hasOwnProperty,n=!e(this.options,"compatibility")||this.options.compatibility,r=!!e(this.options,"sw-app-integration-id")&&this.options["sw-app-integration-id"],i={Accept:"application/vnd.api+json",Authorization:"Bearer ".concat(t.authToken.access),"Content-Type":"application/json","sw-api-compatibility":n};return t.languageId&&(i=Object.assign({"sw-language-id":t.languageId},i)),t.currencyId&&(i=Object.assign({"sw-currency-id":t.currencyId},i)),t.versionId&&(i=Object.assign({"sw-version-id":t.versionId},i)),t.inheritance&&(i=Object.assign({"sw-inheritance":t.inheritance},i)),r&&(i=Object.assign({"sw-app-integration-id":r},i)),i}},{key:"buildDeleteOperations",value:function(t){var e={};t.forEach((function(t){var n=t.entity;n&&(e.hasOwnProperty(n)||(e[n]=[]),e[n].push(t.primary))}));var n=[];return Object.keys(e).forEach((function(t){var r=e[t];n.push({action:"delete",payload:r,entity:t})})),n}}]),t}(),Ge=n("2aLV");function Je(t,e,n){var r=t.split("."),i=r.pop();return{store:r.reduce((function(t,e){return t.hasOwnProperty(e)||n(t,e,{}),t[e]}),e),field:i}}var ze={addApiError:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Object.defineProperty;if("function"!=typeof r)throw new Error("[ErrorStore] createApiError: setReactive is not a function");var i=Je(t,n.api,r),o=i.store,a=i.field;r(o,a,e)},removeApiError:function t(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=e.split("."),o=i.pop(),a=i.reduce((function(t,e){return null!=t&&t[e]?t[e]:null}),n.api);null!==a&&("function"==typeof r?r(a,o):delete a.field,i.length<=0||Yt.g.isEmpty(a)&&t(i.join("."),n,r))},resetApiErrors:function(t){t.api={}},addSystemError:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Object.defineProperty;if("function"!=typeof r)throw new Error("[ErrorStore] createApiError: setReactive is not a function");r(n.system,e,t)},removeSystemError:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;"function"==typeof n?n(e.system,t):delete e.system[t]}};function qe(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}var Ve=function(){function t(){i()(this,t),this.STRING_FILTER_INPUT="string-filter",this.NUMBER_FILTER_INPUT="number-filter",this.DATE_FILTER_INPUT="date-filter",this.ASSOCIATION_FILTER_INPUT="multi-select-filter",this.BOOLEAN_FILTER_INPUT="boolean-filter",this.PRICE_FILTER_INPUT="price-filter",this.EXISTENCE_FILTER_INPUT="existence-filter"}return a()(t,[{key:"create",value:function(t,e){var n=this;return Object.entries(e).map((function(e){var r=f()(e,2),i=r[0],o=r[1];o.name=i;var a=n.getFilterProperties(t,o.property);if(o.type||!a)return o;switch(o.schema=a,a.type){case"string":default:o.type=n.STRING_FILTER_INPUT;break;case"int":o.type=n.NUMBER_FILTER_INPUT;break;case"date":o.type=n.DATE_FILTER_INPUT;break;case"association":o.type="many_to_many"===a.relation||"many_to_one"===a.relation?n.ASSOCIATION_FILTER_INPUT:n.EXISTENCE_FILTER_INPUT;break;case"boolean":o.type=n.BOOLEAN_FILTER_INPUT}return"price"===o.property&&(o.type=n.NUMBER_FILTER_INPUT),o}))}},{key:"getFilterProperties",value:function(t,e){var n=Shopware.EntityDefinition.get(t).properties,r=e.split("."),i=r.shift(),o=n[i];if(!o)throw new Error("No definition found for property ".concat(i));if(r.length>0&&o.entity)return this.getFilterProperties(o.entity,r.join("."));var a=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?qe(Object(n),!0).forEach((function(e){s()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):qe(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},o);return"uuid"===o.type&&Object.keys(n).forEach((function(t){"association"===n[t].type&&n[t].localField===i&&(a=n[t])})),a}}]),t}(),$e={ChangesetGenerator:Be.a,Criteria:Te.a,Entity:De,EntityCollection:Ne.a,EntityDefinition:he,EntityFactory:Fe,EntityHydrator:Ie,Repository:Ue,ErrorResolver:Ge.a,ErrorStore:ze,FilterFactory:Ve},Ke=n("oCYn"),Ye=n("SH5M");function Xe(){Xe=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function u(t,e,n,i){var o=e&&e.prototype instanceof h?e:h,a=Object.create(o.prototype),c=new x(i||[]);return r(a,"_invoke",{value:O(t,n,c)}),a}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var f={};function h(){}function p(){}function d(){}var v={};s(v,o,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(P([])));y&&y!==e&&n.call(y,o)&&(v=y);var m=d.prototype=h.prototype=Object.create(v);function b(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function i(r,o,a,c){var s=l(t[r],t,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==E()(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){i("next",t,a,c)}),(function(t){i("throw",t,a,c)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return i("throw",t,a,c)}))}c(s.arg)}var o;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){i(t,n,e,r)}))}return o=o?o.then(r,r):r()}})}function O(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return R()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=k(a,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=l(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function k(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var i=l(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,f;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function P(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:R}}function R(){return{value:void 0,done:!0}}return p.prototype=d,r(m,"constructor",{value:d,configurable:!0}),r(d,"constructor",{value:p,configurable:!0}),p.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},b(w.prototype),s(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new w(u(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(m),s(m,c,"Generator"),s(m,o,(function(){return this})),s(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=P,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;S(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:P(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},t}var Ze=function(){function t(e){var n=this;i()(this,t),s()(this,"$container",void 0),s()(this,"view",void 0),s()(this,"_resolveViewInitialized",void 0),s()(this,"viewInitialized",new Promise((function(t){n._resolveViewInitialized=t})));var r=function(){};this.$container=e,this.view=null,this.$container.service("service",r),this.$container.service("init",r),this.$container.service("factory",r)}var e,n;return a()(t,[{key:"getContainer",value:function(t){return"string"==typeof t&&this.$container.container[t]?this.$container.container[t]:this.$container.container}},{key:"addFactory",value:function(t,e){return this.$container.factory("factory.".concat(t),e.bind(this)),this}},{key:"addFactoryMiddleware",value:function(t,e){return this._addMiddleware("factory",t,e)}},{key:"addFactoryDecorator",value:function(t,e){return this._addDecorator("factory",t,e)}},{key:"addInitializer",value:function(t,e){return this.$container.factory("init.".concat(t),e.bind(this)),this}},{key:"addServiceProvider",value:function(t,e){return this.$container.factory("service.".concat(t),e.bind(this)),this}},{key:"registerConfig",value:function(t){return t.apiContext&&this.registerApiContext(t.apiContext),t.appContext&&this.registerAppContext(t.appContext),this}},{key:"registerApiContext",value:function(t){return Shopware.Context.api=Shopware.Classes._private.ApiContextFactory(t),this}},{key:"registerAppContext",value:function(t){return Shopware.Context.app=Shopware.Classes._private.AppContextFactory(t),this}},{key:"addServiceProviderMiddleware",value:function(t,e){return this._addMiddleware("service",t,e)}},{key:"_addMiddleware",value:function(t,e,n){return"string"==typeof e&&n&&this.$container.middleware("".concat(t,".").concat(e),n),"function"==typeof e&&e&&this.$container.middleware(t,e),this}},{key:"initializeFeatureFlags",value:function(){var t=Shopware.Context.app.features;if(!t)throw new Error("\n                Feature initialization does not work\n                because the context does not contain any features.\n            ");return Shopware.Feature.init(t),this}},{key:"addServiceProviderDecorator",value:function(t,e){return this._addDecorator("service",t,e)}},{key:"_addDecorator",value:function(t,e,n){return"string"==typeof e&&n&&this.$container.decorator("".concat(t,".").concat(e),n),"function"==typeof e&&e&&this.$container.decorator(t,e),this}},{key:"start",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.initState().registerConfig(t).initializeFeatureFlags().startBootProcess()}},{key:"initState",value:function(){if(this.getContainer("init").state)return this;throw new Error("State could not be initialized")}},{key:"getApplicationRoot",value:function(){var t;return!(null===(t=this.view)||void 0===t||!t.root)&&this.view.root}},{key:"setViewAdapter",value:function(t){this.view=t}},{key:"startBootProcess",value:function(){var t=this.getContainer("service").loginService;return t.isLoggedIn()?this.bootFullApplication():(t.logout(!1,!1),this.bootLogin())}},{key:"bootLogin",value:function(){var t=this;return sessionStorage.setItem("sw-login-should-reload","true"),this.initializeLoginInitializer().then((function(){return t.view?t.view.initDependencies():Promise.reject()})).then((function(){return t.createApplicationRoot()})).catch((function(e){return t.createApplicationRootError(e)}))}},{key:"bootFullApplication",value:function(){var t=this,e=this.getContainer("init");return Shopware.Feature.isActive("FEATURE_NEXT_21547")||Object(Ye.b)({page:1,limit:25}),this.initializeInitializers(e).then((function(){return t.loadPlugins()})).then((function(){return Promise.all(Shopware.Plugin.getBootPromises())})).then((function(){return t.view?t.view.initDependencies():Promise.reject()})).then((function(){return t.createApplicationRoot()})).catch((function(e){return t.createApplicationRootError(e)}))}},{key:"createApplicationRoot",value:function(){var t,e,n,r=this.getContainer("init").router.getRouterInstance();return"testing"===Shopware.Context.app.environment?Promise.resolve(this):this.view?(this.view.init("#app",r,this.getContainer("service")),!Shopware.Context.app.firstRunWizard||null!=r&&null!==(t=r.history)&&void 0!==t&&null!==(e=t.current)&&void 0!==e&&null!==(n=e.name)&&void 0!==n&&n.startsWith("sw.first.run.wizard.")||r.push({name:"sw.first.run.wizard.index"}),"function"==typeof this._resolveViewInitialized&&this._resolveViewInitialized(),Promise.resolve(this)):Promise.reject(new Error("The ViewAdapter was not defined in the application."))}},{key:"createApplicationRootError",value:function(t){var e,n,r;console.error(t);var i=this.getContainer("init").router.getRouterInstance();null===(e=this.view)||void 0===e||e.init("#app",i,this.getContainer("service")),null!==(n=this.view)&&void 0!==n&&null!==(r=n.root)&&void 0!==r&&r.initError&&(this.view.root.initError=t),i.push({name:"error"})}},{key:"initializeInitializers",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"init",n=t.$list().map((function(t){return"".concat(e,".").concat(t)}));this.$container.digest(n);var r=this.getAsyncInitializers(t);return Promise.all(r)}},{key:"initializeLoginInitializer",value:function(){var t=["login","baseComponents","locale","apiServices","svgIcons"],e=this.getContainer("init");t.forEach((function(t){e.hasOwnProperty(t)||console.error('The initializer "'.concat(t,'" does not exists'))})),this.$container.digest(t.map((function(t){return"init.".concat(t)})));var n=this.getAsyncInitializers(t);return Promise.all(n)}},{key:"getAsyncInitializers",value:function(t){var e=this.getContainer("init"),n=[];return Object.keys(t).forEach((function(t){var r,i=e[t];"Promise"===(null==i||null===(r=i.constructor)||void 0===r?void 0:r.name)&&n.push(i)})),n}},{key:"loadPlugins",value:(n=st()(Xe().mark((function t(){var e,n,r,i,o,a=this;return Xe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("development"!=(e="production")){t.next=10;break}return t.next=4,fetch("./sw-plugin-dev.json");case 4:return r=t.sent,t.next=7,r.json();case 7:n=t.sent,t.next=11;break;case 10:n=Shopware.Context.app.config.bundles;case 11:return i=Object.values(n).map((function(t){return a.injectPlugin(t)})),o=Shopware.Context.app.config.bundles,Object.entries(o).forEach((function(t){var r=f()(t,2),i=r[0],o=r[1];o.baseUrl&&("development"===e&&Object.entries(n).forEach((function(t){var e=f()(t,2),n=e[0],r=e[1],a=Shopware.Utils.string,c=a.upperFirst(a.camelCase(n));i===c&&r.html&&(o.baseUrl=r.html),o.baseUrl&&(o.baseUrl=new URL(o.baseUrl,window.origin).toString())})),a.injectIframe({active:o.active,integrationId:o.integrationId,bundleName:i,bundleVersion:o.version,iframeSrc:o.baseUrl,bundleType:o.type}))})),"development"===e&&Object.entries(n).forEach((function(t){var e=f()(t,2),n=e[0],r=e[1],i=Shopware.Utils.string,c=i.upperFirst(i.camelCase(n));!Object.keys(o).includes(c)&&r.html&&a.injectIframe({bundleVersion:void 0,bundleName:c,iframeSrc:r.html})})),t.abrupt("return",Promise.all(i));case 16:case"end":return t.stop()}}),t)}))),function(){return n.apply(this,arguments)})},{key:"injectPlugin",value:(e=st()(Xe().mark((function t(e){var n,r,i=this;return Xe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=[],r=[],e.js&&Array.isArray(e.js)?n=e.js.map((function(t){return i.injectJs(t)})):e.js&&n.push(this.injectJs(e.js)),e.css&&Array.isArray(e.css)?r=e.css.map((function(t){return i.injectCss(t)})):e.css&&r.push(this.injectCss(e.css)),t.prev=4,t.next=7,Promise.all([].concat(k()(n),k()(r)));case 7:return t.abrupt("return",t.sent);case 10:return t.prev=10,t.t0=t.catch(4),console.warn("Error while loading plugin",e),t.abrupt("return",null);case 14:case"end":return t.stop()}}),t,this,[[4,10]])}))),function(t){return e.apply(this,arguments)})},{key:"injectJs",value:function(t){return new Promise((function(e,n){var r=document.createElement("script");r.src=t,r.async=!0,r.onload=function(){e()},r.onerror=function(){n()},document.body.appendChild(r)}))}},{key:"injectCss",value:function(t){return new Promise((function(e,n){var r=document.createElement("link");r.rel="stylesheet",r.href=t,r.onload=function(){e()},r.onerror=function(){n()},document.head.appendChild(r)}))}},{key:"injectIframe",value:function(t){var e,n=t.active,r=t.integrationId,i=t.bundleName,o=t.iframeSrc,a=t.bundleVersion,c=t.bundleType,s=Shopware.Context.app.config.bundles,u=null;s&&s.hasOwnProperty(i)&&(u=s[i].permissions);var l={active:n,integrationId:r,name:i,baseUrl:o,version:a,type:c};Ke.a.set(l,"permissions",null!==(e=u)&&void 0!==e?e:Ke.a.observable({})),Shopware.State.commit("extensions/addExtension",l)}}]),t}(),Qe=Ze,tn=n("hFht"),en=n("oJGZ"),nn=function(){function t(e,n,r,o,a){i()(this,t),s()(this,"hydrator",void 0),s()(this,"changesetGenerator",void 0),s()(this,"entityFactory",void 0),s()(this,"httpClient",void 0),s()(this,"errorResolver",void 0),this.hydrator=e,this.changesetGenerator=n,this.entityFactory=r,this.httpClient=o,this.errorResolver=a}return a()(t,[{key:"create",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e||(e="/".concat(t.replace(/_/g,"-")));var r=Shopware.EntityDefinition.get(t);return new Ue(e,r.entity,this.httpClient,this.hydrator,this.changesetGenerator,this.entityFactory,this.errorResolver,n)}}]),t}();function rn(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=Shopware.Defaults,n=!1,r=on(t,n),i="".concat(r,"/api"),o=localStorage.getItem("sw-admin-current-language")||e.systemLanguageId;return Shopware.State.commit("context/setApiInstallationPath",r),Shopware.State.commit("context/setApiApiPath",i),Shopware.State.commit("context/setApiApiResourcePath","".concat(i)),Shopware.State.commit("context/setApiAssetsPath",an(t.assetPath,n)),Shopware.State.commit("context/setApiLanguageId",o),Shopware.State.commit("context/setApiInheritance",!1),n&&(Shopware.State.commit("context/setApiSystemLanguageId",e.systemLanguageId),Shopware.State.commit("context/setApiLiveVersionId",e.versionId)),Object.entries(t).forEach((function(t){var e=f()(t,2),n=e[0],r=e[1];Shopware.State.commit("context/addApiValue",{key:n,value:r})})),Shopware.Context.api}function on(t,e){var n;if(e)return"";var r="";return null!==(n=t.schemeAndHttpHost)&&void 0!==n&&n.length&&(r="".concat(t.schemeAndHttpHost).concat(t.basePath)),r}function an(t,e){return e?"/bundles/":"".concat(t,"/bundles/")}function cn(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Shopware.State.commit("context/setAppEnvironment","production"),Shopware.State.commit("context/setAppFallbackLocale","en-GB"),Object.entries(t).forEach((function(t){var e=f()(t,2),n=e[0],r=e[1];Shopware.State.commit("context/addAppValue",{key:n,value:r})})),Shopware.Context.app}function sn(t,e,n,r){var i=[],o=[],a=null;return{addRoutes:function(t){return i.push.apply(i,k()(t)),i},addModuleRoutes:function(t){return o.push.apply(o,k()(t)),o},createRouterInstance:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=o.map((function(t){return f(t)})),r=i.map((function(t){return f(t)})),s=l(r,n),u=Object.assign({},e,{routes:s}),h=new t(u);return c(h),a=h,h},getViewComponent:p,getRouterInstance:function(){return a},_setModuleFavicon:d};function c(t){var e=Shopware.Context.api.assetsPath;return t.beforeEach((function(t,n,i){Shopware.Service("loginService").getStorage().setItem("lastActivity","".concat(Math.round(+new Date/1e3))),d(t,e);var o=r.isLoggedIn(),a=new Shopware.Helper.RefreshTokenHelper,c=["/login","/login/info","/login/recovery"];return t.meta&&!0===t.meta.forceRoute||!o&&("login"===t.name||c.includes(t.path)||t.path.startsWith("/login/user-recovery/")||t.path.match(/\/inactivity\/login\/[a-z0-9]{32}/))?i():o&&("login"===t.name||c.includes(t.path)||t.path.startsWith("/login/user-recovery/"))?i({name:"core"}):o||(sessionStorage.setItem("sw-admin-previous-route",JSON.stringify({fullPath:t.fullPath,name:t.name})),a.isRefreshing)?t.meta.privilege&&!Shopware.Service("acl").can(t.meta.privilege)?i({name:"sw.privilege.error.index"}):t.name&&t.name.includes("sw.extension.store")&&t.matched.length<=0?i({name:"sw.extension.store.landing-page"}):s(t,n,i):a.fireRefreshTokenRequest().then((function(){return s(t,n,i)})).catch((function(){return i({name:"sw.login.index"})}))})),t}function s(t,e,n){var r=u(t);null!==r&&(t.meta.$module=r.manifest);var i=function(t,e){if(!e||!e.navigation)return null;var n=e.navigation,r=null;return n.forEach((function(e){e.path===t.name&&(r=e)})),r}(t,r);return null!==i&&(t.meta.$current=i),n()}function u(t){var e=n.getModuleRegistry(),r=null,i=null;return e.forEach((function(e){var n;r||(e.routes.has(t.name)?r=e:(null!==(n=t.meta)&&void 0!==n&&n.parentPath?t.meta.parentPath:void 0)&&e.routes.has(t.meta.parentPath)&&(i=e))})),r||i}function l(t,e){var n=[],r=[];return e.forEach((function(t){!0!==t.coreRoute?r.push(t):n.push(t)})),t.map((function(t){return!0===t.root&&(t.children=r),t})),t=[].concat(k()(t),n)}function f(t){if((0,Shopware.Utils.object.hasOwnProperty)(t,"components")&&Object.keys(t.components).length){var e={};Object.keys(t.components).forEach((function(n){var r=t.components[n];"string"==typeof r&&(r=p(r)),e[n]=r})),(t=h(t)).components=e}return"string"==typeof t.component&&(t.component=p(t.component)),t}function h(t){var e;return null!==(e=t.children)&&void 0!==e&&e.length&&(t.children=t.children.map((function(t){var e=t.component;return"string"==typeof e&&(e=p(e)),t.component=e,t.children&&(t=h(t)),t}))),t}function p(t){return Shopware.Application.view.getComponent(t)}function d(t,e){var n=u(t);if(!n)return!1;var r=n.manifest.favicon||null,i=document.getElementById("dynamic-favicon");i.rel="shortcut icon";var o=n.manifest.faviconSrc||"administration";return 0!==e.length&&(e="".concat(e).concat(o,"/")),i.href=r?"".concat(e,"static/img/favicon/modules/").concat(r):"".concat(e,"static/img/favicon/favicon-32x32.png"),!0}}var un=function(){var t=n("3STq");return t.keys().reduce((function(e,n){var r=t(n).default;return e.push(r),e}),[])},ln=n("fdsa"),fn=n("+Sdf"),hn=n("LvDl"),pn=[];function dn(t){if(!t.includes("."))return null;var e=t.split("."),n=e.pop(),r=e.join(".");return n&&n.length&&r&&r.length?{pathToLastSegment:r,lastSegment:n}:null}Object(fn.a)((function(t){var e=pn.find((function(e){return e.id===t.id}));return e?e.data:null}));var vn={handle:ln.d,publishData:function(t){var e=t.id,n=t.path,r=t.scope,i=pn.find((function(t){return t.id===e}));if(i&&i.scope!==r._uid)console.error('The dataset id "'.concat(e,'" you tried to publish is already registered.'));else if(i&&i.scope===r._uid)Object(fn.b)({id:e,data:Object(hn.get)(r,n)}).catch((function(){}));else{Object(fn.c)(e,(function(t){var e;if(t)if("function"!=typeof(null===(e=t.data)||void 0===e?void 0:e.getDraft))if(Array.isArray(t.data)&&t.data.forEach((function(t,e){null!==t&&"object"===E()(t)&&c(s()({},e,t))})),n.includes(".")){var i=n.split("."),o=i.pop(),a=i.join(".");if(!o)return;r.$set(Shopware.Utils.object.get(r,a),o,t.data)}else r.$set(r,n,t.data);else c(t.data);function c(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;("function"!=typeof(null==t?void 0:t.getIsDirty)||t.getIsDirty())&&Object.keys(t).forEach((function(o){var a,u=dn(a=i?"".concat(i,".").concat(o):"".concat(n,".").concat(o));null!==u&&(Shopware.Utils.hasOwnProperty(t[o],"getDraft",e)&&"function"==typeof t[o].getDraft?c(s()({},o,Shopware.Utils.object.cloneDeep(t[o])),a):Array.isArray(t[o])?t[o].forEach((function(t,e){c(s()({},e,t),a)})):r.$set(Shopware.Utils.object.get(r,u.pathToLastSegment),u.lastSegment,t[o]))}))}}));var o=r.$watch(n,Object(hn.debounce)((function(t){Object(fn.b)({id:e,data:t}).catch((function(){}));var n=pn.find((function(t){return t.id===e}));n?n.data=t:pn.push({id:e,data:t,scope:r._uid})}),750),{deep:!0,immediate:!0});r.$once("hook:beforeDestroy",(function(){pn=pn.filter((function(t){return t.id!==e})),o()})),Object(fn.b)({id:e,data:Object(hn.get)(r,n)}).catch((function(){}))}},getPublishedDataSets:function(){return pn}};window.hasOwnProperty("_features_")&&Ee.a.init(_features_),u.a.config={strict:!1};var gn=new Qe(new u.a);gn.addFactory("component",(function(){return Shopware.Feature.isActive("FEATURE_NEXT_19822")?ht:q})).addFactory("template",(function(){return C})).addFactory("module",(function(){return g})).addFactory("entity",(function(){return Bt})).addFactory("state",(function(){return Ht})).addFactory("serviceFactory",(function(){return Mt})).addFactory("classesFactory",(function(){return Wt})).addFactory("mixin",(function(){return Ut})).addFactory("filter",(function(){return Jt})).addFactory("directive",(function(){return $t})).addFactory("locale",(function(){return Xt})).addFactory("shortcut",(function(){return ne})).addFactory("plugin",(function(){return ie})).addFactory("apiService",(function(){return ae})).addFactory("entityDefinition",(function(){return pe})).addFactory("workerNotification",(function(){return me}));var yn=function(){function t(){i()(this,t),s()(this,"Module",{register:g.registerModule,getModuleRegistry:g.getModuleRegistry,getModuleRoutes:g.getModuleRoutes,getModuleByEntityName:g.getModuleByEntityName}),s()(this,"Component",Ee.a.isActive("FEATURE_NEXT_19822")?{register:ht.register,extend:ht.extend,override:ht.override,build:ht.build,wrapComponentConfig:ht.wrapComponentConfig,getTemplate:ht.getComponentTemplate,getComponentRegistry:ht.getComponentRegistry,getComponentHelper:ht.getComponentHelper,registerComponentHelper:ht.registerComponentHelper,markComponentAsSync:ht.markComponentAsSync,isSyncComponent:ht.isSyncComponent}:{register:q.register,extend:q.extend,override:q.override,build:q.build,getTemplate:q.getComponentTemplate,getComponentRegistry:q.getComponentRegistry,getComponentHelper:q.getComponentHelper,registerComponentHelper:q.registerComponentHelper}),s()(this,"Template",{register:C.registerComponentTemplate,extend:C.extendComponentTemplate,override:C.registerTemplateOverride,getRenderedTemplate:C.getRenderedTemplate}),s()(this,"Entity",{addDefinition:Bt.addEntityDefinition,getDefinition:Bt.getEntityDefinition,getDefinitionRegistry:Bt.getDefinitionRegistry,getRawEntityObject:Bt.getRawEntityObject,getPropertyBlacklist:Bt.getPropertyBlacklist,getRequiredProperties:Bt.getRequiredProperties,getAssociatedProperties:Bt.getAssociatedProperties,getTranslatableProperties:Bt.getTranslatableProperties}),s()(this,"State",Ht()),s()(this,"Mixin",{register:Ut.register,getByName:Ut.getByName}),s()(this,"Filter",{register:Jt.register,getByName:Jt.getByName,getRegistry:Jt.getRegistry}),s()(this,"Directive",{register:$t.registerDirective,getByName:$t.getDirectiveByName,getDirectiveRegistry:$t.getDirectiveRegistry}),s()(this,"Locale",{register:Xt.register,extend:Xt.extend,getByName:Xt.getLocaleByName,getLocaleRegistry:Xt.getLocaleRegistry}),s()(this,"Shortcut",{getShortcutRegistry:ne.getShortcutRegistry,getPathByCombination:ne.getPathByCombination,register:ne.register}),s()(this,"Plugin",{addBootPromise:ie.addBootPromise,getBootPromises:ie.getBootPromises}),s()(this,"Service",Mt),s()(this,"Utils",Yt.b),s()(this,"Application",gn),s()(this,"Feature",Ee.a),s()(this,"ApiService",{register:ae.register,getByName:ae.getByName,getRegistry:ae.getRegistry,getServices:ae.getServices,has:ae.has}),s()(this,"EntityDefinition",{getScalarTypes:pe.getScalarTypes,getJsonTypes:pe.getJsonTypes,getDefinitionRegistry:pe.getDefinitionRegistry,has:pe.has,get:pe.get,add:pe.add,remove:pe.remove,getTranslatedFields:pe.getTranslatedFields,getAssociationFields:pe.getAssociationFields,getRequiredFields:pe.getRequiredFields}),s()(this,"ExtensionAPI",vn),s()(this,"WorkerNotification",{register:me.register,getRegistry:me.getRegistry,override:me.override,remove:me.remove,initialize:me.initialize}),s()(this,"Defaults",{systemLanguageId:"2fbb5fe2e29a4d70aa5854ce7ce3e20b",defaultLanguageIds:["2fbb5fe2e29a4d70aa5854ce7ce3e20b"],versionId:"0fa91ce3e96a4bc2be4bd9ce752c3425",storefrontSalesChannelTypeId:"8a243080f92e4c719546314b577cf82b",productComparisonTypeId:"ed535e5722134ac1aa6524f73e26881b",apiSalesChannelTypeId:"f183ee5650cf4bdb8a774337575067a6",defaultSalutationId:"ed643807c9f84cc8b50132ea3ccb1c3b"}),s()(this,"Data",$e),s()(this,"Classes",{ShopwareError:Se,ApiService:xe.a,_private:{HttpFactory:en.a,RepositoryFactory:nn,ApiContextFactory:rn,AppContextFactory:cn,RouterFactory:sn,FilterFactory:Ve}}),s()(this,"Helper",{FlatTreeHelper:Re,MiddlewareHelper:v,RefreshTokenHelper:tn.a,SanitizerHelper:Ce.a,DeviceHelper:Ae}),s()(this,"_private",{ApiServices:un})}return a()(t,[{key:"Context",get:function(){return this.State.get("context")}}]),t}(),mn=new yn;window.Shopware=mn;e.default=mn},IXFE:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return w}));var r=n("cDf5"),i=n.n(r),o=n("yXPU"),a=n.n(o),c=n("lwsE"),s=n.n(c),u=n("W8MJ"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v),y=n("2iSf");function m(){m=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,i){var o=e&&e.prototype instanceof p?e:p,a=Object.create(o.prototype),c=new P(i||[]);return r(a,"_invoke",{value:j(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var h={};function p(){}function d(){}function v(){}var g={};u(g,a,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(R([])));b&&b!==e&&n.call(b,a)&&(g=b);var w=v.prototype=p.prototype=Object.create(g);function O(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function o(r,a,c,s){var u=f(t[r],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==i()(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){o("next",t,c,s)}),(function(t){o("throw",t,c,s)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return o("throw",t,c,s)}))}s(u.arg)}var a;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function j(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return C()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=E(a,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=f(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===h)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function E(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,E(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=f(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function R(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:C}}function C(){return{value:void 0,done:!0}}return d.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:d,configurable:!0}),d.displayName=u(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},O(k.prototype),u(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new k(l(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(w),u(w,s,"Generator"),u(w,a,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=R,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:R(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t}function b(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var w=function(t){h()(v,t);var e,n,r,i,o,c,u,f,p,d=b(v);function v(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"store";return s()(this,v),(n=d.call(this,t,e,r,"application/json")).name="storeService",n}return l()(v,[{key:"ping",value:(p=a()(m().mark((function t(){return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/ping"),{headers:this.getBasicHeaders()});case 2:case"end":return t.stop()}}),t,this)}))),function(){return p.apply(this,arguments)})},{key:"login",value:(f=a()(m().mark((function t(e,n){var r,i;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=this.getBasicHeaders(),i=this.getBasicParams(),t.next=4,this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/login"),{shopwareId:e,password:n},{params:i,headers:r});case 4:case"end":return t.stop()}}),t,this)}))),function(t,e){return f.apply(this,arguments)})},{key:"checkLogin",value:(u=a()(m().mark((function t(){var e,n,r,i;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=this.getBasicHeaders(),n=this.getBasicParams(),t.next=4,this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/checklogin"),{},{params:n,headers:e});case 4:return r=t.sent,i=r.data,t.abrupt("return",i);case 7:case"end":return t.stop()}}),t,this)}))),function(){return u.apply(this,arguments)})},{key:"logout",value:(c=a()(m().mark((function t(){var e,n;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=this.getBasicHeaders(),n=this.getBasicParams(),t.next=4,this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/logout"),{},{params:n,headers:e});case 4:case"end":return t.stop()}}),t,this)}))),function(){return c.apply(this,arguments)})},{key:"getLicenseList",value:(o=a()(m().mark((function t(){var e,n;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=this.getBasicHeaders(),n=this.getBasicParams(),t.t0=y.a,t.next=5,this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/licenses"),{params:n,headers:e});case 5:return t.t1=t.sent,t.abrupt("return",t.t0.handleResponse.call(t.t0,t.t1));case 7:case"end":return t.stop()}}),t,this)}))),function(){return o.apply(this,arguments)})},{key:"getUpdateList",value:(i=a()(m().mark((function t(){var e,n;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=this.getBasicHeaders(),n=this.getBasicParams(),t.t0=y.a,t.next=5,this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/updates"),{params:n,headers:e});case 5:return t.t1=t.sent,t.abrupt("return",t.t0.handleResponse.call(t.t0,t.t1));case 7:case"end":return t.stop()}}),t,this)}))),function(){return i.apply(this,arguments)})},{key:"downloadPlugin",value:(r=a()(m().mark((function t(e){var n,r,i,o,a,c=arguments;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=c.length>1&&void 0!==c[1]&&c[1],r=c.length>2&&void 0!==c[2]&&c[2],i=this.getBasicHeaders(),o=this.getBasicParams({pluginName:e,unauthenticated:n}),t.next=6,this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/download"),{params:o,headers:i});case 6:if(a=t.sent,!r){t.next=9;break}return t.abrupt("return",y.a.handleResponse(a));case 9:return t.t0=y.a,t.next=12,this.httpClient.post("/_action/plugin/update",null,{params:o,headers:i});case 12:return t.t1=t.sent,t.abrupt("return",t.t0.handleResponse.call(t.t0,t.t1));case 14:case"end":return t.stop()}}),t,this)}))),function(t){return r.apply(this,arguments)})},{key:"downloadAndUpdatePlugin",value:(n=a()(m().mark((function t(e){var n,r=arguments;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.length>1&&void 0!==r[1]&&r[1],t.abrupt("return",this.downloadPlugin(e,n,!0));case 2:case"end":return t.stop()}}),t,this)}))),function(t){return n.apply(this,arguments)})},{key:"getLicenseViolationList",value:(e=a()(m().mark((function t(){var e,n;return m().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=this.getBasicHeaders(),n=this.getBasicParams(),t.t0=y.a,t.next=5,this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/plugin/search"),null,{params:n,headers:e});case 5:return t.t1=t.sent,t.abrupt("return",t.t0.handleResponse.call(t.t0,t.t1));case 7:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})},{key:"getBasicParams",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={language:localStorage.getItem("sw-admin-locale")};return Object.assign({},e,t)}}]),v}(y.a)},J5bW:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=Shopware.Classes.ApiService,v=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"language-plugins";return i()(this,n),(o=e.call(this,t,r,a)).name="languagePluginService",o}return a()(n,[{key:"getPlugins",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/store/".concat(this.apiEndpoint),{params:n,headers:r}).then((function(t){return d.handleResponse(t)}))}}]),n}(d);e.default=v},JMVm:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=Shopware.Data.Criteria,g=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"_admin";return i()(this,n),(o=e.call(this,t,r,a)).name="searchService",o}return a()(n,[{key:"search",value:function(t){var e=t.term,n=t.page,r=void 0===n?1:n,i=t.limit,o=void 0===i?5:i,a=(t.additionalParams,t.additionalHeaders),c=void 0===a?{}:a,s=(this.getBasicHeaders(c),new v(r,o));s.setTerm(e);var u={};return["landing_page","order","customer","product","category","media","product_manufacturer","tag","cms_page"].forEach((function(t){u[t]=s})),this.searchQuery(u,c)}},{key:"elastic",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=this.getBasicHeaders(r);return this.httpClient.post("".concat(this.getApiBasePath(),"/es-search"),{term:t,limit:n,entities:e},{headers:i}).then((function(t){return p.a.handleResponse(t)}))}},{key:"searchQuery",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getBasicHeaders(e);return Object.keys(t).forEach((function(e){"function"==typeof t[e].parse&&(t[e]=t[e].parse())})),this.httpClient.post("".concat(this.getApiBasePath(),"/search"),t,{headers:n}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=g},JUxB:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=Shopware.Classes.ApiService,v=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"import-export";return i()(this,n),(o=e.call(this,t,r,a)).name="importExportService",o}return a()(n,[{key:"getFeatures",value:function(){var t="/_action/".concat(this.getApiBasePath(),"/features");return this.httpClient.get(t,{headers:this.getBasicHeaders()}).then((function(t){return d.handleResponse(t)}))}},{key:"initiate",value:function(t,e,n){var r="/_action/".concat(this.getApiBasePath(),"/initiate"),i=new FormData;return n&&i.append("file",n),i.append("profileId",t),i.append("expireDate",e),this.httpClient.post(r,i,{headers:this.getBasicHeaders()}).then((function(t){return d.handleResponse(t)}))}},{key:"process",value:function(t,e){var n="/_action/".concat(this.getApiBasePath(),"/process");return this.httpClient.post(n,{logId:t,offset:e},{headers:this.getBasicHeaders()}).then((function(t){return d.handleResponse(t)}))}},{key:"cancel",value:function(t){var e="/_action/".concat(this.getApiBasePath(),"/cancel");return this.httpClient.post(e,{logId:t},{headers:this.getBasicHeaders()}).then((function(t){return d.handleResponse(t)}))}},{key:"getDownloadUrl",value:function(t,e){var n="".concat(Shopware.Context.api.apiPath);return"/".concat(n,"/_action/").concat(this.getApiBasePath(),"/file/download?fileId=").concat(t,"&accessToken=").concat(e)}}]),n}(d);e.default=v},KpoE:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("2iSf");function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var b=function(t){l()(n,t);var e=m(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"customer-group-registration";return a()(this,n),(i=e.call(this,t,r,o)).name="customerGroupRegistrationService",i}return s()(n,[{key:"accept",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/".concat(this.getApiBasePath(),"/accept");return this.httpClient.post(i,y({customerIds:Array.isArray(t)?t:[t]},r),{params:e,headers:this.getBasicHeaders(n)}).then((function(t){return v.a.handleResponse(t)}))}},{key:"decline",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/".concat(this.getApiBasePath(),"/decline");return this.httpClient.post(i,y({customerIds:Array.isArray(t)?t:[t]},r),{params:e,headers:this.getBasicHeaders(n)}).then((function(t){return v.a.handleResponse(t)}))}}]),n}(v.a);e.default=b},KzIn:function(t,e,n){"use strict";n.d(e,"b",(function(){return S}));var r=n("GoyQ"),i=n.n(r),o=n("YO3V"),a=n.n(o),c=n("E+oP"),s=n.n(c),u=n("wAXd"),l=n.n(u),f=n("Z0cm"),h=n.n(f),p=n("lSCD"),d=n.n(p),v=n("YiAA"),g=n.n(v),y=n("4qC0"),m=n.n(y),b=n("Znm+"),w=n.n(b),O=n("Y+p1"),k=n.n(O),j=n("UB5X"),E=n.n(j);function S(t){return void 0===t}e.a={isObject:i.a,isPlainObject:a.a,isEmpty:s.a,isRegExp:l.a,isArray:h.a,isFunction:d.a,isDate:g.a,isString:m.a,isBoolean:w.a,isEqual:k.a,isNumber:E.a,isUndefined:S}},Lz4t:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"known-ips";return i()(this,n),(o=e.call(this,t,r,a)).name="knownIpsService",o}return a()(n,[{key:"getKnownIps",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_admin/known-ips",{headers:t}).then((function(t){return t.data.ips}))}}]),n}(n("2iSf").a);e.default=d},"M7+a":function(t,e,n){"use strict";n.d(e,"h",(function(){return w})),n.d(e,"i",(function(){return O})),n.d(e,"a",(function(){return k})),n.d(e,"d",(function(){return j})),n.d(e,"k",(function(){return E})),n.d(e,"j",(function(){return S})),n.d(e,"g",(function(){return x})),n.d(e,"b",(function(){return P})),n.d(e,"c",(function(){return R})),n.d(e,"f",(function(){return C})),n.d(e,"e",(function(){return _}));var r=n("lSNA"),i=n.n(r),o=n("QkVN"),a=n.n(o),c=n("JBE3"),s=n.n(c),u=n("BkRI"),l=n.n(u),f=n("mwIZ"),h=n.n(f),p=n("D1y2"),d=n.n(p),v=n("JZM8"),g=n.n(v),y=n("KzIn");function m(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?m(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}a.a,a.a,l.a,h.a,d.a,g.a;var w=a.a,O=s.a,k=l.a,j=h.a,E=d.a,S=g.a;function x(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function P(t){return JSON.parse(JSON.stringify(t))}function R(t,e){return O(t,e,(function(t,e){if(Array.isArray(t))return t.concat(e)}))}function C(t,e){return t===e?{}:y.a.isObject(t)&&y.a.isObject(e)?y.a.isDate(t)||y.a.isDate(e)?t.valueOf()===e.valueOf()?{}:e:Object.keys(e).reduce((function(n,r){if(!x(t,r))return b(b({},n),{},i()({},r,e[r]));if(y.a.isArray(e[r])){var o=_(t[r],e[r]);return Object.keys(o).length>0?b(b({},n),{},i()({},r,e[r])):n}if(y.a.isObject(e[r])){var a=C(t[r],e[r]);return!y.a.isObject(a)||Object.keys(a).length>0?b(b({},n),{},i()({},r,a)):n}return t[r]!==e[r]?b(b({},n),{},i()({},r,e[r])):n}),{}):e}function _(t,e){if(t===e)return[];if(!y.a.isArray(t)||!y.a.isArray(e))return e;if(t.length<=0&&e.length<=0)return[];if(t.length!==e.length)return e;if(!y.a.isObject(e[0]))return e.filter((function(e){return!t.includes(e)}));var n=[];return e.forEach((function(r,i){var o=C(t[i],e[i]);Object.keys(o).length>0&&n.push(e[i])})),n}},Mt2o:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"config";return i()(this,n),(o=e.call(this,t,r,a)).name="configService",o}return a()(n,[{key:"getConfig",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e,i=this.getBasicHeaders(n);return new Promise((function(e){t.httpClient.get("/_info/config",{params:r,headers:i}).then((function(t){e(p.a.handleResponse(t))}))}))}}]),n}(p.a);e.default=v},NOZM:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return d}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=function(t){s()(n,t);var e=p(n);function n(t,r){var o;return i()(this,n),(o=e.call(this,t,r,null,"application/json")).name="notificationsService",o}return a()(n,[{key:"fetchNotifications",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.httpClient.get("notification/message",{params:{limit:t,latestTimestamp:e},headers:this.getBasicHeaders()}).then((function(t){return t.data}))}}]),n}(n("2iSf").a)},OUny:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"sales-channel";return i()(this,n),(o=e.call(this,t,r,a)).name="salesChannelService",o}return a()(n,[{key:"generateKey",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/access-key/sales-channel",{params:n,headers:r}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},OW7U:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"seo-url-template";return i()(this,n),(o=e.call(this,t,r,a)).name="seoUrlTemplateService",o}return a()(n,[{key:"validate",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="/_action/".concat(this.getApiBasePath(),"/validate");return this.httpClient.post(r,t,{params:e,headers:this.getBasicHeaders(n)}).then((function(t){return p.a.handleResponse(t)}))}},{key:"preview",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="/_action/".concat(this.getApiBasePath(),"/preview");return this.httpClient.post(r,t,{params:e,headers:this.getBasicHeaders(n)}).then((function(t){return 204===t.status?null:p.a.handleResponse(t)}))}},{key:"getContext",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="/_action/".concat(this.getApiBasePath(),"/context");return this.httpClient.post(r,t,{params:e,headers:this.getBasicHeaders(n)}).then((function(t){return p.a.handleResponse(t)}))}},{key:"getDefault",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="/_action/".concat(this.getApiBasePath(),"/default/").concat(t);return this.httpClient.get(r,{params:e,headers:this.getBasicHeaders(n)}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},"RT+Y":function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"media-folder";return i()(this,n),(o=e.call(this,t,r,a)).name="mediaFolderService",o}return a()(n,[{key:"dissolveFolder",value:function(t){var e="/_action/".concat(this.getApiBasePath(t),"/dissolve");return this.httpClient.post(e,"",{params:{},headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}},{key:"moveFolder",value:function(t,e){e&&(e="/".concat(e));var n="/_action/".concat(this.getApiBasePath(t),"/move").concat(e);return this.httpClient.post(n,"",{params:{},headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},SWCa:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return E})),n.d(e,"UploadEvents",(function(){return j}));var r=n("lSNA"),i=n.n(r),o=n("RIqP"),a=n.n(o),c=n("lwsE"),s=n.n(c),u=n("W8MJ"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v),y=n("aGfj"),m=l()((function t(e){var n=e.uploadTag,r=e.src,i=e.targetId,o=e.fileName,a=e.extension,c=void 0===a?"dat":a,u=e.isPrivate,l=void 0!==u&&u;s()(this,t),this.running=!1,this.src=r,this.uploadTag=n,this.targetId=i,this.fileName=o,this.extension=c,this.error=null,this.isPrivate=l})),b=n("2iSf");function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function k(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var j={UPLOAD_ADDED:"media-upload-add",UPLOAD_FINISHED:"media-upload-finish",UPLOAD_FAILED:"media-upload-fail",UPLOAD_CANCELED:"media-upload-cancel"},E=function(t){h()(n,t);var e=k(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"media";return s()(this,n),(i=e.call(this,t,r,o)).name="mediaService",i.uploads=[],i.$listeners={},i}return l()(n,[{key:"hasListeners",value:function(t){return!!t&&this.$listeners.hasOwnProperty(t)}},{key:"hasDefaultListeners",value:function(){return this.hasListeners("default")}},{key:"addListener",value:function(t,e){this.hasListeners(t)||(this.$listeners[t]=[]),this.$listeners[t].push(e)}},{key:"removeListener",value:function(t,e){this.hasListeners(t)&&(void 0!==e?y.a.remove(this.$listeners[t],(function(t){return t===e})):y.a.remove(this.$listeners[t],(function(){return!0})))}},{key:"removeDefaultListener",value:function(t){this.removeListener("default",t)}},{key:"addDefaultListener",value:function(t){this.addListener("default",t)}},{key:"getListenerForTag",value:function(t){var e=this.hasListeners(t)?this.$listeners[t]:[],n=this.hasDefaultListeners()?this.$listeners.default:[];return[].concat(a()(e),a()(n))}},{key:"_createUploadEvent",value:function(t,e,n){return{action:t,uploadTag:e,payload:n}}},{key:"addUpload",value:function(t,e){this.addUploads(t,[e])}},{key:"addUploads",value:function(t,e){var n,r=this,i=e.map((function(e){return new m(O({uploadTag:t},e))}));(n=this.uploads).push.apply(n,a()(i)),this.getListenerForTag(t).forEach((function(e){e(r._createUploadEvent(j.UPLOAD_ADDED,t,{data:i}))}))}},{key:"keepFile",value:function(t,e){var n=this,r=new m(O({uploadTag:t},e));this.getListenerForTag(t).forEach((function(e){e(n._createUploadEvent(j.UPLOAD_FINISHED,t,{targetId:r.targetId,successAmount:0,failureAmount:0,totalAmount:0,customMessage:"global.sw-media-upload.notification.assigned.message"}))}))}},{key:"cancelUpload",value:function(t,e){var n=this,r=new m(O({uploadTag:t},e));this.getListenerForTag(t).forEach((function(e){e(n._createUploadEvent(j.UPLOAD_CANCELED,t,{data:r}))}))}},{key:"removeByTag",value:function(t){y.a.remove(this.uploads,(function(e){return e.uploadTag===t}))}},{key:"runUploads",value:function(t){var e=this,n=y.a.remove(this.uploads,(function(e){return e.uploadTag===t})),r=this.getListenerForTag(t);if(0===n.length)return Promise.resolve();var i=n.length,o=0,a=0;return Promise.all(n.map((function(n){return n.running?Promise.resolve():(n.running=!0,e._startUpload(n).then((function(){n.running=!1,o+=1,r.forEach((function(r){r(e._createUploadEvent(j.UPLOAD_FINISHED,t,{targetId:n.targetId,successAmount:o,failureAmount:a,totalAmount:i}))}))})).catch((function(c){n.error=c,n.running=!1,a+=1,n.successAmount=o,n.failureAmount=a,n.totalAmount=i,r.forEach((function(r){r(e._createUploadEvent(j.UPLOAD_FAILED,t,n))}))})))})))}},{key:"_startUpload",value:function(t){var e=this;return t.src instanceof File?y.d.readAsArrayBuffer(t.src).then((function(n){return e.uploadMediaById(t.targetId,t.src.type,n,t.extension,t.fileName)})):t.src instanceof URL?this.uploadMediaFromUrl(t.targetId,t.src.href,t.extension,t.fileName):Promise.reject(new Error("src of upload must either be an instance of File or URL"))}},{key:"uploadMediaById",value:function(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:t;"application/json"===e&&(e="text/plain");var o="/_action/".concat(this.getApiBasePath(t),"/upload"),a=this.getBasicHeaders({"Content-Type":e}),c={extension:r,fileName:i};return this.httpClient.post(o,n,{params:c,headers:a}).then((function(t){return b.a.handleResponse(t)}))}},{key:"uploadMediaFromUrl",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t,i="/_action/".concat(this.getApiBasePath(t),"/upload"),o=this.getBasicHeaders({"Content-Type":"application/json"}),a={extension:n,fileName:r},c=JSON.stringify({url:e});return this.httpClient.post(i,c,{params:a,headers:o}).then((function(t){return b.a.handleResponse(t)}))}},{key:"renameMedia",value:function(t,e){var n="/_action/".concat(this.getApiBasePath(t),"/rename");return this.httpClient.post(n,JSON.stringify({fileName:e}),{params:{},headers:this.getBasicHeaders()}).then((function(t){return b.a.handleResponse(t)}))}},{key:"provideName",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r="/_action/".concat(this.getApiBasePath(),"/provide-name");return this.httpClient.get(r,{params:{fileName:t,extension:e,mediaId:n},headers:this.getBasicHeaders()}).then((function(t){return b.a.handleResponse(t)}))}}]),n}(b.a)},SnjH:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return y})),n.d(e,"DocumentEvents",(function(){return g}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=Shopware.Feature,g={DOCUMENT_FAILED:"create-document-fail",DOCUMENT_FINISHED:"create-document-finished"},y=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"document";return i()(this,n),(o=e.call(this,t,r,a)).name="documentService",o.$listener=function(){return{}},o}return a()(n,[{key:"createDocument",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},c=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null;if(v.isActive("v6.5.0.0")){var s,u="_action/order/document/".concat(e,"/create"),l=this.getBasicHeaders(a),f={orderId:t,config:r,referencedDocumentId:i};return(c||r.documentMediaFileId)&&(f.static=!0),this.httpClient.post(u,[f],{additionalParams:o,headers:l}).then((function(e){var i,a,f;if(s=null===(i=e.data)||void 0===i?void 0:i.data,c&&c instanceof File&&s&&null!==(a=s[0])&&void 0!==a&&a.documentId){var h,p=null===(h=s[0])||void 0===h?void 0:h.documentId,d=c.name.split(".").shift(),v=c.name.split(".").pop();u="/_action/document/".concat(p,"/upload?fileName=").concat(r.documentNumber,"_").concat(d,"&extension=").concat(v),l["Content-Type"]=c.type,s=n.httpClient.post(u,c,{additionalParams:o,headers:l})}var y=null===(f=e.data)||void 0===f?void 0:f.errors;if(!y||!y.hasOwnProperty(t))return n.$listener(n.createDocumentEvent(g.DOCUMENT_FINISHED)),Promise.resolve(s);n.$listener(n.createDocumentEvent(g.DOCUMENT_FAILED,y[t].pop()))})).catch((function(t){var e,r;null!==(e=t.response)&&void 0!==e&&null!==(r=e.data)&&void 0!==r&&r.errors&&n.$listener(n.createDocumentEvent(g.DOCUMENT_FAILED,t.response.data.errors.pop()))}))}var h,p="/_action/order/".concat(t,"/document/").concat(e),d=this.getBasicHeaders(a),y={config:r,referenced_document_id:i};return(c||r.documentMediaFileId)&&(y.static=!0),this.httpClient.post(p,y,{additionalParams:o,headers:d}).then((function(t){if(h=t,c&&c instanceof File&&t.data.documentId){var e=c.name.split(".").shift(),i=c.name.split(".").pop();p="/_action/document/".concat(t.data.documentId,"/upload?fileName=").concat(r.documentNumber,"_").concat(e,"&extension=").concat(i),d["Content-Type"]=c.type,h=n.httpClient.post(p,c,{additionalParams:o,headers:d})}return n.$listener(n.createDocumentEvent(g.DOCUMENT_FINISHED)),Promise.resolve(h)})).catch((function(t){var e,r;null!==(e=t.response)&&void 0!==e&&null!==(r=e.data)&&void 0!==r&&r.errors&&n.$listener(n.createDocumentEvent(g.DOCUMENT_FAILED,t.response.data.errors.pop()))}))}},{key:"getDocumentPreview",value:function(t,e,n,r){var i=JSON.stringify(r);return this.httpClient.get("/_action/order/".concat(t,"/").concat(e,"/document/").concat(n,"/preview"),{params:{config:i},responseType:"blob",headers:this.getBasicHeaders()})}},{key:"getDocument",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return this.httpClient.get("/_action/document/".concat(t,"/").concat(e).concat(r?"?download=1":""),{responseType:"blob",headers:this.getBasicHeaders()})}},{key:"createDocumentEvent",value:function(t,e){return{action:t,payload:e}}},{key:"setListener",value:function(t){this.$listener=t}}]),n}(p.a)},"T+rS":function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=Shopware.Classes.ApiService,v=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"number-range";return i()(this,n),(o=e.call(this,t,r,a)).name="numberRangeService",o}return a()(n,[{key:"reserve",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=e?"/".concat(e):"",o="_action/number-range/reserve/".concat(t).concat(i),a=this.getBasicHeaders(r),c={preview:n};return this.httpClient.get(o,{params:c,headers:a}).then((function(t){return d.handleResponse(t)}))}},{key:"previewPattern",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=this.getBasicHeaders(r),o={pattern:e,start:n};return this.httpClient.get("_action/number-range/preview-pattern/".concat(t),{params:o,headers:i}).then((function(t){return d.handleResponse(t)}))}}]),n}(d);e.default=v},UPBq:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return d}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=function(t){s()(n,t);var e=p(n);function n(t,r){var o;return i()(this,n),(o=e.call(this,t,r,"marketing")).name="marketingService",o}return a()(n,[{key:"getActiveDiscountCampaigns",value:function(){return Promise.resolve({})}},{key:"_getActiveDiscountCampaignsMock",value:function(){return new Promise((function(t){setTimeout((function(){t({name:"An example campaign",title:"string",phase:"comingSoonPhase",comingSoonStartDate:"2005-08-15T15:52:01",startDate:"2005-08-15T15:52:01",lastCallStartDate:"2005-08-15T15:52:01",endDate:"2025-08-15T15:52:01",components:{storeBanner:{background:{color:"#ffffff",image:"https://images.unsplash.com/photo-1518695075031-b83a29bf0012?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80",position:"50% 70%"},content:{textColor:"#fff",headline:{"de-DE":"Super Angebot","en-GB":"Amazing offer"},description:{"de-DE":"Günstiger geht es nicht","en-GB":"It will not get cheaper"},cta:{category:"GitHub",text:{"de-DE":"Zeige GitHub","en-GB":"Show GitHub"}}}},dashboardBanner:{background:{color:"#ffffff",image:"https://images.unsplash.com/photo-1493606278519-11aa9f86e40a?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80",position:"100% 75%"},leftImage:{src:{"en-GB":"https://images.unsplash.com/photo-1587049016823-69ef9d68bd44?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80","de-DE":"https://images.unsplash.com/photo-1527866959252-deab85ef7d1b?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80"},bgColor:"#ffffff",hideInSmallViewports:!1,srcset:{"en-GB":"https://images.unsplash.com/photo-1587049016823-69ef9d68bd44?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80 634w","de-DE":"https://images.unsplash.com/photo-1527866959252-deab85ef7d1b?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80 1050w"}},content:{textColor:"#171717",linkColor:"#26af44",headline:{"de-DE":"Tolle Kampagne","en-GB":"Amazing campaign"},description:{text:{"de-DE":"Es ist {goToShopwareHomePage}, öffne den {goToExtensionStoreAndOpenCategory} oder gehe zum {goToExtensionStore}","en-GB":"Its {goToShopwareHomePage}, open {goToExtensionStoreAndOpenCategory} or go to the {goToExtensionStore}"},inlineActions:[{placeholder:"goToExtensionStore",text:{"de-DE":"Erweiterungs Store","en-GB":"Extension Store"},route:"sw.extension.store.index.extensions"},{placeholder:"goToExtensionStoreAndOpenCategory",text:{"de-DE":"Sommer Sale","en-GB":"Summer Sale"},execution:{method:"linkToExtensionStoreAndSelectCategory",arguments:["category","summerSale"]}},{placeholder:"goToShopwareHomePage",text:{"de-DE":"Shopware","en-GB":"Shopware"},externalLink:{"de-DE":"https://www.shopware.de","en-GB":"https://www.shopware.com"}}]},label:{bgColor:"#ac2c2c",textColor:"#ffffff",text:{"de-DE":"Wichtig","en-GB":"Important"}},mainAction:{buttonVariant:"ghost",bannerIsClickable:!0,cta:{"de-DE":"Kampagne öffnen","en-GB":"Open campaign"},execution:{method:"linkToExtensionStoreAndSelectCategory",arguments:["category","summerSale"]}}}}}})}),1500)}))}}]),n}(n("2iSf").a)},XwGQ:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return d}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"order/document";return i()(this,n),(o=e.call(this,t,r,a)).name="orderDocumentApiService",o}return a()(n,[{key:"create",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.httpClient.post("/_admin/".concat(this.apiEndpoint,"/create"),t,{additionalParams:e,headers:this.getBasicHeaders(n)})}},{key:"generate",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.httpClient.post("/_action/".concat(this.apiEndpoint,"/").concat(t,"/create"),e,{additionalParams:n,headers:this.getBasicHeaders(r)})}},{key:"download",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.httpClient.post("/_action/".concat(this.apiEndpoint,"/download"),{documentIds:t},{additionalParams:e,responseType:"blob",headers:this.getBasicHeaders(n)})}},{key:"extendingDeprecatedService",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.httpClient.get("/_action/document/extending-deprecated-service",{headers:this.getBasicHeaders(t)})}}]),n}(n("2iSf").a)},ZnZ0:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return d}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=function(t){s()(n,t);var e=p(n);function n(t,r){var o;return i()(this,n),(o=e.call(this,t,r,null,"application/json")).name="appCmsBlocks",o}return a()(n,[{key:"fetchAppBlocks",value:function(){return this.httpClient.get("app-system/cms/blocks",{headers:this.getBasicHeaders()}).then((function(t){return t.data.blocks}))}}]),n}(n("2iSf").a)},aGfj:function(t,e,n){"use strict";n.d(e,"e",(function(){return G})),n.d(e,"c",(function(){return q})),n.d(e,"f",(function(){return V})),n.d(e,"g",(function(){return $})),n.d(e,"d",(function(){return K})),n.d(e,"a",(function(){return X}));var r=n("DzJC"),i=n.n(r),o=n("56YH"),a=n.n(o),c=n("sEfC"),s=n.n(c),u=n("mRsi"),l=n.n(u),f=n("7Cbv"),h=n("wEy/"),p=n.n(h),d=n("Kssv"),v=n.n(d),g=n("qPyV"),y=n.n(g),m=n("kcif"),b=n.n(m),w=n("H8cV"),O=n.n(w),k=n("M7+a"),j=n("gyhU"),E=n("lSNA"),S=n.n(E),x=n("giYa"),P=n.n(x);function R(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function C(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?R(Object(n),!0).forEach((function(e){S()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function _(t,e,n){var r,i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=void 0!==n?{minimumFractionDigits:n,maximumFractionDigits:n}:{minimumFractionDigits:2,maximumFractionDigits:20},c=C(C({style:"currency",currency:e||Shopware.Context.app.systemCurrencyISOCode},a),o);return t.toLocaleString(null!==(r=null!==(i=o.language)&&void 0!==i?i:Shopware.State.get("session").currentLocale)&&void 0!==r?r:"en-US",c)}function A(t){var e,n,r,i,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t)return"";var c=new Date(t);if(isNaN(c))return"";var s=Shopware.Application.getContainer("factory").locale.getLastKnownLocale(),u=null!==(e=null===(n=Shopware)||void 0===n||null===(r=n.State)||void 0===r||null===(i=r.get("session"))||void 0===i||null===(o=i.currentUser)||void 0===o?void 0:o.timeZone)&&void 0!==e?e:"UTC",l=new Intl.DateTimeFormat(s,C({timeZone:u,year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric"},a));return l.format(c)}function B(){var t,e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,r=null!==(t=null===(e=Shopware.State.get("session").currentUser)||void 0===e?void 0:e.timeZone)&&void 0!==t?t:"UTC",i=n.toLocaleDateString("en-GB",{timeZone:r,year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"});return new Date(i)}function T(t){return P.a.hash(t)}function L(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"de-DE",n=1024,r=["B","KB","MB","GB"],i=Number.parseInt(String(t),10),o=0;o<r.length;o+=1){var a=i/n;if(a<.9)break;i=a}return"".concat(i.toFixed(2).toLocaleString(e)).concat(r[o])}function D(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=t.toISOString();return e?n:n.split("T")[0]}var N={getScrollbarHeight:function(t){return t instanceof HTMLElement?t.offsetHeight-t.clientHeight:(Object(j.b)("DOM Utilities",'The provided element needs to be an instance of "HTMLElement".',t),0)},getScrollbarWidth:function(t){return t instanceof HTMLElement?t.offsetWidth-t.clientWidth:(Object(j.b)("DOM Utilities",'The provided element needs to be an instance of "HTMLElement".',t),0)},copyToClipboard:function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e)}},F=n("fm5I"),I=n("KzIn");function H(t,e,n){t.onerror=function(){t.abort(),n(new DOMException("Problem parsing file."))},t.onload=function(){e(t.result)}}function M(t){var e=t.split(".");return 1===e.length?{extension:"",fileName:t}:2!==e.length||e[0]?{extension:e.pop(),fileName:e.join(".")}:{extension:"",fileName:t}}var W={readFileAsArrayBuffer:function(t){var e=new FileReader;return new Promise((function(n,r){H(e,n,r),e.readAsArrayBuffer(t)}))},readFileAsDataURL:function(t){var e=new FileReader;return new Promise((function(n,r){H(e,n,r),e.readAsDataURL(t)}))},readFileAsText:function(t){var e=new FileReader;return new Promise((function(n,r){H(e,n,r),e.readAsText(t)}))},getNameAndExtensionFromFile:function(t){return M(t.name)},getNameAndExtensionFromUrl:function(t){var e=t.href.split("/").pop();if(!e)throw new Error("Invalid URL");var n=e.indexOf("?");return n>0&&(e=e.substring(0,n)),M(e=decodeURI(e))}},U={afterSort:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"afterId";if(0===t.length)return t;t.sort((function(t,n){return t.data[e]===n.data[e]&&null===t.data[e]?0:null===n.data[e]?1:null===t.data[e]?-1:0}));var n=t.shift(),r=[n],i=n&&n.id,o=function(){var n=!0;if(t.forEach((function(o,a){o.data[e]===i&&(r.push(o),i=o.id,t.splice(a,1),n=!1)})),n){var o=t.shift();if(r.push(o),!t.length)return"break";i=o&&o.data[e]}};for(;t.length>0;){var a=o();if("break"===a)break}return r}};var G={deepCopyObject:k.b,hasOwnProperty:k.g,getObjectDiff:k.f,getArrayChanges:k.e,cloneDeep:k.a,merge:k.h,mergeWith:k.i,deepMergeObject:k.c,get:k.d,set:k.k,pick:k.j},J={warn:j.b,error:j.a},z={currency:_,date:A,dateWithUserTimezone:B,fileSize:L,md5:T,toISODate:D},q={getScrollbarHeight:N.getScrollbarHeight,getScrollbarWidth:N.getScrollbarWidth,copyToClipboard:N.copyToClipboard},V={capitalizeString:F.a.capitalizeString,camelCase:F.a.camelCase,upperFirst:F.a.upperFirst,kebabCase:F.a.kebabCase,snakeCase:F.a.snakeCase,md5:T,isEmptyOrSpaces:F.a.isEmptyOrSpaces,isUrl:F.a.isUrl,isValidIp:F.a.isValidIp},$={isObject:I.a.isObject,isPlainObject:I.a.isPlainObject,isEmpty:I.a.isEmpty,isRegExp:I.a.isRegExp,isArray:I.a.isArray,isFunction:I.a.isFunction,isDate:I.a.isDate,isString:I.a.isString,isBoolean:I.a.isBoolean,isEqual:I.a.isEqual,isNumber:I.a.isNumber,isUndefined:I.b},K={readAsArrayBuffer:W.readFileAsArrayBuffer,readAsDataURL:W.readFileAsDataURL,readAsText:W.readFileAsText,getNameAndExtensionFromFile:W.getNameAndExtensionFromFile,getNameAndExtensionFromUrl:W.getNameAndExtensionFromUrl},Y={afterSort:U.afterSort},X={flattenDeep:l.a,remove:p.a,slice:v.a,uniqBy:y.a,chunk:b.a,intersectionBy:O.a};e.b={createId:function(){return Object(f.a)().replace(/-/g,"")},throttle:i.a,debounce:s.a,flow:a.a,get:k.d,object:G,debug:J,format:z,dom:q,string:V,types:$,fileReader:K,sort:Y,array:X}},"aW/P":function(t,e,n){"use strict";var r,i;n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return i})),function(t){t.PRODUCT="product",t.CREDIT="credit",t.CUSTOM="custom",t.PROMOTION="promotion"}(r||(r={})),function(t){t.ABSOLUTE="absolute",t.QUANTITY="quantity"}(i||(i={}))},bNBJ:function(t,e,n){"use strict";n.d(e,"a",(function(){return f}));var r=n("J4zp"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("lSNA"),l=n.n(u),f=function(){function t(){a()(this,t)}return s()(t,null,[{key:"init",value:function(t){var e=this;Object.entries(t).forEach((function(t){var n=i()(t,2),r=n[0],o=n[1];e.flags[r]=o}))}},{key:"getAll",value:function(){return this.flags}},{key:"isActive",value:function(t){return!!this.flags.hasOwnProperty(t)&&this.flags[t]}}]),t}();l()(f,"flags",{})},cNT7:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("M7+a"),d=n("aGfj"),v=n("2iSf");function g(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var y=function(t){s()(n,t);var e=g(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"order";return i()(this,n),(o=e.call(this,t,r,a)).name="orderService",o}return a()(n,[{key:"recalculateOrder",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/order/".concat(t,"/recalculate"),o=Object.assign(v.a.getVersionHeader(e),this.getBasicHeaders(r));return this.httpClient.post(i,{},{additionalParams:n,headers:o})}},{key:"addProductToOrder",value:function(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},a="_action/order/".concat(t,"/product/").concat(n),c=Object.assign(v.a.getVersionHeader(e),this.getBasicHeaders(o));return this.httpClient.post(a,{quantity:r},{additionalParams:i,headers:c})}},{key:"addCustomLineItemToOrder",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order/".concat(t,"/lineItem"),a=Object.assign(v.a.getVersionHeader(e),this.getBasicHeaders(i)),c=Object(p.b)(n.priceDefinition);return c.taxRules=n.priceDefinition.taxRules,c.isCalculated=!0,this.httpClient.post(o,JSON.stringify({label:n.label,quantity:n.quantity,type:n.type,identifier:d.b.createId(),description:n.description,priceDefinition:c}),{additionalParams:r,headers:a})}},{key:"addCreditItemToOrder",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order/".concat(t,"/creditItem"),a=Object.assign(v.a.getVersionHeader(e),this.getBasicHeaders(i)),c=Object(p.b)(n.priceDefinition);return this.httpClient.post(o,JSON.stringify({label:n.label,quantity:n.quantity,type:n.type,identifier:d.b.createId(),description:n.description,priceDefinition:c}),{additionalParams:r,headers:a})}},{key:"addPromotionToOrder",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order/".concat(t,"/promotion-item"),a=Object.assign(v.a.getVersionHeader(e),this.getBasicHeaders(i));return this.httpClient.post(o,JSON.stringify({code:n}),{additionalParams:r,headers:a})}},{key:"toggleAutomaticPromotions",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order/".concat(t,"/toggleAutomaticPromotions"),a=Object.assign(v.a.getVersionHeader(e),this.getBasicHeaders(i));return this.httpClient.post(o,JSON.stringify({skipAutomaticPromotions:n}),{additionalParams:r,headers:a})}},{key:"changeOrderAddress",value:function(t,e,n,r){var i="_action/order-address/".concat(t,"/customer-address/").concat(e),o=Object.assign({},n),a=this.getBasicHeaders(r);return this.httpClient.post(i,{},{params:o,headers:a})}}]),n}(v.a);e.default=y},cPrm:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"check-email-unique";return i()(this,n),(o=e.call(this,t,r,a)).name="userValidationService",o}return a()(n,[{key:"checkUserEmail",value:function(t){var e=t.email,n=t.id,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r,a=this.getBasicHeaders(i),c={email:e,id:n};return this.httpClient.post("/_action/user/".concat(this.apiEndpoint),c,{params:o,headers:a}).then((function(t){return p.a.handleResponse(t)}))}},{key:"checkUserUsername",value:function(t){var e=t.username,n=t.id,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r,a=this.getBasicHeaders(i),c={username:e,id:n};return this.httpClient.post("/_action/user/check-username-unique",c,{params:o,headers:a}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},doAR:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p);function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function y(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var m=function(t){l()(n,t);var e=y(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"order";return a()(this,n),(i=e.call(this,t,r,o)).name="orderStateMachineService",i}return s()(n,[{key:"transitionOrderState",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order/".concat(t,"/state/").concat(e),a=this.getBasicHeaders(i);return this.httpClient.post(o,n,g(g({},r),{},{headers:a}))}},{key:"transitionOrderTransactionState",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order_transaction/".concat(t,"/state/").concat(e),a=this.getBasicHeaders(i);return this.httpClient.post(o,n,g(g({},r),{},{headers:a}))}},{key:"transitionOrderDeliveryState",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_action/order_delivery/".concat(t,"/state/").concat(e),a=this.getBasicHeaders(i);return this.httpClient.post(o,n,g(g({},r),{},{headers:a}))}}]),n}(n("2iSf").a);e.default=m},earh:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"scheduled-task";return i()(this,n),(o=e.call(this,t,r,a)).name="scheduledTaskService",o}return a()(n,[{key:"runTasks",value:function(){var t=this.getBasicHeaders();return this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/run"),null,{headers:t}).then((function(t){return p.a.handleResponse(t)}))}},{key:"getMinRunInterval",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/min-run-interval"),{headers:t}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},egkI:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return O}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("PJYZ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("lSNA"),g=n.n(v),y=n("2iSf");function m(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?m(Object(n),!0).forEach((function(e){g()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function w(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var O=function(t){l()(n,t);var e=w(n);function n(t,r){var o;return i()(this,n),o=e.call(this,t,r,"extension-sdk","application/json"),g()(s()(o),"signedSourcesCache",new Map),o.name="extensionSdkService",o}return a()(n,[{key:"runAction",value:function(t,e){return this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/run-action"),b(b({},t),{},{ids:e}),{params:{},headers:this.getBasicHeaders()}).then((function(t){y.a.handleResponse(t)}))}},{key:"signIframeSrc",value:function(t,e){var n="".concat(t,"-").concat(e);return this.signedSourcesCache.has(n)||this.signedSourcesCache.set(n,this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/sign-uri"),{appName:t,uri:e},{params:{},headers:this.getBasicHeaders()}).then((function(t){return y.a.handleResponse(t)}))),this.signedSourcesCache.get(n)}}]),n}(y.a)},fLuy:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=Shopware.Classes.ApiService,v=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"update";return i()(this,n),(o=e.call(this,t,r,a)).name="updateService",o}return a()(n,[{key:"checkForUpdates",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/check"),{headers:t}).then((function(t){return d.handleResponse(t)}))}},{key:"checkRequirements",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/check-requirements"),{headers:t}).then((function(t){return d.handleResponse(t)}))}},{key:"pluginCompatibility",value:function(){var t=this.getBasicHeaders(),e=this.getBasicParams();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/plugin-compatibility"),{params:e,headers:t}).then((function(t){return d.handleResponse(t)}))}},{key:"downloadUpdate",value:function(t){var e=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/download-latest-update?offset=").concat(t),{headers:e}).then((function(t){return d.handleResponse(t)}))}},{key:"deactivatePlugins",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=this.getBasicHeaders(),r="/_action/".concat(this.getApiBasePath()),i="offset=".concat(t,"&deactivationFilter=").concat(e);return this.httpClient.get("".concat(r,"/deactivate-plugins?").concat(i),{headers:n}).then((function(t){return d.handleResponse(t)}))}},{key:"unpackUpdate",value:function(t){var e=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/unpack?offset=").concat(t),{headers:e}).then((function(t){return d.handleResponse(t)}))}},{key:"getBasicParams",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={language:localStorage.getItem("sw-admin-locale")};return Object.assign({},e,t)}}]),n}(d);e.default=v},fm5I:function(t,e,n){"use strict";var r=n("6acW"),i=n.n(r),o=n("u6S6"),a=n.n(o),c=n("N1om"),s=n.n(c),u=n("79/T"),l=n.n(u),f=n("gQMU"),h=n.n(f);e.a={capitalizeString:i.a,upperFirst:h.a,camelCase:a.a,kebabCase:s.a,snakeCase:l.a,isEmptyOrSpaces:function(t){return!t||t.length<=0||t.trim().length<=0},isUrl:function(t){try{return Boolean(new URL(t))}catch(t){return!1}},isValidIp:function(t){return RegExp("((^\\s*((([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]))\\s*$)|(^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*$))").test(t)}}},"gJ+d":function(t,e,n){"use strict";var r=n("SH5M");e.a=r.a},gNKo:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("2iSf");function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var b=function(t){l()(n,t);var e=m(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"state-machine";return a()(this,n),(i=e.call(this,t,r,o)).name="stateMachineService",i}return s()(n,[{key:"getState",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},a=v.a.makeQueryParams(y({stateFieldName:i},o)),c="_action/state-machine/".concat(t,"/").concat(e,"/state").concat(a),s=this.getBasicHeaders(r);return this.httpClient.get(c,{additionalParams:n,headers:s})}},{key:"transitionState",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:{},c=v.a.makeQueryParams(y({stateFieldName:o},a)),s="_action/state-machine/".concat(t,"/").concat(e,"/state/").concat(n).concat(c),u=this.getBasicHeaders(i);return this.httpClient.post(s,{},{additionalParams:r,headers:u})}}]),n}(v.a);e.default=b},gyhU:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return i}));function r(){}function i(){}},hFIz:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("2iSf");function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var b=function(t){l()(n,t);var e=m(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"system-config";return a()(this,n),(i=e.call(this,t,r,o)).name="systemConfigApiService",i}return s()(n,[{key:"checkConfig",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.httpClient.get("_action/system-config/check",{params:y({domain:t},e),headers:this.getBasicHeaders(n)}).then((function(t){return v.a.handleResponse(t)}))}},{key:"getConfig",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.httpClient.get("_action/system-config/schema",{params:y({domain:t},e),headers:this.getBasicHeaders(n)}).then((function(t){return v.a.handleResponse(t)}))}},{key:"getValues",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.httpClient.get("_action/system-config",{params:y({domain:t,salesChannelId:e},n),headers:this.getBasicHeaders(r)}).then((function(t){return v.a.handleResponse(t)})).then((function(t){return Array.isArray(t)?{}:t}))}},{key:"saveValues",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.httpClient.post("_action/system-config",t,{params:y({salesChannelId:e},n),headers:this.getBasicHeaders(r)}).then((function(t){return v.a.handleResponse(t)}))}},{key:"batchSave",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.httpClient.post("_action/system-config/batch",t,{params:y({},e),headers:this.getBasicHeaders(n)}).then((function(t){return v.a.handleResponse(t)}))}}]),n}(v.a);e.default=b},hFht:function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=function(){function t(){i()(this,t),this._isRefreshing=!1,this._subscribers=[],this._errorSubscribers=[],this._whitelist=["/oauth/token"]}return a()(t,[{key:"subscribe",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};this._subscribers.push(t),this._errorSubscribers.push(e)}},{key:"onRefreshToken",value:function(t){this._subscribers=this._subscribers.reduce((function(e,n){return n.call(null,t),e}),[]),this._errorSubscribers=[]}},{key:"onRefreshTokenFailed",value:function(t){this._errorSubscribers=this._errorSubscribers.reduce((function(e,n){return n.call(null,t),e}),[]),this._subscribers=[]}},{key:"fireRefreshTokenRequest",value:function(){var t=this,e=Shopware.Service("loginService");return this.isRefreshing=!0,e.refreshToken().then((function(e){t.onRefreshToken(e)})).finally((function(){t.isRefreshing=!1})).catch((function(){return e.logout(),t.onRefreshTokenFailed(),Promise.reject()}))}},{key:"whitelist",get:function(){return this._whitelist},set:function(t){this._whitelists=t}},{key:"isRefreshing",get:function(){return this._isRefreshing},set:function(t){this._isRefreshing=t}}]),t}(),s=new c;function u(){return s}},iXTH:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"seo-url";return i()(this,n),(o=e.call(this,t,r,a)).name="seoUrlService",o}return a()(n,[{key:"updateCanonicalUrl",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/".concat(this.getApiBasePath(),"/canonical");return Object.assign(r,{"sw-language-id":e}),this.httpClient.patch(i,t,{params:n,headers:this.getBasicHeaders(r)}).then((function(t){return p.a.handleResponse(t)}))}},{key:"createCustomUrl",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/".concat(this.getApiBasePath(),"/create-custom-url");return this.httpClient.post(i,{routeName:t,urls:e},{params:n,headers:this.getBasicHeaders(r)}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},in9x:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"actions";return i()(this,n),(o=e.call(this,t,r,a)).name="flowActionService",o}return a()(n,[{key:"getActions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_info/flow-actions.json",{params:n,headers:r}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},jVYu:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"calculate-price";return i()(this,n),(o=e.call(this,t,r,a)).name="calculate-price",o}return a()(n,[{key:"calculatePrice",value:function(t){var e=t.taxId,n=t.price,r=t.output,i=void 0===r?"gross":r,o=t.currencyId,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=a,u=this.getBasicHeaders(c),l={taxId:e,price:n,output:i,currencyId:o};return this.httpClient.post("/_action/".concat(this.apiEndpoint),l,{params:s,headers:u}).then((function(t){return p.a.handleResponse(t)}))}},{key:"calculatePrices",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n,o=this.getBasicHeaders(r),a={taxId:t,prices:e};return this.httpClient.post("/_action/calculate-prices",a,{params:i,headers:o}).then((function(t){return p.a.handleResponse(t.data)}))}}]),n}(p.a);e.default=v},juyG:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=function(){function t(e,n){i()(this,t),this.httpClient=e,this.loginService=n,this.name="cacheApiService"}return a()(t,[{key:"info",value:function(){var t=this.getHeaders();return this.httpClient.get("/_action/cache_info",{headers:t})}},{key:"index",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=this.getHeaders();return this.httpClient.post("/_action/index",{skip:t},{headers:e})}},{key:"clear",value:function(){var t=this,e=this.getHeaders();return this.httpClient.delete("/_action/cache",{headers:e}).then((function(n){return 204===n.status?t.httpClient.delete("/_action/container_cache",{headers:e}):Promise.reject()}))}},{key:"cleanupOldCaches",value:function(){var t=this.getHeaders();return this.httpClient.delete("/_action/cleanup",{headers:t})}},{key:"clearAndWarmup",value:function(){var t=this.getHeaders();return this.httpClient.delete("/_action/cache_warmup",{headers:t})}},{key:"getHeaders",value:function(){return{Accept:"application/json",Authorization:"Bearer ".concat(this.loginService.getToken()),"Content-Type":"application/json"}}}]),t}();e.default=c},lWuk:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return b}));var r=n("cDf5"),i=n.n(r),o=n("yXPU"),a=n.n(o),c=n("lwsE"),s=n.n(c),u=n("W8MJ"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v);function y(){y=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,i){var o=e&&e.prototype instanceof p?e:p,a=Object.create(o.prototype),c=new P(i||[]);return r(a,"_invoke",{value:j(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var h={};function p(){}function d(){}function v(){}var g={};u(g,a,(function(){return this}));var m=Object.getPrototypeOf,b=m&&m(m(R([])));b&&b!==e&&n.call(b,a)&&(g=b);var w=v.prototype=p.prototype=Object.create(g);function O(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function o(r,a,c,s){var u=f(t[r],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==i()(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){o("next",t,c,s)}),(function(t){o("throw",t,c,s)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return o("throw",t,c,s)}))}s(u.arg)}var a;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function j(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return C()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=E(a,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=f(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===h)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function E(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,E(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=f(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function R(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:C}}function C(){return{value:void 0,done:!0}}return d.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:d,configurable:!0}),d.displayName=u(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},O(k.prototype),u(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new k(l(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(w),u(w,s,"Generator"),u(w,a,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=R,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:R(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var b=function(t){h()(i,t);var e,n,r=m(i);function i(t,e){var n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"sync";return s()(this,i),(n=r.call(this,t,e,o)).name="promotionSyncService",n}return l()(i,[{key:"loadPackagers",value:(n=a()(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this.httpClient.get("/_action/promotion/setgroup/packager",{headers:this.getBasicHeaders()}).then((function(t){return t.data})));case 1:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"loadSorters",value:(e=a()(y().mark((function t(){return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this.httpClient.get("/_action/promotion/setgroup/sorter",{headers:this.getBasicHeaders()}).then((function(t){return t.data})));case 1:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})}]),i}(n("6CVo").default)},lnsc:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("iWIM"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v);function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function b(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var w=Shopware.Classes.ApiService,O=function(t){h()(n,t);var e=b(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"frw";return a()(this,n),(i=e.call(this,t,r,o)).name="firstRunWizardService",i}return s()(n,[{key:"getBasicHeaders",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m(m({},l()(g()(n.prototype),"getBasicHeaders",this).call(this,t)),{},{"sw-language-id":Shopware.Context.api.languageId})}},{key:"checkShopwareId",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=e,i=this.getBasicHeaders(n);return this.httpClient.post("/_action/store/".concat(this.apiEndpoint,"/login"),t,{params:r,headers:i}).then((function(t){return w.handleResponse(t)}))}},{key:"getLicenseDomains",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/store/license-domains",{params:n,headers:r}).then((function(t){return w.handleResponse(t)}))}},{key:"verifyLicenseDomain",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.post("/_action/store/verify-license-domain",{},{params:n,headers:r}).then((function(t){return w.handleResponse(t)}))}},{key:"setFRWStart",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.post("/_action/store/frw/start",{},{params:n,headers:r}).then((function(t){return w.handleResponse(t)}))}},{key:"setFRWFinish",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.post("/_action/store/frw/finish",{},{params:n,headers:r}).then((function(t){return w.handleResponse(t)}))}}]),n}(w);e.default=O},m9g7:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"message-queue";return i()(this,n),(o=e.call(this,t,r,a)).name="messageQueueService",o}return a()(n,[{key:"consume",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=this.getBasicHeaders();return this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/consume"),{receiver:t},{headers:n,cancelToken:e}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},n2ml:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return v}));var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o;return i()(this,n),(o=e.call(this,t,r,"custom-snippet","application/json")).name="customSnippetApiService",o}return a()(n,[{key:"snippets",value:function(){return this.httpClient.get("/_action/".concat(this.getApiBasePath()),{headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}},{key:"render",value:function(t,e){var n={data:{address:t},format:e};return this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/render"),n,{headers:this.getBasicHeaders()}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a)},oJGZ:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return u}));var r=n("cDf5"),i=n.n(r),o=n("vDqi"),a=n.n(o),c=n("hFht"),s=n("AF7S");function u(e){return function(){var e,n,r=a.a.create({baseURL:Shopware.Context.api.apiPath});(function(t){var e=Object(c.a)();t.interceptors.response.use((function(t){return t}),(function(t){var n=t.config,r=t.response.status,i=n,o=i.url.replace(i.baseURL,"");return e.whitelist.includes(o)?Promise.reject(t):401===r?(e.isRefreshing||e.fireRefreshTokenRequest().catch((function(){return Promise.reject(t)})),new Promise((function(t,n){e.subscribe((function(e){i.headers.Authorization="Bearer ".concat(e),i.url=i.url.replace(i.baseURL,""),t(a()(i))}),(function(t){if(!Shopware.Application.getApplicationRoot())return n(t),void window.location.reload();n(t)}))}))):Promise.reject(t)}))})(r),function(t){t.interceptors.response.use((function(t){return t}),(function(t){if((0,Shopware.Utils.object.hasOwnProperty)(t.config.headers,"sw-app-integration-id"))return Promise.reject(t);var e=t.response,n=e.status,r=e.data,i=r.errors,o=r.data;try{f({status:n,errors:i,error:t,data:o})}catch(t){Shopware.Utils.debug.error(t),i&&i.forEach((function(t){Shopware.State.dispatch("notification/createNotification",{variant:"error",title:t.title,message:t.detail})}))}return Promise.reject(t)}))}(r),function(t){var e=1;t.interceptors.response.use((function(t){return t}),(function(n){var r,i,o=n.config,a=n.response,c=null===(r=a.data)||void 0===r||null===(i=r.errors[0])||void 0===i?void 0:i.code;if(o.storeSessionRequestRetries>=e)return Promise.reject(n);var s=["FRAMEWORK__STORE_SESSION_EXPIRED","FRAMEWORK__STORE_SHOP_SECRET_INVALID"];return 403===a.status&&s.includes(c)?("number"==typeof o.storeSessionRequestRetries?o.storeSessionRequestRetries+=1:o.storeSessionRequestRetries=1,t.request(o)):Promise.reject(n)}))}(r),r.CancelToken=l,"test"!==(null===(e=t)||void 0===e||null===(n=Object({NODE_ENV:"production"}))||void 0===n?void 0:n.NODE_ENV)&&function(t){var e={};t.interceptors.request.use((function(t){var n=t.adapter;return t.adapter=Object(s.a)(n,e),t}))}(r);return r}()}var l=a.a.CancelToken;a.a.isCancel,a.a.Cancel;function f(t){var e,n,r,o,a=t.status,c=t.errors,s=t.error,u=void 0===s?null:s,l=t.data,h=Shopware.Application.view.root,p=h.$tc.bind(h);if(400===a&&(null!==(e=null==u||null===(n=u.response)||void 0===n||null===(r=n.config)||void 0===r?void 0:r.url)&&void 0!==e?e:"").includes("_action/sync")){if(!l)return;Object.values(l).forEach((function(t){t.result.forEach((function(t){t.errors.length&&f({status:parseInt(t.errors[0].status,10),errors:t.errors,data:l})}))}))}403===a&&c.filter((function(t){return"FRAMEWORK__MISSING_PRIVILEGE_ERROR"===t.code})).forEach((function(t){var e=JSON.parse(t.detail).missingPrivileges;Array.isArray(e)||"object"!==i()(e)||(e=Object.values(e));var n=e.reduce((function(t,e){return"".concat(t,'<br>"').concat(e,'"')}),"");Shopware.State.dispatch("notification/createNotification",{variant:"error",system:!0,autoClose:!1,growl:!0,title:p("global.error-codes.FRAMEWORK__MISSING_PRIVILEGE_ERROR"),message:"".concat(p("sw-privileges.error.description")," <br> ").concat(n)})}));if(403===a&&["FRAMEWORK__STORE_SESSION_EXPIRED","FRAMEWORK__STORE_SHOP_SECRET_INVALID"].includes(null===(o=c[0])||void 0===o?void 0:o.code)&&Shopware.State.dispatch("notification/createNotification",{variant:"warning",system:!0,autoClose:!1,growl:!0,title:p("sw-extension.errors.storeSessionExpired.title"),message:p("sw-extension.errors.storeSessionExpired.message"),actions:[{label:p("sw-extension.errors.storeSessionExpired.actionLabel"),method:function(){h.$router.push({name:"sw.extension.my-extensions.account"})}}]}),409===a&&"FRAMEWORK__DELETE_RESTRICTED"===c[0].code){var d,v=c[0].meta.parameters,g=v.entity;d=v.usages.reduce((function(t,e){var n=e.count,r=p("global.default.xTimesIn",n),i=p("global.entities.".concat(e.entityName),n[1]);return"".concat(t,"<br>").concat(r," <b>").concat(i,"</b>")}),""),Shopware.State.dispatch("notification/createNotification",{variant:"error",title:p("global.default.error"),message:"".concat(p("global.notification.messageDeleteFailed",3,{entityName:p("global.entities.".concat(g))})).concat(d)})}if(412===a){var y=c.find((function(t){return"FRAMEWORK__LANGUAGE_NOT_FOUND"===t.code}));y&&(localStorage.removeItem("sw-admin-current-language"),Shopware.State.dispatch("notification/createNotification",{variant:"error",system:!0,autoClose:!1,growl:!0,title:y.title,message:"".concat(y.detail," Please reload the administration."),actions:[{label:"Reload administration",method:function(){return window.location.reload()}}]}))}}}).call(this,n("8oxB"))},p0GD:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return w}));var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("iWIM"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v);function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function b(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var w=function(t){h()(n,t);var e=b(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"reset-excluded-search-term";return a()(this,n),(i=e.call(this,t,r,o)).name="excludedSearchTermService",i}return s()(n,[{key:"resetExcludedSearchTerm",value:function(){var t=this._getHeader();return this.httpClient.post("/_admin/reset-excluded-search-term",{},{headers:t})}},{key:"_getHeader",value:function(){return m(m({},l()(g()(n.prototype),"getBasicHeaders",this).call(this)),{},{"sw-language-id":Shopware.Context.api.languageId})}}]),n}(n("2iSf").a)},rh9H:function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return b}));var r=n("cDf5"),i=n.n(r),o=n("yXPU"),a=n.n(o),c=n("lwsE"),s=n.n(c),u=n("W8MJ"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v);function y(){y=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,i){var o=e&&e.prototype instanceof p?e:p,a=Object.create(o.prototype),c=new P(i||[]);return r(a,"_invoke",{value:j(t,n,c)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var h={};function p(){}function d(){}function v(){}var g={};u(g,a,(function(){return this}));var m=Object.getPrototypeOf,b=m&&m(m(R([])));b&&b!==e&&n.call(b,a)&&(g=b);var w=v.prototype=p.prototype=Object.create(g);function O(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function o(r,a,c,s){var u=f(t[r],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==i()(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){o("next",t,c,s)}),(function(t){o("throw",t,c,s)})):e.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return o("throw",t,c,s)}))}s(u.arg)}var a;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function j(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return C()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=E(a,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=f(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===h)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function E(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,E(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=f(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function R(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:C}}function C(){return{value:void 0,done:!0}}return d.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:d,configurable:!0}),d.displayName=u(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},O(k.prototype),u(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new k(l(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(w),u(w,s,"Generator"),u(w,a,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=R,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),x(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;x(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:R(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var b=function(t){h()(r,t);var e,n=m(r);function r(t,e){var i;return s()(this,r),(i=n.call(this,t,e,"","application/json")).name="appModulesService",i}return l()(r,[{key:"fetchAppModules",value:(e=a()(y().mark((function t(){var e,n;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.httpClient.get("app-system/modules",{headers:this.getBasicHeaders()});case 2:return e=t.sent,n=e.data,t.abrupt("return",n.modules);case 5:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})}]),r}(n("2iSf").a)},s5md:function(t,e,n){"use strict";n.r(e),n.d(e,"InvalidActionButtonParameterError",(function(){return j})),n.d(e,"default",(function(){return E}));var r=n("lSNA"),i=n.n(r),o=n("iWIM"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("lwsE"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v),y=n("oShl"),m=n.n(y),b=n("2iSf");function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function k(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var j=function(t){h()(n,t);var e=k(n);function n(t){var r;return l()(this,n),(r=e.call(this,t)).name="InvalidActionButtonParameterError",r}return s()(n)}(m()(Error)),E=function(t){h()(n,t);var e=k(n);function n(t,r){var i;return l()(this,n),(i=e.call(this,t,r,null,"application/json")).name="appActionButtonService",i}return s()(n,[{key:"getBasicHeaders",value:function(){return O(O({},a()(g()(n.prototype),"getBasicHeaders",this).call(this)),{},{"sw-language-id":Shopware.Context.api.languageId})}},{key:"getActionButtonsPerView",value:function(t,e){if(!t)throw new j('Parameter "entity" must have a valid value. Given: '.concat(t));if(!e)throw new j('Parameter "view" must have a valid value. Given: '.concat(e));return this.httpClient.get("app-system/action-button/".concat(t,"/").concat(e),{headers:this.getBasicHeaders()}).then((function(t){return t.data.actions}))}},{key:"runAction",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.httpClient.post("app-system/action-button/run/".concat(t),e,{headers:this.getBasicHeaders()})}}]),n}(b.a)},szaz:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("M7+a"),g=n("aGfj"),y=n("2iSf"),m=n("aW/P");function b(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function w(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?b(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function O(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var k=function(t){l()(n,t);var e=O(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"cart";return a()(this,n),(i=e.call(this,t,r,o)).name="cartStoreService",i}return s()(n,[{key:"getLineItemTypes",value:function(){return m.a}},{key:"getLineItemPriceTypes",value:function(){return m.b}},{key:"mapLineItemTypeToPriceType",value:function(t){var e;return(e={},i()(e,m.a.PRODUCT,m.b.QUANTITY),i()(e,m.a.CUSTOM,m.b.QUANTITY),i()(e,m.a.CREDIT,m.b.ABSOLUTE),e)[t]}},{key:"createCart",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getCart(t,null,e,n)}},{key:"getCart",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="_proxy/store-api/".concat(t,"/checkout/cart"),o=this.getBasicHeaders(w({},r));return e&&(o["sw-context-token"]=e),this.httpClient.get(i,w(w({},n),{},{headers:o}))}},{key:"cancelCart",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="_proxy/store-api/".concat(t,"/checkout/cart"),o=this.getBasicHeaders(w(w({},r),{},{"sw-context-token":e}));return this.httpClient.delete(i,w(w({},n),{},{headers:o}))}},{key:"removeLineItems",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_proxy/store-api/".concat(t,"/checkout/cart/line-item"),a=this.getBasicHeaders(w(w({},i),{},{"sw-context-token":e}));return this.httpClient.delete(o,w(w({},r),{},{headers:a,data:{ids:n}}))}},{key:"getRouteForItem",value:function(t,e){return"_proxy/store-api/".concat(e,"/checkout/cart/line-item")}},{key:"shouldPriceUpdated",value:function(t,e){var n,r,i,o,a,c,s,u,l,f=(null===(n=t.price)||void 0===n?void 0:n.unitPrice)!==t.priceDefinition.price,h=(null!==(r=null===(i=t.price)||void 0===i||null===(o=i.taxRules)||void 0===o||null===(a=o[0])||void 0===a?void 0:a.taxRate)&&void 0!==r?r:null)!==(null!==(c=null===(s=t.priceDefinition)||void 0===s||null===(u=s.taxRules)||void 0===u||null===(l=u[0])||void 0===l?void 0:l.taxRate)&&void 0!==c?c:null),p=t.type===m.a.CUSTOM;return!!(!e&&f||h||p&&!f)}},{key:"getPayloadForItem",value:function(t,e,n,r){var i=null;return this.shouldPriceUpdated(t,n)&&((i=Object(v.b)(t.priceDefinition)).taxRules=t.priceDefinition.taxRules,i.quantity=t.quantity,i.type=this.mapLineItemTypeToPriceType(t.type)),{items:[{id:r,referencedId:r,label:t.label,quantity:t.quantity,type:t.type,description:t.description,priceDefinition:i,stackable:!0,removable:!0,salesChannelId:e}]}}},{key:"saveLineItem",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o=n._isNew&&n.type===m.a.PRODUCT,a=n.identifier||n.id||g.b.createId(),c=this.getRouteForItem(a,t),s=w(w({},this.getBasicHeaders(i)),{},{"sw-context-token":e}),u=this.getPayloadForItem(n,t,o,a);return n._isNew?this.httpClient.post(c,u,w(w({},r),{},{headers:s})):this.httpClient.patch(c,u,w(w({},r),{},{headers:s}))}},{key:"addPromotionCode",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_proxy/store-api/".concat(t,"/checkout/cart/line-item"),a=w(w({},this.getBasicHeaders(i)),{},{"sw-context-token":e}),c={items:[{type:m.a.PROMOTION,referencedId:n}]};return this.httpClient.post(o,c,w(w({},r),{},{headers:a}))}},{key:"modifyShippingCosts",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_proxy/modify-shipping-costs",a=w(w({},this.getBasicHeaders(r)),{},{"sw-context-token":e});return this.httpClient.patch(o,{salesChannelId:t,shippingCosts:n},w(w({},i),{},{headers:a}))}},{key:"disableAutomaticPromotions",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{salesChannelId:null},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="_proxy/disable-automatic-promotions",i=w(w({},this.getBasicHeaders(n)),{},{"sw-context-token":t}),o={salesChannelId:e.salesChannelId};return this.httpClient.patch(r,o,w(w({},e),{},{headers:i}))}},{key:"enableAutomaticPromotions",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{salesChannelId:null},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r="_proxy/enable-automatic-promotions",i=w(w({},this.getBasicHeaders(n)),{},{"sw-context-token":t}),o={salesChannelId:e.salesChannelId};return this.httpClient.patch(r,o,w(w({},e),{},{headers:i}))}},{key:"addMultipleLineItems",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o="_proxy/store-api/".concat(t,"/checkout/cart/line-item"),a=w(w({},this.getBasicHeaders(i)),{},{"sw-context-token":e}),c=n.map((function(e){if(e.type===m.a.PROMOTION)return e;var n=e.identifier||e.id||g.b.createId();return{id:n,referencedId:n,label:e.label,quantity:e.quantity,type:e.type,description:e.description,priceDefinition:e.type===m.a.PRODUCT?null:e.priceDefinition,stackable:!0,removable:!0,salesChannelId:t}}));return this.httpClient.post(o,{items:c},w(w({},r),{},{headers:a}))}}]),n}(y.a);e.default=k},tGtW:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f);function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var d=Shopware.Classes.ApiService,v=function(t){s()(n,t);var e=p(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"integration";return i()(this,n),(o=e.call(this,t,r,a)).name="integrationService",o}return a()(n,[{key:"generateKey",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t,i=this.getBasicHeaders(e),o=n?"/_action/access-key/user":"/_action/access-key/intergration";return this.httpClient.get(o,{params:r,headers:i}).then((function(t){return d.handleResponse(t)}))}}]),n}(d);e.default=v},tTBJ:function(t,e,n){"use strict";n.r(e);var r=n("J4zp"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("2iSf");function g(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var y=function(t){l()(n,t);var e=g(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"snippet";return a()(this,n),(i=e.call(this,t,r,o)).name="snippetService",i}return s()(n,[{key:"getByKey",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=this.getBasicHeaders();return this.httpClient.post("/_action/".concat(this.getApiBasePath()),{translationKey:t,page:e,limit:n,isCustom:r},{headers:i}).then((function(t){return v.a.handleResponse(t)}))}},{key:"getFilter",value:function(){var t=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/filter"),{headers:t}).then((function(t){return v.a.handleResponse(t)}))}},{key:"getSnippets",value:function(t,e){var n=this.getBasicHeaders(),r=e||t.getLastKnownLocale();return this.httpClient.get("/_admin/snippets?locale=".concat(r),{headers:n}).then((function(t){return v.a.handleResponse(t)})).then((function(e){var n=t.getLocaleRegistry();Object.entries(e).forEach((function(e){var r=i()(e,2),o=r[0],a=r[1],c=n.has(o)?"extend":"register";t[c](o,a)}))}))}}]),n}(v.a);e.default=y},uaRI:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"user";return i()(this,n),(o=e.call(this,t,r,a)).name="userService",o}return a()(n,[{key:"getUser",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_info/me",{params:n,headers:r}).then((function(t){return p.a.handleResponse(t)}))}},{key:"updateUser",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.patch("/_info/me",n,{headers:r}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},umIt:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("7W2i"),l=n.n(u),f=n("a1gu"),h=n.n(f),p=n("Nsbk"),d=n.n(p),v=n("2iSf");function g(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}var y=function(t){l()(n,t);var e=g(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"mail-template";return a()(this,n),(i=e.call(this,t,r,o)).name="mailService",i}return s()(n,[{key:"sendMailTemplate",value:function(t,e,n,r,o){var a,c,s,u,l,f,h,p,d=arguments.length>5&&void 0!==arguments[5]&&arguments[5],g=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[],y=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,m="/_action/".concat(this.getApiBasePath(),"/send");return this.httpClient.post(m,{contentHtml:null!==(a=n.contentHtml)&&void 0!==a?a:null===(c=n.translated)||void 0===c?void 0:c.contentHtml,contentPlain:null!==(s=n.contentPlain)&&void 0!==s?s:null===(u=n.translated)||void 0===u?void 0:u.contentPlain,mailTemplateData:null!=y?y:n.mailTemplateType.templateData,recipients:i()({},t,e),salesChannelId:o,mediaIds:r.getIds(),subject:null!==(l=n.subject)&&void 0!==l?l:null===(f=n.translated)||void 0===f?void 0:f.subject,senderMail:n.senderMail,senderName:null!==(h=n.senderName)&&void 0!==h?h:null===(p=n.translated)||void 0===p?void 0:p.senderName,documentIds:g,testMode:d},{headers:this.getBasicHeaders()}).then((function(t){return v.a.handleResponse(t)}))}},{key:"testMailTemplate",value:function(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[];return this.sendMailTemplate(t,t,e,n,r,!0,i)}},{key:"buildRenderPreview",value:function(t,e){var n="/_action/".concat(this.getApiBasePath(),"/build");return this.httpClient.post(n,{mailTemplateType:t,mailTemplate:e},{headers:this.getBasicHeaders()}).then((function(t){return v.a.handleResponse(t)}))}}]),n}(v.a);e.default=y},vVie:function(t,e,n){"use strict";n.r(e);var r=n("lSNA"),i=n.n(r),o=n("lwsE"),a=n.n(o),c=n("W8MJ"),s=n.n(c),u=n("iWIM"),l=n.n(u),f=n("7W2i"),h=n.n(f),p=n("a1gu"),d=n.n(p),v=n("Nsbk"),g=n.n(v);function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function b(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var w=Shopware.Classes.ApiService,O=function(t){h()(n,t);var e=b(n);function n(t,r){var i,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"recommendations";return a()(this,n),(i=e.call(this,t,r,o)).name="recommendationsService",i}return s()(n,[{key:"getBasicHeaders",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m(m({},l()(g()(n.prototype),"getBasicHeaders",this).call(this,t)),{},{"sw-language-id":Shopware.Context.api.languageId})}},{key:"getRecommendations",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/store/".concat(this.apiEndpoint),{params:n,headers:r}).then((function(t){return w.handleResponse(t)}))}},{key:"getRecommendationRegions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t,r=this.getBasicHeaders(e);return this.httpClient.get("/_action/store/recommendation-regions",{params:n,headers:r}).then((function(t){return w.handleResponse(t)}))}}]),n}(w);e.default=O},wjWR:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"_info/config-me";return i()(this,n),(o=e.call(this,t,r,a)).name="userConfigService",o}return a()(n,[{key:"search",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=this.getBasicHeaders(),n=Object.assign({},{keys:t});return this.httpClient.get(this.getApiBasePath(),{params:n,headers:e}).then((function(t){return p.a.handleResponse(t)})).catch((function(t){Shopware.Utils.debug.error(t)}))}},{key:"upsert",value:function(t){var e=this.getBasicHeaders();return this.httpClient.post(this.getApiBasePath(),t,{headers:e}).then((function(t){return p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v},wqx3:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"check-customer-email-valid";return i()(this,n),(o=e.call(this,t,r,a)).name="customerValidationService",o}return a()(n,[{key:"checkCustomerEmail",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=e,i=this.getBasicHeaders(n);return this.httpClient.post("/_admin/".concat(this.apiEndpoint),t,{params:r,headers:i}).then((function(t){return p.a.handleResponse(t)})).catch((function(t){throw t}))}}]),n}(p.a);e.default=v},yoR4:function(t,e,n){"use strict";n.r(e);var r=n("lwsE"),i=n.n(r),o=n("W8MJ"),a=n.n(o),c=n("7W2i"),s=n.n(c),u=n("a1gu"),l=n.n(u),f=n("Nsbk"),h=n.n(f),p=n("2iSf");function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=h()(t);if(e){var i=h()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}var v=function(t){s()(n,t);var e=d(n);function n(t,r){var o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"user";return i()(this,n),(o=e.call(this,t,r,a)).name="userRecoveryService",o.context=Shopware.Context,o}return a()(n,[{key:"createRecovery",value:function(t){var e="/_action/".concat(this.getApiBasePath(),"/user-recovery");return this.httpClient.post(e,{email:t},{params:{},headers:this.getBasicHeaders()}).then((function(t){p.a.handleResponse(t)}))}},{key:"checkHash",value:function(t){var e="/_action/".concat(this.getApiBasePath(),"/user-recovery/hash");return this.httpClient.get(e,{params:{hash:t},headers:this.getBasicHeaders()}).then((function(t){p.a.handleResponse(t)}))}},{key:"updateUserPassword",value:function(t,e,n){var r="/_action/".concat(this.getApiBasePath(),"/user-recovery/password");return this.httpClient.patch(r,{hash:t,password:e,passwordConfirm:n},{params:{},headers:this.getBasicHeaders()}).then((function(t){p.a.handleResponse(t)}))}}]),n}(p.a);e.default=v}},[[0,"runtime","vendors-node"]]]);
//# sourceMappingURL=commons.js.map