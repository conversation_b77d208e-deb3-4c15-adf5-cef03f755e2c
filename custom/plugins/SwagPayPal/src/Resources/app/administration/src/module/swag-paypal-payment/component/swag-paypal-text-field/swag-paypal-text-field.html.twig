{% block sw_text_field %}
    <sw-contextual-field
            v-bind="$attrs"
            v-on="$listeners"
            class="sw-field--text"
            :name="formFieldName"
            @inheritance-restore="$emit('inheritance-restore', $event)"
            @inheritance-remove="$emit('inheritance-remove', $event)">

        <template v-if="hasPrefix" #sw-contextual-field-prefix="{ disabled, identification }">
            <slot name="prefix" v-bind="{disabled, identification}"></slot>
        </template>

        <template #sw-field-input="{ identification, error, disabled, size, setFocusClass, removeFocusClass, hasSuffix, hasPrefix }">
            <input v-bind="$attrs"
                   v-on="additionalListeners"
                   type="text"
                   :name="identification"
                   :id="identification"
                   :disabled="disabled"
                   :value="currentValue"
                   :placeHolder="placeholder"
                   @input="onInput"
                   @change="onChange"
                   @focus="setFocusClass"
                   @blur="removeFocusClass">
        </template>

        <template v-if="copyable || hasSuffix" #sw-contextual-field-suffix="{disabled, identification}">
            <slot name="suffix" v-bind="{ identification }"></slot>
            <sw-field-copyable v-if="copyable"
                               :displayName="identification"
                               :copyableText="currentValue"
                               :tooltip="copyableTooltip">
            </sw-field-copyable>
        </template>
    </sw-contextual-field>
{% endblock %}
