<?xml version="1.0" encoding="UTF-8"?>

<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         bootstrap="src/TestBootstrap.php"
         cacheResult="false"
         executionOrder="random">

    <php>
        <ini name="error_reporting" value="-1" />
        <env name="SHELL_VERBOSITY" value="-1" />
        <env name="APP_ENV" value="test" />
        <env name="APP_DEBUG" value="1" />
        <env name="SHELL_VERBOSITY" value="-1" />

        <env name="SYMFONY_DEPRECATIONS_HELPER" value="weak" />
        <server name="MAILER_URL" value="null://localhost"/>
    </php>

    <coverage>
        <include>
            <directory suffix=".php">./vendor/shopware/administration</directory>
            <directory suffix=".php">./vendor/shopware/core</directory>
            <directory suffix=".php">./vendor/shopware/storefront</directory>
        </include>
        <exclude>
            <directory suffix=".php">./src/**/Test</directory>
            <directory suffix=".php">./vendor/shopware/core/**/Test</directory>
            <directory suffix=".php">./vendor/shopware/core/Migration</directory>
            <directory suffix=".php">./vendor/shopware/core/Flag</directory>
        </exclude>
    </coverage>

    <testsuites>
        <testsuite name="administration">
            <directory>vendor/shopware/administration</directory>
        </testsuite>

        <testsuite name="storefront">
            <directory>vendor/shopware/storefront</directory>
        </testsuite>

        <testsuite name="checkout">
            <directory>vendor/shopware/core/Checkout</directory>
        </testsuite>

        <testsuite name="content">
            <directory>vendor/shopware/core/Content</directory>
        </testsuite>

        <testsuite name="framework">
            <directory>vendor/shopware/core/Framework</directory>
        </testsuite>

        <testsuite name="migration">
            <directory>vendor/shopware/core/Migration</directory>
        </testsuite>

        <testsuite name="system">
            <directory>vendor/shopware/core/System</directory>
        </testsuite>

        <testsuite name="elasticsearch">
            <directory>vendor/shopware/elasticsearch</directory>
        </testsuite>
    </testsuites>

    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener">
            <arguments>
                <array>
                    <!-- set this option to 0 to disable the DebugClassLoader integration -->
                    <element key="debug-class-loader"><integer>0</integer></element>
                </array>
            </arguments>
        </listener>
        <listener class="JohnKary\PHPUnit\Listener\SpeedTrapListener" />
    </listeners>

    <!-- See also: vendor/shopware/platform/phpunit.xml.dist -->
</phpunit>
