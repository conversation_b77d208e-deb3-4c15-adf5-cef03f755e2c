{% block sw_first_run_wizard_modal %}
    <div class="swag-paypal-pos-wizard">
        <sw-modal v-if="showModal"
                  class="swag-paypal-pos-wizard-modal"
                  position-identifier="swag-paypal-pos-wizard-modal"
                  :title="title"
                  :variant="variant"
                  @modal-close="onCloseModal">

            {% block swag_paypal_pos_wizard_modal_content %}
                <div class="swag-paypal-pos-wizard__modal-content">
                    <sw-container class="swag-paypal-pos-wizard__columns"
                                  :class="{ 'swag-paypal-pos-wizard__columns--two': showSteps }">

                        {% block swag_paypal_pos_wizard_modal_content_steps %}
                            <div v-if="showSteps"
                                 class="swag-paypal-pos-wizard__steps">
                                <sw-step-display :initialItemVariants="stepInitialItemVariants"
                                                 :itemIndex="stepIndex"
                                                 :itemVariant="stepVariant">
                                    <sw-step-item v-for="pageName in displayStepperPages" :key="pageName">
                                        {{ $tc(`swag-paypal-pos.wizard.stepItemTitle.${pageName}`) }}
                                    </sw-step-item>
                                </sw-step-display>
                            </div>
                        {% endblock %}

                        {% block swag_paypal_pos_wizard_modal_content_page %}
                            <div class="swag-paypal-pos-wizard__page">
                                <router-view :salesChannel="salesChannel"
                                             :cloneSalesChannelId="cloneSalesChannelId"
                                             :saveSalesChannel="save"
                                             :isLoading="isLoading"
                                             @buttons-update="updateButtons"
                                             @frw-set-title="setTitle"
                                             @frw-finish="onFinishWizard"
                                             @toggle-loading="toggleLoading"
                                             @recreate-sales-channel="createNewSalesChannel"
                                             @update-clone-sales-channel="updateCloneSalesChannel">
                                </router-view>
                            </div>
                        {% endblock %}
                    </sw-container>
                </div>
            {% endblock %}

            {% block swag_paypal_pos_wizard_modal_footer %}
                <template #modal-footer>
                    <div class="swag-paypal-pos-wizard__footer">

                        {% block swag_paypal_pos_wizard_modal_footer_left %}
                            <div class="footer-left">
                                <sw-button v-for="button in buttons.left"
                                           size="small"
                                           :key="button.key"
                                           :disabled="button.disabled"
                                           :isLoading="isLoading"
                                           :variant="button.variant"
                                           @click="onButtonClick(button.action)">
                                    {{ button.label }}
                                </sw-button>
                            </div>
                        {% endblock %}

                        {% block swag_paypal_pos_wizard_modal_footer_right %}
                            <div class="footer-right">
                                <sw-button v-for="button in buttons.right"
                                           size="small"
                                           :key="button.key"
                                           :disabled="button.disabled"
                                           :isLoading="isLoading"
                                           :variant="button.variant"
                                           @click="onButtonClick(button.action)">
                                    {{ button.label }}
                                </sw-button>
                            </div>
                        {% endblock %}
                    </div>
                </template>
            {% endblock %}
        </sw-modal>
    </div>
{% endblock %}

