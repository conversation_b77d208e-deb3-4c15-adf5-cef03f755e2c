<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>General configuration</title>
        <title lang="de-DE">Allgemeine Konfiguration</title>

        <input-field type="bool">
            <name>enableIntegration</name>
            <label>Enable the integration</label>
            <label lang="de-DE">Aktivieren Sie die Integration</label>
            <helpText>Activate the integration with Odoo and send data to Odoo.</helpText>
            <helpText lang="de-DE">Aktivieren Sie die Integration mit Odoo und senden Sie Daten an Odoo.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>
    </card>

    <card>
        <title>Odoo configuration</title>
        <title lang="de-DE">Odoo Konfiguration</title>

        <input-field type="url">
            <name>serverUrl</name>
            <label>Odoo server URL</label>
            <label lang="de-DE">Odoo-Server-URL</label>
            <helpText>Odoo server URL.</helpText>
            <helpText lang="de-DE">Odoo-Server-URL.</helpText>
            <defaultValue/>
        </input-field>
        <input-field type="text">
            <name>username</name>
            <label>Odoo username</label>
            <label lang="de-DE">Odoo-Benutzername</label>
            <helpText>Odoo username of API user.</helpText>
            <helpText lang="de-DE">Odoo-Benutzername des API-Benutzers.</helpText>
            <defaultValue/>
        </input-field>
        <input-field type="password">
            <name>password</name>
            <label>Odoo API key/Password</label>
            <label lang="de-DE">Odoo API-Schlüssel/Passwort</label>
            <helpText>Odoo API key of the user, or optionally you can also use the password.</helpText>
            <helpText lang="de-DE">Odoo API-Schlüssel des Benutzers, optional können Sie auch das Passwort verwenden.
            </helpText>
            <defaultValue/>
        </input-field>
        <input-field type="text">
            <name>database</name>
            <label>Odoo database name</label>
            <label lang="de-DE">Name der Odoo-Datenbank</label>
            <helpText>Odoo database name.</helpText>
            <helpText lang="de-DE">Name der Odoo-Datenbank.</helpText>
            <defaultValue/>
        </input-field>

        <component name="bs-odoo-api-verify-button">
            <name>odooApiVerify</name>
        </component>
    </card>

    <card>
        <title>Other configuration</title>
        <title lang="de-DE">Andere Konfiguration</title>

        <input-field type="text">
            <name>uid</name>
            <label>Odoo UID</label>
            <label lang="de-DE"></label>
            <helpText>UID of the odoo user.</helpText>
            <helpText lang="de-DE"></helpText>
            <defaultValue/>
            <disabled>true</disabled>
        </input-field>

        <input-field type="int">
            <name>companyId</name>
            <label>Odoo Company Id</label>
            <label lang="de-DE">Odoo Company ID</label>
            <helpText>Company id of the odoo company, in which all data from shopware need to be synced.</helpText>
            <helpText lang="de-DE">Unternehmens -ID des Odoo -Unternehmens, in dem alle Daten aus Shopware synchronisiert werden müssen.</helpText>
            <defaultValue/>
        </input-field>

        <input-field type="int">
            <name>defaultCategoryId</name>
            <label>Default Odoo Category Id</label>
            <label lang="de-DE">Standard -ODOO -Kategorie -ID</label>
            <helpText>Category id of the odoo, in which new products from shopware, If not set it will leave empty, so odoo will show all in category of product.</helpText>
            <helpText lang="de-DE">Kategorie -ID des Odoo, in dem neue Produkte aus Shopware, falls nicht festgelegt, leer bleibt, sodass Odoo alles in Produktkategorie zeigt.</helpText>
            <defaultValue/>
        </input-field>

        <input-field type="int">
            <name>defaultTaxId</name>
            <label>Default Odoo Tax Id</label>
            <label lang="de-DE">Standard -Odoo -Steuer -ID</label>
            <helpText>Tax id of the odoo, in which new products from shopware set default, If not set use default from odoo side.</helpText>
            <helpText lang="de-DE">Steuer -ID des Odoo, in dem neue Produkte aus Shopware standardmäßig sein müssen, wenn nicht festgelegt wird, verwenden Sie die Standardeinstellung von der Odoo -Seite.</helpText>
            <defaultValue/>
        </input-field>

        <input-field type="int">
            <name>defaultPricelistId</name>
            <label>Default Odoo Pricelist Id</label>
            <label lang="de-DE">Standard Odoo Pricelist ID</label>
            <helpText>Price list id of the odoo, New customer from shopware use it as default price list id, if not set it will get default from odoo side.</helpText>
            <helpText lang="de-DE">Preislisten -ID des Odoo, neuer Kunde aus Shopware verwenden es als Standardpreislisten -ID, falls festgelegt wird, wird sie von Odoo -Seite standardmäßig erhalten.</helpText>
            <defaultValue/>
        </input-field>
    </card>
</config>
