{% block swag_paypal_pos_detail_settings %}
    <div class="swag-paypal-pos-detail-settings">

        {% block swag_paypal_pos_detail_settings_alert %}
            <sw-alert class="swag-paypal-pos-detail-settings__save-alert">
                {{ $tc('swag-paypal-pos.detail.settings.saveAlert') }}
            </sw-alert>
        {% endblock %}

        {% block swag_paypal_pos_detail_settings_basic %}
            <sw-card position-identifier="swag-paypal-pos-detail-settings-basic"
                     :isLoading="isLoading"
                     :title="$tc('swag-paypal-pos.detail.settings.titleGeneralSettings')">
                <sw-container columns="1fr 200px" gap="0px 30px">

                    {% block swag_paypal_pos_detail_settings_input_name %}
                        <sw-text-field v-model="salesChannel.name"
                                       required
                                       type="text"
                                       validation="required"
                                       :error="salesChannelNameError"
                                       :disabled="!acl.can('sales_channel.editor')"
                                       :label="$tc('sw-sales-channel.detail.labelInputName')"
                                       :placeholder="placeholder(salesChannel, 'name', $tc('sw-sales-channel.detail.placeholderName'))">
                        </sw-text-field>
                    {% endblock %}

                    {% block swag_paypal_pos_detail_settings_input_active %}
                        <sw-switch-field v-model="salesChannel.active"
                                         bordered
                                         :disabled="!acl.can('sales_channel.editor')"
                                         :label="$tc('sw-sales-channel.detail.labelInputActive')"
                                         :helpText="$tc('swag-paypal-pos.detail.settings.activeHelpText')">
                        </sw-switch-field>
                    {% endblock %}

                </sw-container>

                {% block swag_paypal_pos_detail_settings_input_media_domain %}
                    <swag-paypal-pos-url-field v-model="swagPaypalPosSalesChannel.mediaDomain"
                                               required
                                               validation="required"
                                               :error="swagPaypalPosSalesChannelMediaDomainError"
                                               :disabled="!acl.can('sales_channel.editor')"
                                               :label="$tc('swag-paypal-pos.detail.settings.domain')"
                                               :helpText="$tc('swag-paypal-pos.detail.settings.domainHelpText')"
                                               @input="forceUpdate">
                    </swag-paypal-pos-url-field>
                {% endblock %}

                {% block swag_paypal_pos_detail_settings_input_language %}
                    <sw-entity-single-select v-model="salesChannel.languageId"
                                             entity="language"
                                             required
                                             :disabled="!acl.can('sales_channel.editor')"
                                             :label="$tc('global.entities.language')"
                                             @change="changeLanguage">
                    </sw-entity-single-select>
                {% endblock %}

                {% block swag_paypal_pos_detail_settings_input_product_stream %}
                    {# ToDo PPI-56 - implement support for product streams #}
                    <sw-entity-single-select v-if="false"
                                             v-model="swagPaypalPosSalesChannel.productStreamId"
                                             entity="product_stream"
                                             :disabled="!acl.can('sales_channel.editor')"
                                             :label="$tc('swag-paypal-pos.detail.settings.productStream')"
                                             @change="forceUpdate">
                    </sw-entity-single-select>
                {% endblock %}

            </sw-card>
        {% endblock %}

        {% block swag_paypal_pos_detail_settings_sync %}
            <div class="sw-sales-channel-detail-sync">
                <sw-card position-identifier="swag-paypal-pos-detail-settings-sync"
                         :isLoading="isLoading"
                         :title="$tc('swag-paypal-pos.detail.settings.titleSync')">

                    {% block swag_paypal_pos_detail_settings_sync_input %}
                        <sw-radio-field
                            v-model="salesChannel.extensions.paypalPosSalesChannel.replace"
                            :label="$tc('swag-paypal-pos.wizard.syncLibrary.description')"
                            class="swag-paypal-pos-boolean-radio"
                            :options="optionsReplace"
                            :disabled="!acl.can('sales_channel.editor')"
                            @input="forceUpdate">
                        </sw-radio-field>
                    {% endblock %}

                </sw-card>
            </div>
        {% endblock %}

        {% block swag_paypal_pos_detail_settings_prices %}
            <div class="sw-sales-channel-detail-prices">
                <sw-card position-identifier="swag-paypal-pos-detail-settings-prices"
                         :isLoading="isLoading"
                         :title="$tc('swag-paypal-pos.detail.settings.titlePrices')">

                    {% block swag_paypal_pos_detail_settings_prices_input %}
                        <swag-paypal-pos-boolean-radio
                            v-model="swagPaypalPosSalesChannel.syncPrices"
                            class="swag-paypal-pos-detail-settings__sync-prices-input"
                            :label="$tc('swag-paypal-pos.wizard.syncPrices.description')"
                            :optionTrue="optionSyncPrices"
                            :optionFalse="optionNotSyncPrices"
                            :disabled="!acl.can('sales_channel.editor')"
                            @input="forceUpdate">
                        </swag-paypal-pos-boolean-radio>
                    {% endblock %}

                </sw-card>
            </div>
        {% endblock %}

        {% block swag_paypal_pos_detail_settings_credentials %}
            <div class="sw-sales-channel-detail-credentials">
                <sw-card position-identifier="swag-paypal-pos-detail-settings-credentials"
                         :isLoading="isLoading"
                         :title="$tc('swag-paypal-pos.detail.settings.titleCredentials')">

                    {% block swag_paypal_pos_detail_settings_credentials_apikey %}
                        <sw-password-field v-model="swagPaypalPosSalesChannel.apiKey"
                                           class="swag-paypal-pos-detail-settings__apikey-field"
                                           required
                                           :label="$tc('swag-paypal-pos.authentication.labelApiKey')"
                                           :disabled="!acl.can('sales_channel.editor')"
                                           :placeholder="$tc('swag-paypal-pos.authentication.placeholderApiKey')"
                                           @input="forceUpdate">
                            <template #suffix>

                                {% block swag_paypal_pos_detail_settings_credentials_apikey_suffix %}
                                    <sw-icon name="regular-low-vision"
                                             class="swag-paypal-pos-detail-settings__apikey-suffix"
                                             size="22px">
                                    </sw-icon>
                                {% endblock %}
                            </template>
                        </sw-password-field>
                    {% endblock %}

                    {% block swag_paypal_pos_detail_settings_credentials_apikey_description %}
                        <div class="swag-paypal-pos-detail-settings__apikey-description">

                            {% block swag_paypal_pos_detail_settings_credentials_apikey_description_text %}
                                <div class="swag-paypal-pos-detail-settings__apikey-description-text">
                                    {{ $tc('swag-paypal-pos.authentication.apiKeyDescription') }}
                                </div>
                            {% endblock %}

                            {% block swag_paypal_pos_detail_settings_credentials_apikey_description_link %}
                                <sw-external-link v-if="acl.can('sales_channel.editor')"
                                                  :href="apiKeyUrl"
                                                  class="swag-paypal-pos-detail-settings__apikey-description-link">

                                    {% block swag_paypal_pos_detail_settings_credentials_apikey_description_link_text %}
                                        {{ $tc('swag-paypal-pos.authentication.buttonGenerateKey') }}
                                    {% endblock %}
                                </sw-external-link>
                            {% endblock %}
                        </div>
                    {% endblock %}

                </sw-card>
            </div>
        {% endblock %}

        {% block swag_paypal_pos_detail_settings_options %}
            <sw-card position-identifier="swag-paypal-pos-detail-settings-options"
                     :title="$tc('swag-paypal-pos.detail.settings.optionsTitle')"
                     :isLoading="isLoading">
                <template #grid>
                    <sw-container rows="auto auto">

                        {% block swag_paypal_pos_detail_settings_options_reset %}
                            <sw-card-section>
                                <sw-container columns="1fr 175px" gap="0px 30px" class="swag-paypal-pos-detail-settings__reset-sync">

                                    {% block sw_sales_channel_detail_base_options_reset_text %}
                                        <div class="swag-paypal-pos-detail-settings__options-description-text">
                                            {{ $tc('swag-paypal-pos.detail.settings.reset.descriptionText') }}
                                        </div>
                                    {% endblock %}

                                    {% block sw_sales_channel_detail_base_options_reset_button %}
                                        <div class="swag-paypal-pos-detail-settings__options-button">
                                            <sw-button size="small"
                                                       :disabled="!acl.can('sales_channel.editor')"
                                                       @click="showResetModal = true">
                                                {{ $tc('swag-paypal-pos.detail.settings.reset.buttonTitle') }}
                                            </sw-button>
                                        </div>
                                    {% endblock %}

                                </sw-container>
                            </sw-card-section>
                        {% endblock %}

                        {% block swag_paypal_pos_detail_settings_options_delete %}
                            <sw-card-section divider="top">
                                <sw-container columns="1fr 175px" gap="0px 30px" class="sw-sales-channel-detail-settings__delete-sales-channel">

                                    {% block sw_sales_channel_detail_base_options_delete_text %}
                                        <div class="swag-paypal-pos-detail-settings__options-description-text">
                                            <strong>{{ $tc('global.default.warning') }}</strong>
                                            {{ $tc('sw-sales-channel.detail.textDeleteSalesChannelWarning') }}
                                        </div>
                                    {% endblock %}

                                    {% block sw_sales_channel_detail_base_options_delete_button %}
                                        <div class="swag-paypal-pos-detail-settings__options-button">
                                            <sw-button
                                                variant="danger"
                                                size="small"
                                                :disabled="!acl.can('sales_channel.deleter')"
                                                @click="showDeleteModal = true">
                                                {{ $tc('sw-sales-channel.detail.buttonDelete') }}
                                            </sw-button>
                                        </div>
                                    {% endblock %}

                                </sw-container>
                            </sw-card-section>
                        {% endblock %}

                    </sw-container>
                </template>

                {% block swag_paypal_pos_detail_settings_options_reset_modal %}
                    <sw-modal v-if="showResetModal"
                              class="swag-paypal-pos-detail-settings__reset-sync-modal"
                              ref="modal"
                              variant="small"
                              :title="$tc('swag-paypal-pos.detail.settings.reset.modalTitle')"
                              @modal-close="showResetModal = false">

                        {% block swag_paypal_pos_detail_settings_options_reset_modal_confirmtext %}
                            <p class="swag-paypal-pos-detail-settings__reset-modal-confirm-text">
                                {{ $tc('swag-paypal-pos.detail.settings.reset.modalConfirmText') }}
                            </p>
                            <p class="swag-paypal-pos-detail-settings__reset-modal-name">
                                <strong>{{ placeholder(salesChannel, 'name') }}</strong>
                            </p>
                            <p>{{ $tc('swag-paypal-pos.detail.settings.reset.modalTextInfo') }}</p>
                        {% endblock %}

                        {% block sw_sales_channel_detail_options_reset_modal_footer %}
                            <template #modal-footer>

                                {% block swag_paypal_pos_detail_settings_options_reset_modal_abort %}
                                    <sw-button size="small" @click="showResetModal = false">
                                        {{ $tc('global.default.cancel') }}
                                    </sw-button>
                                {% endblock %}

                                {% block swag_paypal_pos_detail_settings_options_reset_modal_confirm %}
                                    <sw-button size="small"
                                               variant="danger"
                                               @click="onConfirmReset">
                                        {{ $tc('swag-paypal-pos.detail.settings.reset.modalButtonConfirm') }}
                                    </sw-button>
                                {% endblock %}

                            </template>
                        {% endblock %}

                    </sw-modal>
                {% endblock %}

                {% block swag_paypal_pos_detail_settings_options_delete_modal %}
                    <sw-modal v-if="showDeleteModal"
                              class="sw-sales-channel-detail-settings__delete-modal"
                              ref="modal"
                              variant="small"
                              :title="$tc('sw-sales-channel.detail.deleteModalTitle')"
                              @modal-close="showDeleteModal = false">

                        {% block swag_paypal_pos_detail_settings_options_delete_modal_confirmtext %}
                            <p class="sw-sales-channel-detail-settings__delete-modal-confirm-text">
                                {{ $tc('sw-sales-channel.detail.textConfirmDelete') }}
                            </p>
                            <p class="sw-sales-channel-detail-settings__delete-modal-name">
                                <strong>{{ placeholder(salesChannel, 'name') }}</strong>
                            </p>
                            <p>{{ $tc('sw-sales-channel.detail.textDeleteInfo') }}</p>
                        {% endblock %}

                        {% block sw_sales_channel_detail_options_delete_modal_footer %}
                            <template #modal-footer>

                                {% block swag_paypal_pos_detail_settings_options_delete_modal_abort %}
                                    <sw-button size="small" @click="showDeleteModal = false">
                                        {{ $tc('global.default.cancel') }}
                                    </sw-button>
                                {% endblock %}

                                {% block swag_paypal_pos_detail_settings_options_delete_modal_confirm %}
                                    <sw-button size="small"
                                               variant="danger"
                                               @click="onConfirmDelete">
                                        {{ $tc('sw-sales-channel.detail.buttonConfirmDelete') }}
                                    </sw-button>
                                {% endblock %}

                            </template>
                        {% endblock %}

                    </sw-modal>
                {% endblock %}

            </sw-card>
        {% endblock %}

    </div>
{% endblock %}
