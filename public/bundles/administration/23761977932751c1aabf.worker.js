/*! For license information please see 23761977932751c1aabf.worker.js.LICENSE.txt */
!function(A){var e={};function t(r){if(e[r])return e[r].exports;var n=e[r]={i:r,l:!1,exports:{}};return A[r].call(n.exports,n,n.exports,t),n.l=!0,n.exports}t.m=A,t.c=e,t.d=function(A,e,r){t.o(A,e)||Object.defineProperty(A,e,{enumerable:!0,get:r})},t.r=function(A){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(A,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(A,"__esModule",{value:!0})},t.t=function(A,e){if(1&e&&(A=t(A)),8&e)return A;if(4&e&&"object"==typeof A&&A&&A.__esModule)return A;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:A}),2&e&&"string"!=typeof A)for(var n in A)t.d(r,n,function(e){return A[e]}.bind(null,n));return r},t.n=function(A){var e=A&&A.__esModule?function(){return A.default}:function(){return A};return t.d(e,"a",e),e},t.o=function(A,e){return Object.prototype.hasOwnProperty.call(A,e)},t.p="bundles/administration/",t(t.s="W5m1")}({"+2B0":function(A,e,t){"use strict";var r=t("eRe6");function n(A){r.call(this,null==A?"canceled":A,r.ERR_CANCELED),this.name="CanceledError"}t("xTJ+").inherits(n,r,{__CANCEL__:!0}),A.exports=n},"+6XX":function(A,e,t){var r=t("y1pI");A.exports=function(A){return r(this.__data__,A)>-1}},"+K+b":function(A,e,t){var r=t("JHRd");A.exports=function(A){var e=new A.constructor(A.byteLength);return new r(e).set(new r(A)),e}},"+Qka":function(A,e,t){var r=t("fmRc"),n=t("t2Dn"),o=t("cq/+"),i=t("T1AV"),s=t("GoyQ"),B=t("mTTR"),a=t("itsj");A.exports=function A(e,t,c,u,l){e!==t&&o(t,(function(o,B){if(l||(l=new r),s(o))i(e,t,B,c,A,u,l);else{var g=u?u(a(e,B),o,B+"",e,t,l):void 0;void 0===g&&(g=o),n(e,B,g)}}),B)}},"+iFO":function(A,e,t){var r=t("dTAl"),n=t("LcsW"),o=t("6sVZ");A.exports=function(A){return"function"!=typeof A.constructor||o(A)?{}:r(n(A))}},"/9aa":function(A,e,t){var r=t("NykK"),n=t("ExA7");A.exports=function(A){return"symbol"==typeof A||n(A)&&"[object Symbol]"==r(A)}},"03A+":function(A,e,t){var r=t("JTzB"),n=t("ExA7"),o=Object.prototype,i=o.hasOwnProperty,s=o.propertyIsEnumerable,B=r(function(){return arguments}())?r:function(A){return n(A)&&i.call(A,"callee")&&!s.call(A,"callee")};A.exports=B},"0Cz8":function(A,e,t){var r=t("Xi7e"),n=t("ebwN"),o=t("e4Nc");A.exports=function(A,e){var t=this.__data__;if(t instanceof r){var i=t.__data__;if(!n||i.length<199)return i.push([A,e]),this.size=++t.size,this;t=this.__data__=new o(i)}return t.set(A,e),this.size=t.size,this}},"0ycA":function(A,e){A.exports=function(){return[]}},"1+5i":function(A,e,t){var r=t("w/wX"),n=t("sEf8"),o=t("mdPL"),i=o&&o.isSet,s=i?n(i):r;A.exports=s},"1hJj":function(A,e,t){var r=t("e4Nc"),n=t("ftKO"),o=t("3A9y");function i(A){var e=-1,t=null==A?0:A.length;for(this.__data__=new r;++e<t;)this.add(A[e])}i.prototype.add=i.prototype.push=n,i.prototype.has=o,A.exports=i},"2SVd":function(A,e,t){"use strict";A.exports=function(A){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(A)}},"2gN3":function(A,e,t){var r=t("Kz5y")["__core-js_shared__"];A.exports=r},"3A9y":function(A,e){A.exports=function(A){return this.__data__.has(A)}},"3Fdi":function(A,e){var t=Function.prototype.toString;A.exports=function(A){if(null!=A){try{return t.call(A)}catch(A){}try{return A+""}catch(A){}}return""}},"3L66":function(A,e,t){var r=t("MMmD"),n=t("ExA7");A.exports=function(A){return n(A)&&r(A)}},"44Ds":function(A,e,t){var r=t("e4Nc");function n(A,e){if("function"!=typeof A||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var t=function(){var r=arguments,n=e?e.apply(this,r):r[0],o=t.cache;if(o.has(n))return o.get(n);var i=A.apply(this,r);return t.cache=o.set(n,i)||o,i};return t.cache=new(n.Cache||r),t}n.Cache=r,A.exports=n},"4Cfb":function(A,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.formatCookie=void 0;var r=function(A){var e=A.path,t=A.domain,r=A.expires,n=A.secure,o=function(A){var e=A.sameSite;return void 0===e?null:["none","lax","strict"].indexOf(e.toLowerCase())>=0?e:null}(A);return[null==e?"":";path="+e,null==t?"":";domain="+t,null==r?"":";expires="+r.toUTCString(),void 0===n||!1===n?"":";secure",null===o?"":";SameSite="+o].join("")};e.formatCookie=function(A,e,t){return[encodeURIComponent(A),"=",encodeURIComponent(e),r(t)].join("")}},"4kuk":function(A,e,t){var r=t("SfRM"),n=t("Hvzi"),o=t("u8Dt"),i=t("ekgI"),s=t("JSQU");function B(A){var e=-1,t=null==A?0:A.length;for(this.clear();++e<t;){var r=A[e];this.set(r[0],r[1])}}B.prototype.clear=r,B.prototype.delete=n,B.prototype.get=o,B.prototype.has=i,B.prototype.set=s,A.exports=B},"4qC0":function(A,e,t){var r=t("NykK"),n=t("Z0cm"),o=t("ExA7");A.exports=function(A){return"string"==typeof A||!n(A)&&o(A)&&"[object String]"==r(A)}},"4sDh":function(A,e,t){var r=t("4uTw"),n=t("03A+"),o=t("Z0cm"),i=t("wJg7"),s=t("shjB"),B=t("9Nap");A.exports=function(A,e,t){for(var a=-1,c=(e=r(e,A)).length,u=!1;++a<c;){var l=B(e[a]);if(!(u=null!=A&&t(A,l)))break;A=A[l]}return u||++a!=c?u:!!(c=null==A?0:A.length)&&s(c)&&i(l,c)&&(o(A)||n(A))}},"4uTw":function(A,e,t){var r=t("Z0cm"),n=t("9ggG"),o=t("GNiM"),i=t("dt0z");A.exports=function(A,e){return r(A)?A:n(A,e)?[A]:o(i(A))}},"5GeT":function(A,e,t){"use strict";(function(e){var r=t("xTJ+");A.exports=function(A,t){t=t||new FormData;var n=[];function o(A){return null===A?"":r.isDate(A)?A.toISOString():r.isArrayBuffer(A)||r.isTypedArray(A)?"function"==typeof Blob?new Blob([A]):e.from(A):A}return function A(e,i){if(r.isPlainObject(e)||r.isArray(e)){if(-1!==n.indexOf(e))throw Error("Circular reference detected in "+i);n.push(e),r.forEach(e,(function(e,n){if(!r.isUndefined(e)){var s,B=i?i+"."+n:n;if(e&&!i&&"object"==typeof e)if(r.endsWith(n,"{}"))e=JSON.stringify(e);else if(r.endsWith(n,"[]")&&(s=r.toArray(e)))return void s.forEach((function(A){!r.isUndefined(A)&&t.append(B,o(A))}));A(e,B)}})),n.pop()}else t.append(i,o(e))}(A),t}}).call(this,t("HDXh").Buffer)},"5Tg0":function(A,e,t){(function(A){var r=t("Kz5y"),n=e&&!e.nodeType&&e,o=n&&"object"==typeof A&&A&&!A.nodeType&&A,i=o&&o.exports===n?r.Buffer:void 0,s=i?i.allocUnsafe:void 0;A.exports=function(A,e){if(e)return A.slice();var t=A.length,r=s?s(t):new A.constructor(t);return A.copy(r),r}}).call(this,t("YuTi")(A))},"5oMp":function(A,e,t){"use strict";A.exports=function(A,e){return e?A.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):A}},"6sVZ":function(A,e){var t=Object.prototype;A.exports=function(A){var e=A&&A.constructor;return A===("function"==typeof e&&e.prototype||t)}},"77Zs":function(A,e,t){var r=t("Xi7e");A.exports=function(){this.__data__=new r,this.size=0}},"7GkX":function(A,e,t){var r=t("b80T"),n=t("A90E"),o=t("MMmD");A.exports=function(A){return o(A)?r(A):n(A)}},"7Ix3":function(A,e){A.exports=function(A){var e=[];if(null!=A)for(var t in Object(A))e.push(t);return e}},"7W2i":function(A,e,t){var r=t("SksO");A.exports=function(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");A.prototype=Object.create(e&&e.prototype,{constructor:{value:A,writable:!0,configurable:!0}}),Object.defineProperty(A,"prototype",{writable:!1}),e&&r(A,e)},A.exports.__esModule=!0,A.exports.default=A.exports},"7fqy":function(A,e){A.exports=function(A){var e=-1,t=Array(A.size);return A.forEach((function(A,r){t[++e]=[r,A]})),t}},"88Gu":function(A,e){var t=Date.now;A.exports=function(A){var e=0,r=0;return function(){var n=t(),o=16-(n-r);if(r=n,o>0){if(++e>=800)return arguments[0]}else e=0;return A.apply(void 0,arguments)}}},"8oxB":function(A,e){var t,r,n=A.exports={};function o(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function s(A){if(t===setTimeout)return setTimeout(A,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(A,0);try{return t(A,0)}catch(e){try{return t.call(null,A,0)}catch(e){return t.call(this,A,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:o}catch(A){t=o}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(A){r=i}}();var B,a=[],c=!1,u=-1;function l(){c&&B&&(c=!1,B.length?a=B.concat(a):u=-1,a.length&&g())}function g(){if(!c){var A=s(l);c=!0;for(var e=a.length;e;){for(B=a,a=[];++u<e;)B&&B[u].run();u=-1,e=a.length}B=null,c=!1,function(A){if(r===clearTimeout)return clearTimeout(A);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(A);try{r(A)}catch(e){try{return r.call(null,A)}catch(e){return r.call(this,A)}}}(A)}}function f(A,e){this.fun=A,this.array=e}function w(){}n.nextTick=function(A){var e=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];a.push(new f(A,e)),1!==a.length||c||s(g)},f.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=w,n.addListener=w,n.once=w,n.off=w,n.removeListener=w,n.removeAllListeners=w,n.emit=w,n.prependListener=w,n.prependOnceListener=w,n.listeners=function(A){return[]},n.binding=function(A){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(A){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},"9Nap":function(A,e,t){var r=t("/9aa");A.exports=function(A){if("string"==typeof A||r(A))return A;var e=A+"";return"0"==e&&1/A==-Infinity?"-0":e}},"9ggG":function(A,e,t){var r=t("Z0cm"),n=t("/9aa"),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,i=/^\w*$/;A.exports=function(A,e){if(r(A))return!1;var t=typeof A;return!("number"!=t&&"symbol"!=t&&"boolean"!=t&&null!=A&&!n(A))||(i.test(A)||!o.test(A)||null!=e&&A in Object(e))}},"9rSQ":function(A,e,t){"use strict";var r=t("xTJ+");function n(){this.handlers=[]}n.prototype.use=function(A,e,t){return this.handlers.push({fulfilled:A,rejected:e,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1},n.prototype.eject=function(A){this.handlers[A]&&(this.handlers[A]=null)},n.prototype.forEach=function(A){r.forEach(this.handlers,(function(e){null!==e&&A(e)}))},A.exports=n},A90E:function(A,e,t){var r=t("6sVZ"),n=t("V6Ve"),o=Object.prototype.hasOwnProperty;A.exports=function(A){if(!r(A))return n(A);var e=[];for(var t in Object(A))o.call(A,t)&&"constructor"!=t&&e.push(t);return e}},AP2z:function(A,e,t){var r=t("nmnc"),n=Object.prototype,o=n.hasOwnProperty,i=n.toString,s=r?r.toStringTag:void 0;A.exports=function(A){var e=o.call(A,s),t=A[s];try{A[s]=void 0;var r=!0}catch(A){}var n=i.call(A);return r&&(e?A[s]=t:delete A[s]),n}},B8du:function(A,e){A.exports=function(){return!1}},BiGR:function(A,e,t){var r=t("nmnc"),n=t("03A+"),o=t("Z0cm"),i=r?r.isConcatSpreadable:void 0;A.exports=function(A){return o(A)||n(A)||!!(i&&A&&A[i])}},BkRI:function(A,e,t){var r=t("OBhP");A.exports=function(A){return r(A,5)}},CH3K:function(A,e){A.exports=function(A,e){for(var t=-1,r=e.length,n=A.length;++t<r;)A[n+t]=e[t];return A}},CgaS:function(A,e,t){"use strict";var r=t("xTJ+"),n=t("MLWZ"),o=t("9rSQ"),i=t("UnBK"),s=t("SntB"),B=t("g7np"),a=t("hIuj"),c=a.validators;function u(A){this.defaults=A,this.interceptors={request:new o,response:new o}}u.prototype.request=function(A,e){"string"==typeof A?(e=e||{}).url=A:e=A||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&a.assertOptions(t,{silentJSONParsing:c.transitional(c.boolean),forcedJSONParsing:c.transitional(c.boolean),clarifyTimeoutError:c.transitional(c.boolean)},!1);var r=[],n=!0;this.interceptors.request.forEach((function(A){"function"==typeof A.runWhen&&!1===A.runWhen(e)||(n=n&&A.synchronous,r.unshift(A.fulfilled,A.rejected))}));var o,B=[];if(this.interceptors.response.forEach((function(A){B.push(A.fulfilled,A.rejected)})),!n){var u=[i,void 0];for(Array.prototype.unshift.apply(u,r),u=u.concat(B),o=Promise.resolve(e);u.length;)o=o.then(u.shift(),u.shift());return o}for(var l=e;r.length;){var g=r.shift(),f=r.shift();try{l=g(l)}catch(A){f(A);break}}try{o=i(l)}catch(A){return Promise.reject(A)}for(;B.length;)o=o.then(B.shift(),B.shift());return o},u.prototype.getUri=function(A){A=s(this.defaults,A);var e=B(A.baseURL,A.url);return n(e,A.params,A.paramsSerializer)},r.forEach(["delete","get","head","options"],(function(A){u.prototype[A]=function(e,t){return this.request(s(t||{},{method:A,url:e,data:(t||{}).data}))}})),r.forEach(["post","put","patch"],(function(A){function e(e){return function(t,r,n){return this.request(s(n||{},{method:A,headers:e?{"Content-Type":"multipart/form-data"}:{},url:t,data:r}))}}u.prototype[A]=e(),u.prototype[A+"Form"]=e(!0)})),A.exports=u},Cwc5:function(A,e,t){var r=t("NKxu"),n=t("Npjl");A.exports=function(A,e){var t=n(A,e);return r(t)?t:void 0}},D1y2:function(A,e,t){var r=t("FZoo");A.exports=function(A,e,t){return null==A?A:r(A,e,t)}},DSRE:function(A,e,t){(function(A){var r=t("Kz5y"),n=t("B8du"),o=e&&!e.nodeType&&e,i=o&&"object"==typeof A&&A&&!A.nodeType&&A,s=i&&i.exports===o?r.Buffer:void 0,B=(s?s.isBuffer:void 0)||n;A.exports=B}).call(this,t("YuTi")(A))},DfZB:function(A,e,t){"use strict";A.exports=function(A){return function(e){return A.apply(null,e)}}},"Dw+G":function(A,e,t){var r=t("juv8"),n=t("mTTR");A.exports=function(A,e){return A&&r(e,n(e),A)}},"E+oP":function(A,e,t){var r=t("A90E"),n=t("QqLw"),o=t("03A+"),i=t("Z0cm"),s=t("MMmD"),B=t("DSRE"),a=t("6sVZ"),c=t("c6wG"),u=Object.prototype.hasOwnProperty;A.exports=function(A){if(null==A)return!0;if(s(A)&&(i(A)||"string"==typeof A||"function"==typeof A.splice||B(A)||c(A)||o(A)))return!A.length;var e=n(A);if("[object Map]"==e||"[object Set]"==e)return!A.size;if(a(A))return!r(A).length;for(var t in A)if(u.call(A,t))return!1;return!0}},E2jh:function(A,e,t){var r,n=t("2gN3"),o=(r=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";A.exports=function(A){return!!o&&o in A}},EA7m:function(A,e,t){var r=t("zZ0H"),n=t("Ioao"),o=t("wclG");A.exports=function(A,e){return o(n(A,e,r),A+"")}},EEGq:function(A,e,t){var r=t("juv8"),n=t("oCl/");A.exports=function(A,e){return r(A,n(A),e)}},EpBk:function(A,e){A.exports=function(A){var e=typeof A;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==A:null===A}},ExA7:function(A,e){A.exports=function(A){return null!=A&&"object"==typeof A}},FYKA:function(A,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=t("wTB4");Object.defineProperty(e,"CookieStorage",{enumerable:!0,get:function(){return r.CookieStorage}});var n=t("4Cfb");Object.defineProperty(e,"formatCookie",{enumerable:!0,get:function(){return n.formatCookie}});var o=t("LbSc");Object.defineProperty(e,"parseCookies",{enumerable:!0,get:function(){return o.parseCookies}})},FZoo:function(A,e,t){var r=t("MrPd"),n=t("4uTw"),o=t("wJg7"),i=t("GoyQ"),s=t("9Nap");A.exports=function(A,e,t,B){if(!i(A))return A;for(var a=-1,c=(e=n(e,A)).length,u=c-1,l=A;null!=l&&++a<c;){var g=s(e[a]),f=t;if("__proto__"===g||"constructor"===g||"prototype"===g)return A;if(a!=u){var w=l[g];void 0===(f=B?B(w,g,l):void 0)&&(f=i(w)?w:o(e[a+1])?[]:{})}r(l,g,f),l=l[g]}return A}},FfPP:function(A,e,t){var r=t("idmN"),n=t("hgQt");A.exports=function(A,e){return r(A,e,(function(e,t){return n(A,t)}))}},G6z8:function(A,e,t){var r=t("fR/l"),n=t("oCl/"),o=t("mTTR");A.exports=function(A){return r(A,o,n)}},GNiM:function(A,e,t){var r=t("I01J"),n=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g,i=r((function(A){var e=[];return 46===A.charCodeAt(0)&&e.push(""),A.replace(n,(function(A,t,r,n){e.push(r?n.replace(o,"$1"):t||A)})),e}));A.exports=i},Gi0A:function(A,e,t){var r=t("QqLw"),n=t("ExA7");A.exports=function(A){return n(A)&&"[object Map]"==r(A)}},GoyQ:function(A,e){A.exports=function(A){var e=typeof A;return null!=A&&("object"==e||"function"==e)}},H7XF:function(A,e,t){"use strict";e.byteLength=function(A){var e=a(A),t=e[0],r=e[1];return 3*(t+r)/4-r},e.toByteArray=function(A){var e,t,r=a(A),i=r[0],s=r[1],B=new o(function(A,e,t){return 3*(e+t)/4-t}(0,i,s)),c=0,u=s>0?i-4:i;for(t=0;t<u;t+=4)e=n[A.charCodeAt(t)]<<18|n[A.charCodeAt(t+1)]<<12|n[A.charCodeAt(t+2)]<<6|n[A.charCodeAt(t+3)],B[c++]=e>>16&255,B[c++]=e>>8&255,B[c++]=255&e;2===s&&(e=n[A.charCodeAt(t)]<<2|n[A.charCodeAt(t+1)]>>4,B[c++]=255&e);1===s&&(e=n[A.charCodeAt(t)]<<10|n[A.charCodeAt(t+1)]<<4|n[A.charCodeAt(t+2)]>>2,B[c++]=e>>8&255,B[c++]=255&e);return B},e.fromByteArray=function(A){for(var e,t=A.length,n=t%3,o=[],i=16383,s=0,B=t-n;s<B;s+=i)o.push(c(A,s,s+i>B?B:s+i));1===n?(e=A[t-1],o.push(r[e>>2]+r[e<<4&63]+"==")):2===n&&(e=(A[t-2]<<8)+A[t-1],o.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"="));return o.join("")};for(var r=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,B=i.length;s<B;++s)r[s]=i[s],n[i.charCodeAt(s)]=s;function a(A){var e=A.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var t=A.indexOf("=");return-1===t&&(t=e),[t,t===e?0:4-t%4]}function c(A,e,t){for(var n,o,i=[],s=e;s<t;s+=3)n=(A[s]<<16&16711680)+(A[s+1]<<8&65280)+(255&A[s+2]),i.push(r[(o=n)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return i.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},H8j4:function(A,e,t){var r=t("QkVE");A.exports=function(A,e){var t=r(this,A),n=t.size;return t.set(A,e),this.size+=t.size==n?0:1,this}},HDXh:function(A,e,t){"use strict";(function(A){var r=t("H7XF"),n=t("kVK+"),o=t("v3Qx");function i(){return B.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function s(A,e){if(i()<e)throw new RangeError("Invalid typed array length");return B.TYPED_ARRAY_SUPPORT?(A=new Uint8Array(e)).__proto__=B.prototype:(null===A&&(A=new B(e)),A.length=e),A}function B(A,e,t){if(!(B.TYPED_ARRAY_SUPPORT||this instanceof B))return new B(A,e,t);if("number"==typeof A){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return u(this,A)}return a(this,A,e,t)}function a(A,e,t,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(A,e,t,r){if(e.byteLength,t<0||e.byteLength<t)throw new RangeError("'offset' is out of bounds");if(e.byteLength<t+(r||0))throw new RangeError("'length' is out of bounds");e=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r);B.TYPED_ARRAY_SUPPORT?(A=e).__proto__=B.prototype:A=l(A,e);return A}(A,e,t,r):"string"==typeof e?function(A,e,t){"string"==typeof t&&""!==t||(t="utf8");if(!B.isEncoding(t))throw new TypeError('"encoding" must be a valid string encoding');var r=0|f(e,t);A=s(A,r);var n=A.write(e,t);n!==r&&(A=A.slice(0,n));return A}(A,e,t):function(A,e){if(B.isBuffer(e)){var t=0|g(e.length);return 0===(A=s(A,t)).length||e.copy(A,0,0,t),A}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(r=e.length)!=r?s(A,0):l(A,e);if("Buffer"===e.type&&o(e.data))return l(A,e.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(A,e)}function c(A){if("number"!=typeof A)throw new TypeError('"size" argument must be a number');if(A<0)throw new RangeError('"size" argument must not be negative')}function u(A,e){if(c(e),A=s(A,e<0?0:0|g(e)),!B.TYPED_ARRAY_SUPPORT)for(var t=0;t<e;++t)A[t]=0;return A}function l(A,e){var t=e.length<0?0:0|g(e.length);A=s(A,t);for(var r=0;r<t;r+=1)A[r]=255&e[r];return A}function g(A){if(A>=i())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i().toString(16)+" bytes");return 0|A}function f(A,e){if(B.isBuffer(A))return A.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(A)||A instanceof ArrayBuffer))return A.byteLength;"string"!=typeof A&&(A=""+A);var t=A.length;if(0===t)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":case void 0:return P(A).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*t;case"hex":return t>>>1;case"base64":return V(A).length;default:if(r)return P(A).length;e=(""+e).toLowerCase(),r=!0}}function w(A,e,t){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===t||t>this.length)&&(t=this.length),t<=0)return"";if((t>>>=0)<=(e>>>=0))return"";for(A||(A="utf8");;)switch(A){case"hex":return K(this,e,t);case"utf8":case"utf-8":return v(this,e,t);case"ascii":return I(this,e,t);case"latin1":case"binary":return b(this,e,t);case"base64":return H(this,e,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,e,t);default:if(r)throw new TypeError("Unknown encoding: "+A);A=(A+"").toLowerCase(),r=!0}}function Q(A,e,t){var r=A[e];A[e]=A[t],A[t]=r}function h(A,e,t,r,n){if(0===A.length)return-1;if("string"==typeof t?(r=t,t=0):t>2147483647?t=2147483647:t<-2147483648&&(t=-2147483648),t=+t,isNaN(t)&&(t=n?0:A.length-1),t<0&&(t=A.length+t),t>=A.length){if(n)return-1;t=A.length-1}else if(t<0){if(!n)return-1;t=0}if("string"==typeof e&&(e=B.from(e,r)),B.isBuffer(e))return 0===e.length?-1:p(A,e,t,r,n);if("number"==typeof e)return e&=255,B.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(A,e,t):Uint8Array.prototype.lastIndexOf.call(A,e,t):p(A,[e],t,r,n);throw new TypeError("val must be string, number or Buffer")}function p(A,e,t,r,n){var o,i=1,s=A.length,B=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(A.length<2||e.length<2)return-1;i=2,s/=2,B/=2,t/=2}function a(A,e){return 1===i?A[e]:A.readUInt16BE(e*i)}if(n){var c=-1;for(o=t;o<s;o++)if(a(A,o)===a(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===B)return c*i}else-1!==c&&(o-=o-c),c=-1}else for(t+B>s&&(t=s-B),o=t;o>=0;o--){for(var u=!0,l=0;l<B;l++)if(a(A,o+l)!==a(e,l)){u=!1;break}if(u)return o}return-1}function C(A,e,t,r){t=Number(t)||0;var n=A.length-t;r?(r=Number(r))>n&&(r=n):r=n;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var i=0;i<r;++i){var s=parseInt(e.substr(2*i,2),16);if(isNaN(s))return i;A[t+i]=s}return i}function U(A,e,t,r){return N(P(e,A.length-t),A,t,r)}function d(A,e,t,r){return N(function(A){for(var e=[],t=0;t<A.length;++t)e.push(255&A.charCodeAt(t));return e}(e),A,t,r)}function F(A,e,t,r){return d(A,e,t,r)}function y(A,e,t,r){return N(V(e),A,t,r)}function E(A,e,t,r){return N(function(A,e){for(var t,r,n,o=[],i=0;i<A.length&&!((e-=2)<0);++i)r=(t=A.charCodeAt(i))>>8,n=t%256,o.push(n),o.push(r);return o}(e,A.length-t),A,t,r)}function H(A,e,t){return 0===e&&t===A.length?r.fromByteArray(A):r.fromByteArray(A.slice(e,t))}function v(A,e,t){t=Math.min(A.length,t);for(var r=[],n=e;n<t;){var o,i,s,B,a=A[n],c=null,u=a>239?4:a>223?3:a>191?2:1;if(n+u<=t)switch(u){case 1:a<128&&(c=a);break;case 2:128==(192&(o=A[n+1]))&&(B=(31&a)<<6|63&o)>127&&(c=B);break;case 3:o=A[n+1],i=A[n+2],128==(192&o)&&128==(192&i)&&(B=(15&a)<<12|(63&o)<<6|63&i)>2047&&(B<55296||B>57343)&&(c=B);break;case 4:o=A[n+1],i=A[n+2],s=A[n+3],128==(192&o)&&128==(192&i)&&128==(192&s)&&(B=(15&a)<<18|(63&o)<<12|(63&i)<<6|63&s)>65535&&B<1114112&&(c=B)}null===c?(c=65533,u=1):c>65535&&(c-=65536,r.push(c>>>10&1023|55296),c=56320|1023&c),r.push(c),n+=u}return function(A){var e=A.length;if(e<=m)return String.fromCharCode.apply(String,A);var t="",r=0;for(;r<e;)t+=String.fromCharCode.apply(String,A.slice(r,r+=m));return t}(r)}e.Buffer=B,e.SlowBuffer=function(A){+A!=A&&(A=0);return B.alloc(+A)},e.INSPECT_MAX_BYTES=50,B.TYPED_ARRAY_SUPPORT=void 0!==A.TYPED_ARRAY_SUPPORT?A.TYPED_ARRAY_SUPPORT:function(){try{var A=new Uint8Array(1);return A.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===A.foo()&&"function"==typeof A.subarray&&0===A.subarray(1,1).byteLength}catch(A){return!1}}(),e.kMaxLength=i(),B.poolSize=8192,B._augment=function(A){return A.__proto__=B.prototype,A},B.from=function(A,e,t){return a(null,A,e,t)},B.TYPED_ARRAY_SUPPORT&&(B.prototype.__proto__=Uint8Array.prototype,B.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&B[Symbol.species]===B&&Object.defineProperty(B,Symbol.species,{value:null,configurable:!0})),B.alloc=function(A,e,t){return function(A,e,t,r){return c(e),e<=0?s(A,e):void 0!==t?"string"==typeof r?s(A,e).fill(t,r):s(A,e).fill(t):s(A,e)}(null,A,e,t)},B.allocUnsafe=function(A){return u(null,A)},B.allocUnsafeSlow=function(A){return u(null,A)},B.isBuffer=function(A){return!(null==A||!A._isBuffer)},B.compare=function(A,e){if(!B.isBuffer(A)||!B.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(A===e)return 0;for(var t=A.length,r=e.length,n=0,o=Math.min(t,r);n<o;++n)if(A[n]!==e[n]){t=A[n],r=e[n];break}return t<r?-1:r<t?1:0},B.isEncoding=function(A){switch(String(A).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},B.concat=function(A,e){if(!o(A))throw new TypeError('"list" argument must be an Array of Buffers');if(0===A.length)return B.alloc(0);var t;if(void 0===e)for(e=0,t=0;t<A.length;++t)e+=A[t].length;var r=B.allocUnsafe(e),n=0;for(t=0;t<A.length;++t){var i=A[t];if(!B.isBuffer(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(r,n),n+=i.length}return r},B.byteLength=f,B.prototype._isBuffer=!0,B.prototype.swap16=function(){var A=this.length;if(A%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<A;e+=2)Q(this,e,e+1);return this},B.prototype.swap32=function(){var A=this.length;if(A%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<A;e+=4)Q(this,e,e+3),Q(this,e+1,e+2);return this},B.prototype.swap64=function(){var A=this.length;if(A%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<A;e+=8)Q(this,e,e+7),Q(this,e+1,e+6),Q(this,e+2,e+5),Q(this,e+3,e+4);return this},B.prototype.toString=function(){var A=0|this.length;return 0===A?"":0===arguments.length?v(this,0,A):w.apply(this,arguments)},B.prototype.equals=function(A){if(!B.isBuffer(A))throw new TypeError("Argument must be a Buffer");return this===A||0===B.compare(this,A)},B.prototype.inspect=function(){var A="",t=e.INSPECT_MAX_BYTES;return this.length>0&&(A=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(A+=" ... ")),"<Buffer "+A+">"},B.prototype.compare=function(A,e,t,r,n){if(!B.isBuffer(A))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===t&&(t=A?A.length:0),void 0===r&&(r=0),void 0===n&&(n=this.length),e<0||t>A.length||r<0||n>this.length)throw new RangeError("out of range index");if(r>=n&&e>=t)return 0;if(r>=n)return-1;if(e>=t)return 1;if(this===A)return 0;for(var o=(n>>>=0)-(r>>>=0),i=(t>>>=0)-(e>>>=0),s=Math.min(o,i),a=this.slice(r,n),c=A.slice(e,t),u=0;u<s;++u)if(a[u]!==c[u]){o=a[u],i=c[u];break}return o<i?-1:i<o?1:0},B.prototype.includes=function(A,e,t){return-1!==this.indexOf(A,e,t)},B.prototype.indexOf=function(A,e,t){return h(this,A,e,t,!0)},B.prototype.lastIndexOf=function(A,e,t){return h(this,A,e,t,!1)},B.prototype.write=function(A,e,t,r){if(void 0===e)r="utf8",t=this.length,e=0;else if(void 0===t&&"string"==typeof e)r=e,t=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(t)?(t|=0,void 0===r&&(r="utf8")):(r=t,t=void 0)}var n=this.length-e;if((void 0===t||t>n)&&(t=n),A.length>0&&(t<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return C(this,A,e,t);case"utf8":case"utf-8":return U(this,A,e,t);case"ascii":return d(this,A,e,t);case"latin1":case"binary":return F(this,A,e,t);case"base64":return y(this,A,e,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,A,e,t);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},B.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var m=4096;function I(A,e,t){var r="";t=Math.min(A.length,t);for(var n=e;n<t;++n)r+=String.fromCharCode(127&A[n]);return r}function b(A,e,t){var r="";t=Math.min(A.length,t);for(var n=e;n<t;++n)r+=String.fromCharCode(A[n]);return r}function K(A,e,t){var r=A.length;(!e||e<0)&&(e=0),(!t||t<0||t>r)&&(t=r);for(var n="",o=e;o<t;++o)n+=G(A[o]);return n}function x(A,e,t){for(var r=A.slice(e,t),n="",o=0;o<r.length;o+=2)n+=String.fromCharCode(r[o]+256*r[o+1]);return n}function L(A,e,t){if(A%1!=0||A<0)throw new RangeError("offset is not uint");if(A+e>t)throw new RangeError("Trying to access beyond buffer length")}function D(A,e,t,r,n,o){if(!B.isBuffer(A))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<o)throw new RangeError('"value" argument is out of bounds');if(t+r>A.length)throw new RangeError("Index out of range")}function S(A,e,t,r){e<0&&(e=65535+e+1);for(var n=0,o=Math.min(A.length-t,2);n<o;++n)A[t+n]=(e&255<<8*(r?n:1-n))>>>8*(r?n:1-n)}function O(A,e,t,r){e<0&&(e=4294967295+e+1);for(var n=0,o=Math.min(A.length-t,4);n<o;++n)A[t+n]=e>>>8*(r?n:3-n)&255}function T(A,e,t,r,n,o){if(t+r>A.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function M(A,e,t,r,o){return o||T(A,0,t,4),n.write(A,e,t,r,23,4),t+4}function R(A,e,t,r,o){return o||T(A,0,t,8),n.write(A,e,t,r,52,8),t+8}B.prototype.slice=function(A,e){var t,r=this.length;if((A=~~A)<0?(A+=r)<0&&(A=0):A>r&&(A=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<A&&(e=A),B.TYPED_ARRAY_SUPPORT)(t=this.subarray(A,e)).__proto__=B.prototype;else{var n=e-A;t=new B(n,void 0);for(var o=0;o<n;++o)t[o]=this[o+A]}return t},B.prototype.readUIntLE=function(A,e,t){A|=0,e|=0,t||L(A,e,this.length);for(var r=this[A],n=1,o=0;++o<e&&(n*=256);)r+=this[A+o]*n;return r},B.prototype.readUIntBE=function(A,e,t){A|=0,e|=0,t||L(A,e,this.length);for(var r=this[A+--e],n=1;e>0&&(n*=256);)r+=this[A+--e]*n;return r},B.prototype.readUInt8=function(A,e){return e||L(A,1,this.length),this[A]},B.prototype.readUInt16LE=function(A,e){return e||L(A,2,this.length),this[A]|this[A+1]<<8},B.prototype.readUInt16BE=function(A,e){return e||L(A,2,this.length),this[A]<<8|this[A+1]},B.prototype.readUInt32LE=function(A,e){return e||L(A,4,this.length),(this[A]|this[A+1]<<8|this[A+2]<<16)+16777216*this[A+3]},B.prototype.readUInt32BE=function(A,e){return e||L(A,4,this.length),16777216*this[A]+(this[A+1]<<16|this[A+2]<<8|this[A+3])},B.prototype.readIntLE=function(A,e,t){A|=0,e|=0,t||L(A,e,this.length);for(var r=this[A],n=1,o=0;++o<e&&(n*=256);)r+=this[A+o]*n;return r>=(n*=128)&&(r-=Math.pow(2,8*e)),r},B.prototype.readIntBE=function(A,e,t){A|=0,e|=0,t||L(A,e,this.length);for(var r=e,n=1,o=this[A+--r];r>0&&(n*=256);)o+=this[A+--r]*n;return o>=(n*=128)&&(o-=Math.pow(2,8*e)),o},B.prototype.readInt8=function(A,e){return e||L(A,1,this.length),128&this[A]?-1*(255-this[A]+1):this[A]},B.prototype.readInt16LE=function(A,e){e||L(A,2,this.length);var t=this[A]|this[A+1]<<8;return 32768&t?4294901760|t:t},B.prototype.readInt16BE=function(A,e){e||L(A,2,this.length);var t=this[A+1]|this[A]<<8;return 32768&t?4294901760|t:t},B.prototype.readInt32LE=function(A,e){return e||L(A,4,this.length),this[A]|this[A+1]<<8|this[A+2]<<16|this[A+3]<<24},B.prototype.readInt32BE=function(A,e){return e||L(A,4,this.length),this[A]<<24|this[A+1]<<16|this[A+2]<<8|this[A+3]},B.prototype.readFloatLE=function(A,e){return e||L(A,4,this.length),n.read(this,A,!0,23,4)},B.prototype.readFloatBE=function(A,e){return e||L(A,4,this.length),n.read(this,A,!1,23,4)},B.prototype.readDoubleLE=function(A,e){return e||L(A,8,this.length),n.read(this,A,!0,52,8)},B.prototype.readDoubleBE=function(A,e){return e||L(A,8,this.length),n.read(this,A,!1,52,8)},B.prototype.writeUIntLE=function(A,e,t,r){(A=+A,e|=0,t|=0,r)||D(this,A,e,t,Math.pow(2,8*t)-1,0);var n=1,o=0;for(this[e]=255&A;++o<t&&(n*=256);)this[e+o]=A/n&255;return e+t},B.prototype.writeUIntBE=function(A,e,t,r){(A=+A,e|=0,t|=0,r)||D(this,A,e,t,Math.pow(2,8*t)-1,0);var n=t-1,o=1;for(this[e+n]=255&A;--n>=0&&(o*=256);)this[e+n]=A/o&255;return e+t},B.prototype.writeUInt8=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,1,255,0),B.TYPED_ARRAY_SUPPORT||(A=Math.floor(A)),this[e]=255&A,e+1},B.prototype.writeUInt16LE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,2,65535,0),B.TYPED_ARRAY_SUPPORT?(this[e]=255&A,this[e+1]=A>>>8):S(this,A,e,!0),e+2},B.prototype.writeUInt16BE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,2,65535,0),B.TYPED_ARRAY_SUPPORT?(this[e]=A>>>8,this[e+1]=255&A):S(this,A,e,!1),e+2},B.prototype.writeUInt32LE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,4,4294967295,0),B.TYPED_ARRAY_SUPPORT?(this[e+3]=A>>>24,this[e+2]=A>>>16,this[e+1]=A>>>8,this[e]=255&A):O(this,A,e,!0),e+4},B.prototype.writeUInt32BE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,4,4294967295,0),B.TYPED_ARRAY_SUPPORT?(this[e]=A>>>24,this[e+1]=A>>>16,this[e+2]=A>>>8,this[e+3]=255&A):O(this,A,e,!1),e+4},B.prototype.writeIntLE=function(A,e,t,r){if(A=+A,e|=0,!r){var n=Math.pow(2,8*t-1);D(this,A,e,t,n-1,-n)}var o=0,i=1,s=0;for(this[e]=255&A;++o<t&&(i*=256);)A<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(A/i>>0)-s&255;return e+t},B.prototype.writeIntBE=function(A,e,t,r){if(A=+A,e|=0,!r){var n=Math.pow(2,8*t-1);D(this,A,e,t,n-1,-n)}var o=t-1,i=1,s=0;for(this[e+o]=255&A;--o>=0&&(i*=256);)A<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(A/i>>0)-s&255;return e+t},B.prototype.writeInt8=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,1,127,-128),B.TYPED_ARRAY_SUPPORT||(A=Math.floor(A)),A<0&&(A=255+A+1),this[e]=255&A,e+1},B.prototype.writeInt16LE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,2,32767,-32768),B.TYPED_ARRAY_SUPPORT?(this[e]=255&A,this[e+1]=A>>>8):S(this,A,e,!0),e+2},B.prototype.writeInt16BE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,2,32767,-32768),B.TYPED_ARRAY_SUPPORT?(this[e]=A>>>8,this[e+1]=255&A):S(this,A,e,!1),e+2},B.prototype.writeInt32LE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,4,2147483647,-2147483648),B.TYPED_ARRAY_SUPPORT?(this[e]=255&A,this[e+1]=A>>>8,this[e+2]=A>>>16,this[e+3]=A>>>24):O(this,A,e,!0),e+4},B.prototype.writeInt32BE=function(A,e,t){return A=+A,e|=0,t||D(this,A,e,4,2147483647,-2147483648),A<0&&(A=4294967295+A+1),B.TYPED_ARRAY_SUPPORT?(this[e]=A>>>24,this[e+1]=A>>>16,this[e+2]=A>>>8,this[e+3]=255&A):O(this,A,e,!1),e+4},B.prototype.writeFloatLE=function(A,e,t){return M(this,A,e,!0,t)},B.prototype.writeFloatBE=function(A,e,t){return M(this,A,e,!1,t)},B.prototype.writeDoubleLE=function(A,e,t){return R(this,A,e,!0,t)},B.prototype.writeDoubleBE=function(A,e,t){return R(this,A,e,!1,t)},B.prototype.copy=function(A,e,t,r){if(t||(t=0),r||0===r||(r=this.length),e>=A.length&&(e=A.length),e||(e=0),r>0&&r<t&&(r=t),r===t)return 0;if(0===A.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(t<0||t>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),A.length-e<r-t&&(r=A.length-e+t);var n,o=r-t;if(this===A&&t<e&&e<r)for(n=o-1;n>=0;--n)A[n+e]=this[n+t];else if(o<1e3||!B.TYPED_ARRAY_SUPPORT)for(n=0;n<o;++n)A[n+e]=this[n+t];else Uint8Array.prototype.set.call(A,this.subarray(t,t+o),e);return o},B.prototype.fill=function(A,e,t,r){if("string"==typeof A){if("string"==typeof e?(r=e,e=0,t=this.length):"string"==typeof t&&(r=t,t=this.length),1===A.length){var n=A.charCodeAt(0);n<256&&(A=n)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!B.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof A&&(A&=255);if(e<0||this.length<e||this.length<t)throw new RangeError("Out of range index");if(t<=e)return this;var o;if(e>>>=0,t=void 0===t?this.length:t>>>0,A||(A=0),"number"==typeof A)for(o=e;o<t;++o)this[o]=A;else{var i=B.isBuffer(A)?A:P(new B(A,r).toString()),s=i.length;for(o=0;o<t-e;++o)this[o+e]=i[o%s]}return this};var k=/[^+\/0-9A-Za-z-_]/g;function G(A){return A<16?"0"+A.toString(16):A.toString(16)}function P(A,e){var t;e=e||1/0;for(var r=A.length,n=null,o=[],i=0;i<r;++i){if((t=A.charCodeAt(i))>55295&&t<57344){if(!n){if(t>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(i+1===r){(e-=3)>-1&&o.push(239,191,189);continue}n=t;continue}if(t<56320){(e-=3)>-1&&o.push(239,191,189),n=t;continue}t=65536+(n-55296<<10|t-56320)}else n&&(e-=3)>-1&&o.push(239,191,189);if(n=null,t<128){if((e-=1)<0)break;o.push(t)}else if(t<2048){if((e-=2)<0)break;o.push(t>>6|192,63&t|128)}else if(t<65536){if((e-=3)<0)break;o.push(t>>12|224,t>>6&63|128,63&t|128)}else{if(!(t<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(t>>18|240,t>>12&63|128,t>>6&63|128,63&t|128)}}return o}function V(A){return r.toByteArray(function(A){if((A=function(A){return A.trim?A.trim():A.replace(/^\s+|\s+$/g,"")}(A).replace(k,"")).length<2)return"";for(;A.length%4!=0;)A+="=";return A}(A))}function N(A,e,t,r){for(var n=0;n<r&&!(n+t>=e.length||n>=A.length);++n)e[n+t]=A[n];return n}}).call(this,t("yLpj"))},HDyB:function(A,e,t){var r=t("nmnc"),n=t("JHRd"),o=t("ljhN"),i=t("or5M"),s=t("7fqy"),B=t("rEGp"),a=r?r.prototype:void 0,c=a?a.valueOf:void 0;A.exports=function(A,e,t,r,a,u,l){switch(t){case"[object DataView]":if(A.byteLength!=e.byteLength||A.byteOffset!=e.byteOffset)return!1;A=A.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(A.byteLength!=e.byteLength||!u(new n(A),new n(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+A,+e);case"[object Error]":return A.name==e.name&&A.message==e.message;case"[object RegExp]":case"[object String]":return A==e+"";case"[object Map]":var g=s;case"[object Set]":var f=1&r;if(g||(g=B),A.size!=e.size&&!f)return!1;var w=l.get(A);if(w)return w==e;r|=2,l.set(A,e);var Q=i(g(A),g(e),r,a,u,l);return l.delete(A),Q;case"[object Symbol]":if(c)return c.call(A)==c.call(e)}return!1}},HOxn:function(A,e,t){var r=t("Cwc5")(t("Kz5y"),"Promise");A.exports=r},HSsa:function(A,e,t){"use strict";A.exports=function(A,e){return function(){for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];return A.apply(e,t)}}},Hvzi:function(A,e){A.exports=function(A){var e=this.has(A)&&delete this.__data__[A];return this.size-=e?1:0,e}},I01J:function(A,e,t){var r=t("44Ds");A.exports=function(A){var e=r(A,(function(A){return 500===t.size&&t.clear(),A})),t=e.cache;return e}},Ioao:function(A,e,t){var r=t("heNW"),n=Math.max;A.exports=function(A,e,t){return e=n(void 0===e?A.length-1:e,0),function(){for(var o=arguments,i=-1,s=n(o.length-e,0),B=Array(s);++i<s;)B[i]=o[e+i];i=-1;for(var a=Array(e+1);++i<e;)a[i]=o[i];return a[e]=t(B),r(A,this,a)}}},JBE3:function(A,e,t){var r=t("+Qka"),n=t("LsHQ")((function(A,e,t,n){r(A,e,t,n)}));A.exports=n},JHRd:function(A,e,t){var r=t("Kz5y").Uint8Array;A.exports=r},JHgL:function(A,e,t){var r=t("QkVE");A.exports=function(A){return r(this,A).get(A)}},JSQU:function(A,e,t){var r=t("YESw");A.exports=function(A,e){var t=this.__data__;return this.size+=this.has(A)?0:1,t[A]=r&&void 0===e?"__lodash_hash_undefined__":e,this}},JTzB:function(A,e,t){var r=t("NykK"),n=t("ExA7");A.exports=function(A){return n(A)&&"[object Arguments]"==r(A)}},JZM8:function(A,e,t){var r=t("FfPP"),n=t("xs/l")((function(A,e){return null==A?{}:r(A,e)}));A.exports=n},JoaM:function(A,e,t){var r=t("NykK"),n=t("ExA7");A.exports=function(A){return n(A)&&"[object RegExp]"==r(A)}},Juji:function(A,e){A.exports=function(A,e){return null!=A&&e in Object(A)}},KMkd:function(A,e){A.exports=function(){this.__data__=[],this.size=0}},KfNM:function(A,e){var t=Object.prototype.toString;A.exports=function(A){return t.call(A)}},Kz5y:function(A,e,t){var r=t("WFqU"),n="object"==typeof self&&self&&self.Object===Object&&self,o=r||n||Function("return this")();A.exports=o},L8xA:function(A,e){A.exports=function(A){var e=this.__data__,t=e.delete(A);return this.size=e.size,t}},LXxW:function(A,e){A.exports=function(A,e){for(var t=-1,r=null==A?0:A.length,n=0,o=[];++t<r;){var i=A[t];e(i,t,A)&&(o[n++]=i)}return o}},LbSc:function(A,e,t){"use strict";function r(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(A)))return;var t=[],r=!0,n=!1,o=void 0;try{for(var i,s=A[Symbol.iterator]();!(r=(i=s.next()).done)&&(t.push(i.value),!e||t.length!==e);r=!0);}catch(A){n=!0,o=A}finally{try{r||null==s.return||s.return()}finally{if(n)throw o}}return t}(A,e)||function(A,e){if(!A)return;if("string"==typeof A)return n(A,e);var t=Object.prototype.toString.call(A).slice(8,-1);"Object"===t&&A.constructor&&(t=A.constructor.name);if("Map"===t||"Set"===t)return Array.from(A);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return n(A,e)}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=A[t];return r}Object.defineProperty(e,"__esModule",{value:!0}),e.parseCookies=void 0;e.parseCookies=function(A){if(0===A.length)return{};var e={},t=new RegExp("\\s*;\\s*");return A.split(t).forEach((function(A){var t=r(A.split("="),2),n=t[0],o=t[1],i=decodeURIComponent(n),s=decodeURIComponent(o);e[i]=s})),e}},LcsW:function(A,e,t){var r=t("kekF")(Object.getPrototypeOf,Object);A.exports=r},Lmem:function(A,e,t){"use strict";A.exports=function(A){return!(!A||!A.__CANCEL__)}},LsHQ:function(A,e,t){var r=t("EA7m"),n=t("mv/X");A.exports=function(A){return r((function(e,t){var r=-1,o=t.length,i=o>1?t[o-1]:void 0,s=o>2?t[2]:void 0;for(i=A.length>3&&"function"==typeof i?(o--,i):void 0,s&&n(t[0],t[1],s)&&(i=o<3?void 0:i,o=1),e=Object(e);++r<o;){var B=t[r];B&&A(e,B,r,i)}return e}))}},MLWZ:function(A,e,t){"use strict";var r=t("xTJ+");function n(A){return encodeURIComponent(A).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}A.exports=function(A,e,t){if(!e)return A;var o;if(t)o=t(e);else if(r.isURLSearchParams(e))o=e.toString();else{var i=[];r.forEach(e,(function(A,e){null!=A&&(r.isArray(A)?e+="[]":A=[A],r.forEach(A,(function(A){r.isDate(A)?A=A.toISOString():r.isObject(A)&&(A=JSON.stringify(A)),i.push(n(e)+"="+n(A))})))})),o=i.join("&")}if(o){var s=A.indexOf("#");-1!==s&&(A=A.slice(0,s)),A+=(-1===A.indexOf("?")?"?":"&")+o}return A}},MMmD:function(A,e,t){var r=t("lSCD"),n=t("shjB");A.exports=function(A){return null!=A&&n(A.length)&&!r(A)}},MrPd:function(A,e,t){var r=t("hypo"),n=t("ljhN"),o=Object.prototype.hasOwnProperty;A.exports=function(A,e,t){var i=A[e];o.call(A,e)&&n(i,t)&&(void 0!==t||e in A)||r(A,e,t)}},MvSz:function(A,e,t){var r=t("LXxW"),n=t("0ycA"),o=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,s=i?function(A){return null==A?[]:(A=Object(A),r(i(A),(function(e){return o.call(A,e)})))}:n;A.exports=s},NKxu:function(A,e,t){var r=t("lSCD"),n=t("E2jh"),o=t("GoyQ"),i=t("3Fdi"),s=/^\[object .+?Constructor\]$/,B=Function.prototype,a=Object.prototype,c=B.toString,u=a.hasOwnProperty,l=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");A.exports=function(A){return!(!o(A)||n(A))&&(r(A)?l:s).test(i(A))}},Npjl:function(A,e){A.exports=function(A,e){return null==A?void 0:A[e]}},Nsbk:function(A,e){function t(e){return A.exports=t=Object.setPrototypeOf?Object.getPrototypeOf:function(A){return A.__proto__||Object.getPrototypeOf(A)},A.exports.__esModule=!0,A.exports.default=A.exports,t(e)}A.exports=t,A.exports.__esModule=!0,A.exports.default=A.exports},NykK:function(A,e,t){var r=t("nmnc"),n=t("AP2z"),o=t("KfNM"),i=r?r.toStringTag:void 0;A.exports=function(A){return null==A?void 0===A?"[object Undefined]":"[object Null]":i&&i in Object(A)?n(A):o(A)}},O0oS:function(A,e,t){var r=t("Cwc5"),n=function(){try{var A=r(Object,"defineProperty");return A({},"",{}),A}catch(A){}}();A.exports=n},OBhP:function(A,e,t){var r=t("fmRc"),n=t("gFfm"),o=t("MrPd"),i=t("WwFo"),s=t("Dw+G"),B=t("5Tg0"),a=t("Q1l4"),c=t("VOtZ"),u=t("EEGq"),l=t("qZTm"),g=t("G6z8"),f=t("QqLw"),w=t("yHx3"),Q=t("wrZu"),h=t("+iFO"),p=t("Z0cm"),C=t("DSRE"),U=t("zEVN"),d=t("GoyQ"),F=t("1+5i"),y=t("7GkX"),E=t("mTTR"),H="[object Arguments]",v="[object Function]",m="[object Object]",I={};I[H]=I["[object Array]"]=I["[object ArrayBuffer]"]=I["[object DataView]"]=I["[object Boolean]"]=I["[object Date]"]=I["[object Float32Array]"]=I["[object Float64Array]"]=I["[object Int8Array]"]=I["[object Int16Array]"]=I["[object Int32Array]"]=I["[object Map]"]=I["[object Number]"]=I[m]=I["[object RegExp]"]=I["[object Set]"]=I["[object String]"]=I["[object Symbol]"]=I["[object Uint8Array]"]=I["[object Uint8ClampedArray]"]=I["[object Uint16Array]"]=I["[object Uint32Array]"]=!0,I["[object Error]"]=I[v]=I["[object WeakMap]"]=!1,A.exports=function A(e,t,b,K,x,L){var D,S=1&t,O=2&t,T=4&t;if(b&&(D=x?b(e,K,x,L):b(e)),void 0!==D)return D;if(!d(e))return e;var M=p(e);if(M){if(D=w(e),!S)return a(e,D)}else{var R=f(e),k=R==v||"[object GeneratorFunction]"==R;if(C(e))return B(e,S);if(R==m||R==H||k&&!x){if(D=O||k?{}:h(e),!S)return O?u(e,s(D,e)):c(e,i(D,e))}else{if(!I[R])return x?e:{};D=Q(e,R,S)}}L||(L=new r);var G=L.get(e);if(G)return G;L.set(e,D),F(e)?e.forEach((function(r){D.add(A(r,t,b,r,e,L))})):U(e)&&e.forEach((function(r,n){D.set(n,A(r,t,b,n,e,L))}));var P=M?void 0:(T?O?g:l:O?E:y)(e);return n(P||e,(function(r,n){P&&(r=e[n=r]),o(D,n,A(r,t,b,n,e,L))})),D}},OTTw:function(A,e,t){"use strict";var r=t("xTJ+");A.exports=r.isStandardBrowserEnv()?function(){var A,e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");function n(A){var r=A;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return A=n(window.location.href),function(e){var t=r.isString(e)?n(e):e;return t.protocol===A.protocol&&t.host===A.host}}():function(){return!0}},"Of+w":function(A,e,t){var r=t("Cwc5")(t("Kz5y"),"WeakMap");A.exports=r},PJYZ:function(A,e){A.exports=function(A){if(void 0===A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A},A.exports.__esModule=!0,A.exports.default=A.exports},Q1l4:function(A,e){A.exports=function(A,e){var t=-1,r=A.length;for(e||(e=Array(r));++t<r;)e[t]=A[t];return e}},QcOe:function(A,e,t){var r=t("GoyQ"),n=t("6sVZ"),o=t("7Ix3"),i=Object.prototype.hasOwnProperty;A.exports=function(A){if(!r(A))return o(A);var e=n(A),t=[];for(var s in A)("constructor"!=s||!e&&i.call(A,s))&&t.push(s);return t}},QkVE:function(A,e,t){var r=t("EpBk");A.exports=function(A,e){var t=A.__data__;return r(e)?t["string"==typeof e?"string":"hash"]:t.map}},QkVN:function(A,e,t){var r=t("+Qka"),n=t("LsHQ")((function(A,e,t){r(A,e,t)}));A.exports=n},QoRX:function(A,e){A.exports=function(A,e){for(var t=-1,r=null==A?0:A.length;++t<r;)if(e(A[t],t,A))return!0;return!1}},QqLw:function(A,e,t){var r=t("tadb"),n=t("ebwN"),o=t("HOxn"),i=t("yGk4"),s=t("Of+w"),B=t("NykK"),a=t("3Fdi"),c="[object Map]",u="[object Promise]",l="[object Set]",g="[object WeakMap]",f="[object DataView]",w=a(r),Q=a(n),h=a(o),p=a(i),C=a(s),U=B;(r&&U(new r(new ArrayBuffer(1)))!=f||n&&U(new n)!=c||o&&U(o.resolve())!=u||i&&U(new i)!=l||s&&U(new s)!=g)&&(U=function(A){var e=B(A),t="[object Object]"==e?A.constructor:void 0,r=t?a(t):"";if(r)switch(r){case w:return f;case Q:return c;case h:return u;case p:return l;case C:return g}return e}),A.exports=U},RYHr:function(A,e){A.exports=null},"Rn+g":function(A,e,t){"use strict";var r=t("eRe6");A.exports=function(A,e,t){var n=t.config.validateStatus;t.status&&n&&!n(t.status)?e(new r("Request failed with status code "+t.status,[r.ERR_BAD_REQUEST,r.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t)):A(t)}},SfRM:function(A,e,t){var r=t("YESw");A.exports=function(){this.__data__=r?r(null):{},this.size=0}},SksO:function(A,e){function t(e,r){return A.exports=t=Object.setPrototypeOf||function(A,e){return A.__proto__=e,A},A.exports.__esModule=!0,A.exports.default=A.exports,t(e,r)}A.exports=t,A.exports.__esModule=!0,A.exports.default=A.exports},SntB:function(A,e,t){"use strict";var r=t("xTJ+");A.exports=function(A,e){e=e||{};var t={};function n(A,e){return r.isPlainObject(A)&&r.isPlainObject(e)?r.merge(A,e):r.isPlainObject(e)?r.merge({},e):r.isArray(e)?e.slice():e}function o(t){return r.isUndefined(e[t])?r.isUndefined(A[t])?void 0:n(void 0,A[t]):n(A[t],e[t])}function i(A){if(!r.isUndefined(e[A]))return n(void 0,e[A])}function s(t){return r.isUndefined(e[t])?r.isUndefined(A[t])?void 0:n(void 0,A[t]):n(void 0,e[t])}function B(t){return t in e?n(A[t],e[t]):t in A?n(void 0,A[t]):void 0}var a={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:B};return r.forEach(Object.keys(A).concat(Object.keys(e)),(function(A){var e=a[A]||o,n=e(A);r.isUndefined(n)&&e!==B||(t[A]=n)})),t}},T1AV:function(A,e,t){var r=t("t2Dn"),n=t("5Tg0"),o=t("yP5f"),i=t("Q1l4"),s=t("+iFO"),B=t("03A+"),a=t("Z0cm"),c=t("3L66"),u=t("DSRE"),l=t("lSCD"),g=t("GoyQ"),f=t("YO3V"),w=t("c6wG"),Q=t("itsj"),h=t("jeLo");A.exports=function(A,e,t,p,C,U,d){var F=Q(A,t),y=Q(e,t),E=d.get(y);if(E)r(A,t,E);else{var H=U?U(F,y,t+"",A,e,d):void 0,v=void 0===H;if(v){var m=a(y),I=!m&&u(y),b=!m&&!I&&w(y);H=y,m||I||b?a(F)?H=F:c(F)?H=i(F):I?(v=!1,H=n(y,!0)):b?(v=!1,H=o(y,!0)):H=[]:f(y)||B(y)?(H=F,B(F)?H=h(F):g(F)&&!l(F)||(H=s(y))):v=!1}v&&(d.set(y,H),C(H,y,p,U,d),d.delete(y)),r(A,t,H)}}},TD3H:function(A,e,t){"use strict";(function(e){var r=t("xTJ+"),n=t("yK9s"),o=t("eRe6"),i=t("yvr/"),s=t("5GeT"),B={"Content-Type":"application/x-www-form-urlencoded"};function a(A,e){!r.isUndefined(A)&&r.isUndefined(A["Content-Type"])&&(A["Content-Type"]=e)}var c,u={transitional:i,adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==e&&"[object process]"===Object.prototype.toString.call(e))&&(c=t("tQ2B")),c),transformRequest:[function(A,e){if(n(e,"Accept"),n(e,"Content-Type"),r.isFormData(A)||r.isArrayBuffer(A)||r.isBuffer(A)||r.isStream(A)||r.isFile(A)||r.isBlob(A))return A;if(r.isArrayBufferView(A))return A.buffer;if(r.isURLSearchParams(A))return a(e,"application/x-www-form-urlencoded;charset=utf-8"),A.toString();var t,o=r.isObject(A),i=e&&e["Content-Type"];if((t=r.isFileList(A))||o&&"multipart/form-data"===i){var B=this.env&&this.env.FormData;return s(t?{"files[]":A}:A,B&&new B)}return o||"application/json"===i?(a(e,"application/json"),function(A,e,t){if(r.isString(A))try{return(e||JSON.parse)(A),r.trim(A)}catch(A){if("SyntaxError"!==A.name)throw A}return(t||JSON.stringify)(A)}(A)):A}],transformResponse:[function(A){var e=this.transitional||u.transitional,t=e&&e.silentJSONParsing,n=e&&e.forcedJSONParsing,i=!t&&"json"===this.responseType;if(i||n&&r.isString(A)&&A.length)try{return JSON.parse(A)}catch(A){if(i){if("SyntaxError"===A.name)throw o.from(A,o.ERR_BAD_RESPONSE,this,null,this.response);throw A}}return A}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:t("RYHr")},validateStatus:function(A){return A>=200&&A<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(A){u.headers[A]={}})),r.forEach(["post","put","patch"],(function(A){u.headers[A]=r.merge(B)})),A.exports=u}).call(this,t("8oxB"))},TYy9:function(A,e,t){var r=t("XGnz");A.exports=function(A){return(null==A?0:A.length)?r(A,1):[]}},UB5X:function(A,e,t){var r=t("NykK"),n=t("ExA7");A.exports=function(A){return"number"==typeof A||n(A)&&"[object Number]"==r(A)}},"UNi/":function(A,e){A.exports=function(A,e){for(var t=-1,r=Array(A);++t<A;)r[t]=e(t);return r}},UnBK:function(A,e,t){"use strict";var r=t("xTJ+"),n=t("xAGQ"),o=t("Lmem"),i=t("TD3H"),s=t("+2B0");function B(A){if(A.cancelToken&&A.cancelToken.throwIfRequested(),A.signal&&A.signal.aborted)throw new s}A.exports=function(A){return B(A),A.headers=A.headers||{},A.data=n.call(A,A.data,A.headers,A.transformRequest),A.headers=r.merge(A.headers.common||{},A.headers[A.method]||{},A.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete A.headers[e]})),(A.adapter||i.adapter)(A).then((function(e){return B(A),e.data=n.call(A,e.data,e.headers,A.transformResponse),e}),(function(e){return o(e)||(B(A),e&&e.response&&(e.response.data=n.call(A,e.response.data,e.response.headers,A.transformResponse))),Promise.reject(e)}))}},V6Ve:function(A,e,t){var r=t("kekF")(Object.keys,Object);A.exports=r},VOtZ:function(A,e,t){var r=t("juv8"),n=t("MvSz");A.exports=function(A,e){return r(A,n(A),e)}},VaNO:function(A,e){A.exports=function(A){return this.__data__.has(A)}},W5m1:function(A,e,t){"use strict";t.r(e);var r=t("FYKA"),n=t("wOnQ"),o=t.n(n);function i(A,e){var t,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i="bearerAuth",s=[],B=[],a=[],c=L();return{loginByUsername:l,verifyUserByUsername:f,refreshToken:g,getToken:I,getBearerAuthentication:H,setBearerAuthentication:d,logout:v,forwardLogout:m,isLoggedIn:K,addOnTokenChangedListener:w,addOnLogoutListener:Q,addOnLoginListener:h,getStorageKey:x,notifyOnLoginListener:U,verifyUserToken:u,getStorage:D};function u(A){return f(Shopware.State.get("session").currentUser.username,A).then((function(A){var e=A.access;if(Shopware.Utils.types.isString(e))return e;throw new Error("access Token should be of type String")})).catch((function(A){throw A}))}function l(t,r){return A.post("/oauth/token",{grant_type:"password",client_id:"administration",scopes:"write",username:t,password:r},{baseURL:e.apiPath}).then((function(A){"undefined"!=typeof document&&void 0!==document.cookie&&c.setItem("lastActivity","".concat(Math.round(+new Date/1e3)));var e=d({access:A.data.access_token,refresh:A.data.refresh_token,expiry:A.data.expires_in});return window.localStorage.setItem("redirectFromLogin","true"),e}))}function g(){var t=b();return t&&t.length?A.post("/oauth/token",{grant_type:"refresh_token",client_id:"administration",scopes:"write",refresh_token:t},{baseURL:e.apiPath}).then((function(A){var e=A.data.expires_in;return d({access:A.data.access_token,expiry:e,refresh:A.data.refresh_token}),A.data.access_token})):Promise.reject(new Error("No refresh token found."))}function f(t,r){return A.post("/oauth/token",{grant_type:"password",client_id:"administration",scope:"user-verified",username:t,password:r},{baseURL:e.apiPath}).then((function(A){return{access:A.data.access_token,expiry:A.data.expires_in,refresh:A.data.refresh_token}}))}function w(A){s.push(A)}function Q(A){B.push(A)}function h(A){a.push(A)}function p(A){s.forEach((function(e){e.call(null,A)}))}function C(){B.forEach((function(A){A.call(null)}))}function U(){return window.localStorage.getItem("redirectFromLogin")?(window.localStorage.removeItem("redirectFromLogin"),a.map((function(A){return A.call(null)}))):null}function d(A){var t=A.access,r=A.refresh,o=A.expiry,s={access:t,refresh:r,expiry:o=Math.round(+new Date/1e3)+o};return"undefined"!=typeof document&&void 0!==document.cookie?c.setItem(i,JSON.stringify(s)):n=s,K()&&p(s),e.authToken=s,F(o),s}function F(A){if(t&&clearTimeout(t),E()&&y())v(!0);else{var e=1e3*A-Date.now();t=setTimeout((function(){g()}),e/2)}}function y(){return Number(c.getItem("lastActivity"))<=Math.round(+new Date/1e3)-1500}function E(){return!("development"===Shopware.Context.app.environment)}function H(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if("undefined"!=typeof document&&void 0!==document.cookie)try{n=JSON.parse(c.getItem(i))}catch(A){n=null}return e.authToken=n,!!n&&(A?!!n[A]&&n[A]:n)}function v(){var A=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return"undefined"!=typeof document&&void 0!==document.cookie&&(c.removeItem(i),document.cookie="bearerAuth=deleted; expires=Thu, 18 Dec 2013 12:00:00 UTC;path=".concat(e.basePath+e.pathInfo)),e.authToken=null,n=null,localStorage.removeItem("rememberMe"),m(A,t),!0}function m(A,e){C();var t=Shopware.Application.getApplicationRoot().$router;if(t){var r=Shopware.Utils.createId();sessionStorage.setItem("sw-admin-previous-route_".concat(r),JSON.stringify({fullPath:t.currentRoute.fullPath,name:t.currentRoute.name})),A&&e?o()(document.querySelector("#app"),{scale:.1}).then((function(A){window.localStorage.setItem("inactivityBackground_".concat(r),A.toDataURL("image/jpeg")),window.sessionStorage.setItem("lastKnownUser",Shopware.State.get("session").currentUser.username),t.push({name:"sw.inactivity.login.index",params:{id:r}})})):t.push({name:"sw.login.index"})}}function I(){return H("access")}function b(){return H("refresh")}function K(){var A=!!I();return A&&F(H("expiry")),A}function x(){return i}function L(){var A=e.basePath+e.pathInfo;return new r.CookieStorage({path:A,domain:null,secure:!1,sameSite:"Strict"})}function D(){return c}}var s=t("lwsE"),B=t.n(s),a=t("W8MJ"),c=t.n(a),u=t("7W2i"),l=t.n(u),g=t("a1gu"),f=t.n(g),w=t("Nsbk"),Q=t.n(w),h=t("cDf5"),p=t.n(h),C=t("lSNA"),U=t.n(C),d=t("GoyQ"),F=t.n(d),y=t("YO3V"),E=t.n(y),H=t("E+oP"),v=t.n(H),m=t("wAXd"),I=t.n(m),b=t("Z0cm"),K=t.n(b),x=t("lSCD"),L=t.n(x),D=t("YiAA"),S=t.n(D),O=t("4qC0"),T=t.n(O),M=t("Znm+"),R=t.n(M),k=t("Y+p1"),G=t.n(k),P=t("UB5X"),V=t.n(P),N={isObject:F.a,isPlainObject:E.a,isEmpty:v.a,isRegExp:I.a,isArray:K.a,isFunction:L.a,isDate:S.a,isString:T.a,isBoolean:R.a,isEqual:G.a,isNumber:V.a,isUndefined:function(A){return void 0===A}};var _=t("QkVN"),J=t.n(_),X=t("JBE3"),j=t.n(X),Y=t("BkRI"),W=t.n(Y),Z=t("mwIZ"),z=t.n(Z),q=t("D1y2"),$=t.n(q),AA=t("JZM8"),eA=t.n(AA);J.a,J.a,W.a,z.a,$.a,eA.a,J.a,j.a,W.a,z.a,$.a,eA.a;function tA(A,e){return Object.prototype.hasOwnProperty.call(A,e)}function rA(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(A);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,r)}return t}function nA(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?rA(Object(t),!0).forEach((function(e){U()(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):rA(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function oA(A){var e=function(A){var e;if(N.isString(A))try{e=JSON.parse(A)}catch(A){return!1}else{if(!N.isObject(A)||N.isArray(A))return!1;e=A}return e}(A);if(!e)return null;if(!0===e.parsed||!function(A){return void 0!==A.data||void 0!==A.errors||void 0!==A.links||void 0!==A.meta}(e))return e;var t=function(A){var e={links:null,errors:null,data:null,associations:null,aggregations:null};if(A.errors)return e.errors=A.errors,e;var t=function(A){var e=new Map;if(!A||!A.length)return e;return A.forEach((function(A){var t="".concat(A.type,"-").concat(A.id);e.set(t,A)})),e}(A.included);if(N.isArray(A.data))e.data=A.data.map((function(A){var r=iA(A,t);return tA(r,"associationLinks")&&(e.associations=nA(nA({},e.associations),r.associationLinks),delete r.associationLinks),r}));else if(N.isObject(A.data)){var r=iA(A.data,t);Object.prototype.hasOwnProperty.call(r,"associationLinks")&&(e.associations=nA(nA({},e.associations),r.associationLinks),delete r.associationLinks),e.data=r}else e.data=null;A.meta&&Object.keys(A.meta).length&&(e.meta=sA(A.meta));A.links&&Object.keys(A.links).length&&(e.links=A.links);A.aggregations&&Object.keys(A.aggregations).length&&(e.aggregations=A.aggregations);return e}(e);return t.parsed=!0,t}function iA(A,e){var t={id:A.id,type:A.type,links:A.links||{},meta:A.meta||{}};if(A.attributes&&Object.keys(A.attributes).length>0){var r=sA(A.attributes);t=nA(nA({},t),r)}if(A.relationships){var n=function(A,e){var t={},r={};return Object.keys(A).forEach((function(n){var o=A[n];if(o.links&&Object.keys(o.links).length&&(r[n]=o.links.related),o.data){var i=o.data;N.isArray(i)?t[n]=i.map((function(A){return BA(A,e)})):N.isObject(i)?t[n]=BA(i,e):t[n]=null}})),{mappedRelations:t,associationLinks:r}}(A.relationships,e);t=nA(nA(nA({},t),n.mappedRelations),{associationLinks:n.associationLinks})}return t}function sA(A){var e={};return Object.keys(A).forEach((function(t){var r=A[t],n=t.replace(/-([a-z])/g,(function(A,e){return e.toUpperCase()}));e[n]=r})),e}function BA(A,e){var t="".concat(A.type,"-").concat(A.id);return e.has(t)?iA(e.get(t),e):A}var aA=function(){function A(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/vnd.api+json";B()(this,A),U()(this,"client",{}),U()(this,"loginService",void 0),U()(this,"endpoint",""),U()(this,"type","application/vnd.api+json"),U()(this,"name",""),this.httpClient=e,this.loginService=t,this.apiEndpoint=r,this.contentType=n}return c()(A,[{key:"getApiBasePath",value:function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t="";return null!=e&&e.length&&(t+="".concat(e,"/")),A&&"number"==typeof A||"string"==typeof A&&A.length>0?"".concat(t).concat(this.apiEndpoint,"/").concat(A):"".concat(t).concat(this.apiEndpoint)}},{key:"getBasicHeaders",value:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={Accept:this.contentType,Authorization:"Bearer ".concat(this.loginService.getToken()),"Content-Type":"application/json"};return Object.assign({},e,A)}},{key:"apiEndpoint",get:function(){return this.endpoint},set:function(A){this.endpoint=A}},{key:"httpClient",get:function(){return this.client},set:function(A){this.client=A}},{key:"contentType",get:function(){return this.type},set:function(A){this.type=A}}],[{key:"handleResponse",value:function(e){if(null===e.data||void 0===e.data)return e;var t=e.headers;return"object"===p()(t)&&null!==t&&"application/vnd.api+json"===t["content-type"]?A.parseJsonApiData(e.data):e.data}},{key:"parseJsonApiData",value:function(A){return oA(A)}},{key:"getVersionHeader",value:function(A){return{"sw-version-id":A}}},{key:"makeQueryParams",value:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=Object.keys(A).filter((function(e){return"string"==typeof A[e]})).map((function(e){return"".concat(e,"=").concat(A[e])}));return e.length?"?".concat(e.join("&")):""}}]),A}(),cA=aA;function uA(A){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(A){return!1}}();return function(){var t,r=Q()(A);if(e){var n=Q()(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return f()(this,t)}}var lA=function(A){l()(t,A);var e=uA(t);function t(A,r){var n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"scheduled-task";return B()(this,t),(n=e.call(this,A,r,o)).name="scheduledTaskService",n}return c()(t,[{key:"runTasks",value:function(){var A=this.getBasicHeaders();return this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/run"),null,{headers:A}).then((function(A){return cA.handleResponse(A)}))}},{key:"getMinRunInterval",value:function(){var A=this.getBasicHeaders();return this.httpClient.get("/_action/".concat(this.getApiBasePath(),"/min-run-interval"),{headers:A}).then((function(A){return cA.handleResponse(A)}))}}]),t}(cA),gA=lA;function fA(A){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(A){return!1}}();return function(){var t,r=Q()(A);if(e){var n=Q()(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return f()(this,t)}}var wA=function(A){l()(t,A);var e=fA(t);function t(A,r){var n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"message-queue";return B()(this,t),(n=e.call(this,A,r,o)).name="messageQueueService",n}return c()(t,[{key:"consume",value:function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,t=this.getBasicHeaders();return this.httpClient.post("/_action/".concat(this.getApiBasePath(),"/consume"),{receiver:A},{headers:t,cancelToken:e}).then((function(A){return cA.handleResponse(A)}))}}]),t}(cA),QA=wA,hA=t("vDqi"),pA=t.n(hA);self.onmessage=vA;const{CancelToken:CA}=pA.a;let UA,dA,FA,yA=!1,EA=CA.source(),HA={};function vA({data:{context:A,bearerAuth:e,host:t,transports:r,type:n}}){if("logout"===n)return void(yA=!1);const o=A.apiResourcePath,s=pA.a.create({baseURL:o,timeout:3e5});UA=new i(s,A,e),dA=new gA(s,UA),FA=new QA(s,UA),"consumeReset"===n&&bA(),yA||(yA=!0,r.forEach((A=>{IA(A)})),"consumeReset"!==n&&dA.getMinRunInterval().then((A=>{if(A.minRunInterval>0){mA(1e3*A.minRunInterval)}})))}function mA(A){yA&&(dA.runTasks().catch((A=>{const{response:{status:e}}=A;401===e&&postMessage("expiredToken")})),setTimeout((()=>{mA(A)}),A))}function IA(A,e=setTimeout){yA&&FA.consume(A,EA.token).then((t=>{const r=0===t.handledMessages?2e4:0;HA[A]=e((()=>{IA(A)}),r)})).catch((t=>(pA.a.isCancel(t)||(HA[A]=e((()=>{IA(A)}),1e4)),t)))}function bA(){EA.cancel(),Object.values(HA).forEach((A=>{clearTimeout(A)})),EA=CA.source(),HA={},yA=!1}e.default={onMessage:vA,runTasks:mA,consumeMessages:IA,cancelConsumeMessages:bA}},W8MJ:function(A,e){function t(A,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(A,r.key,r)}}A.exports=function(A,e,r){return e&&t(A.prototype,e),r&&t(A,r),Object.defineProperty(A,"prototype",{writable:!1}),A},A.exports.__esModule=!0,A.exports.default=A.exports},WFqU:function(A,e,t){(function(e){var t="object"==typeof e&&e&&e.Object===Object&&e;A.exports=t}).call(this,t("yLpj"))},WwFo:function(A,e,t){var r=t("juv8"),n=t("7GkX");A.exports=function(A,e){return A&&r(e,n(e),A)}},XGnz:function(A,e,t){var r=t("CH3K"),n=t("BiGR");A.exports=function A(e,t,o,i,s){var B=-1,a=e.length;for(o||(o=n),s||(s=[]);++B<a;){var c=e[B];t>0&&o(c)?t>1?A(c,t-1,o,i,s):r(s,c):i||(s[s.length]=c)}return s}},XM5P:function(A,e){A.exports={version:"0.27.2"}},XYm9:function(A,e,t){var r=t("+K+b");A.exports=function(A,e){var t=e?r(A.buffer):A.buffer;return new A.constructor(t,A.byteOffset,A.byteLength)}},Xi7e:function(A,e,t){var r=t("KMkd"),n=t("adU4"),o=t("tMB7"),i=t("+6XX"),s=t("Z8oC");function B(A){var e=-1,t=null==A?0:A.length;for(this.clear();++e<t;){var r=A[e];this.set(r[0],r[1])}}B.prototype.clear=r,B.prototype.delete=n,B.prototype.get=o,B.prototype.has=i,B.prototype.set=s,A.exports=B},XwJu:function(A,e,t){"use strict";var r=t("xTJ+");A.exports=function(A){return r.isObject(A)&&!0===A.isAxiosError}},"Y+p1":function(A,e,t){var r=t("wF/u");A.exports=function(A,e){return r(A,e)}},YESw:function(A,e,t){var r=t("Cwc5")(Object,"create");A.exports=r},YO3V:function(A,e,t){var r=t("NykK"),n=t("LcsW"),o=t("ExA7"),i=Function.prototype,s=Object.prototype,B=i.toString,a=s.hasOwnProperty,c=B.call(Object);A.exports=function(A){if(!o(A)||"[object Object]"!=r(A))return!1;var e=n(A);if(null===e)return!0;var t=a.call(e,"constructor")&&e.constructor;return"function"==typeof t&&t instanceof t&&B.call(t)==c}},YiAA:function(A,e,t){var r=t("sdKN"),n=t("sEf8"),o=t("mdPL"),i=o&&o.isDate,s=i?n(i):r;A.exports=s},YuTi:function(A,e){A.exports=function(A){return A.webpackPolyfill||(A.deprecate=function(){},A.paths=[],A.children||(A.children=[]),Object.defineProperty(A,"loaded",{enumerable:!0,get:function(){return A.l}}),Object.defineProperty(A,"id",{enumerable:!0,get:function(){return A.i}}),A.webpackPolyfill=1),A}},Z0cm:function(A,e){var t=Array.isArray;A.exports=t},Z8oC:function(A,e,t){var r=t("y1pI");A.exports=function(A,e){var t=this.__data__,n=r(t,A);return n<0?(++this.size,t.push([A,e])):t[n][1]=e,this}},ZWtO:function(A,e,t){var r=t("4uTw"),n=t("9Nap");A.exports=function(A,e){for(var t=0,o=(e=r(e,A)).length;null!=A&&t<o;)A=A[n(e[t++])];return t&&t==o?A:void 0}},"Znm+":function(A,e,t){var r=t("NykK"),n=t("ExA7");A.exports=function(A){return!0===A||!1===A||n(A)&&"[object Boolean]"==r(A)}},a1gu:function(A,e,t){var r=t("cDf5").default,n=t("PJYZ");A.exports=function(A,e){if(e&&("object"===r(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return n(A)},A.exports.__esModule=!0,A.exports.default=A.exports},adU4:function(A,e,t){var r=t("y1pI"),n=Array.prototype.splice;A.exports=function(A){var e=this.__data__,t=r(e,A);return!(t<0)&&(t==e.length-1?e.pop():n.call(e,t,1),--this.size,!0)}},b2z7:function(A,e){var t=/\w*$/;A.exports=function(A){var e=new A.constructor(A.source,t.exec(A));return e.lastIndex=A.lastIndex,e}},b80T:function(A,e,t){var r=t("UNi/"),n=t("03A+"),o=t("Z0cm"),i=t("DSRE"),s=t("wJg7"),B=t("c6wG"),a=Object.prototype.hasOwnProperty;A.exports=function(A,e){var t=o(A),c=!t&&n(A),u=!t&&!c&&i(A),l=!t&&!c&&!u&&B(A),g=t||c||u||l,f=g?r(A.length,String):[],w=f.length;for(var Q in A)!e&&!a.call(A,Q)||g&&("length"==Q||u&&("offset"==Q||"parent"==Q)||l&&("buffer"==Q||"byteLength"==Q||"byteOffset"==Q)||s(Q,w))||f.push(Q);return f}},c6wG:function(A,e,t){var r=t("dD9F"),n=t("sEf8"),o=t("mdPL"),i=o&&o.isTypedArray,s=i?n(i):r;A.exports=s},cDf5:function(A,e){function t(e){return A.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},A.exports.__esModule=!0,A.exports.default=A.exports,t(e)}A.exports=t,A.exports.__esModule=!0,A.exports.default=A.exports},"cq/+":function(A,e,t){var r=t("mc0g")();A.exports=r},cvCv:function(A,e){A.exports=function(A){return function(){return A}}},dD9F:function(A,e,t){var r=t("NykK"),n=t("shjB"),o=t("ExA7"),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,A.exports=function(A){return o(A)&&n(A.length)&&!!i[r(A)]}},dTAl:function(A,e,t){var r=t("GoyQ"),n=Object.create,o=function(){function A(){}return function(e){if(!r(e))return{};if(n)return n(e);A.prototype=e;var t=new A;return A.prototype=void 0,t}}();A.exports=o},dt0z:function(A,e,t){var r=t("zoYe");A.exports=function(A){return null==A?"":r(A)}},e4Nc:function(A,e,t){var r=t("fGT3"),n=t("k+1r"),o=t("JHgL"),i=t("pSRY"),s=t("H8j4");function B(A){var e=-1,t=null==A?0:A.length;for(this.clear();++e<t;){var r=A[e];this.set(r[0],r[1])}}B.prototype.clear=r,B.prototype.delete=n,B.prototype.get=o,B.prototype.has=i,B.prototype.set=s,A.exports=B},e5cp:function(A,e,t){var r=t("fmRc"),n=t("or5M"),o=t("HDyB"),i=t("seXi"),s=t("QqLw"),B=t("Z0cm"),a=t("DSRE"),c=t("c6wG"),u="[object Arguments]",l="[object Array]",g="[object Object]",f=Object.prototype.hasOwnProperty;A.exports=function(A,e,t,w,Q,h){var p=B(A),C=B(e),U=p?l:s(A),d=C?l:s(e),F=(U=U==u?g:U)==g,y=(d=d==u?g:d)==g,E=U==d;if(E&&a(A)){if(!a(e))return!1;p=!0,F=!1}if(E&&!F)return h||(h=new r),p||c(A)?n(A,e,t,w,Q,h):o(A,e,U,t,w,Q,h);if(!(1&t)){var H=F&&f.call(A,"__wrapped__"),v=y&&f.call(e,"__wrapped__");if(H||v){var m=H?A.value():A,I=v?e.value():e;return h||(h=new r),Q(m,I,t,w,h)}}return!!E&&(h||(h=new r),i(A,e,t,w,Q,h))}},eRe6:function(A,e,t){"use strict";var r=t("xTJ+");function n(A,e,t,r,n){Error.call(this),this.message=A,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),r&&(this.request=r),n&&(this.response=n)}r.inherits(n,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var o=n.prototype,i={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach((function(A){i[A]={value:A}})),Object.defineProperties(n,i),Object.defineProperty(o,"isAxiosError",{value:!0}),n.from=function(A,e,t,i,s,B){var a=Object.create(o);return r.toFlatObject(A,a,(function(A){return A!==Error.prototype})),n.call(a,A.message,e,t,i,s),a.name=A.name,B&&Object.assign(a,B),a},A.exports=n},eUgh:function(A,e){A.exports=function(A,e){for(var t=-1,r=null==A?0:A.length,n=Array(r);++t<r;)n[t]=e(A[t],t,A);return n}},ebwN:function(A,e,t){var r=t("Cwc5")(t("Kz5y"),"Map");A.exports=r},ekgI:function(A,e,t){var r=t("YESw"),n=Object.prototype.hasOwnProperty;A.exports=function(A){var e=this.__data__;return r?void 0!==e[A]:n.call(e,A)}},eqyj:function(A,e,t){"use strict";var r=t("xTJ+");A.exports=r.isStandardBrowserEnv()?{write:function(A,e,t,n,o,i){var s=[];s.push(A+"="+encodeURIComponent(e)),r.isNumber(t)&&s.push("expires="+new Date(t).toGMTString()),r.isString(n)&&s.push("path="+n),r.isString(o)&&s.push("domain="+o),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read:function(A){var e=document.cookie.match(new RegExp("(^|;\\s*)("+A+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(A){this.write(A,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},fGT3:function(A,e,t){var r=t("4kuk"),n=t("Xi7e"),o=t("ebwN");A.exports=function(){this.size=0,this.__data__={hash:new r,map:new(o||n),string:new r}}},"fR/l":function(A,e,t){var r=t("CH3K"),n=t("Z0cm");A.exports=function(A,e,t){var o=e(A);return n(A)?o:r(o,t(A))}},fmRc:function(A,e,t){var r=t("Xi7e"),n=t("77Zs"),o=t("L8xA"),i=t("gCq4"),s=t("VaNO"),B=t("0Cz8");function a(A){var e=this.__data__=new r(A);this.size=e.size}a.prototype.clear=n,a.prototype.delete=o,a.prototype.get=i,a.prototype.has=s,a.prototype.set=B,A.exports=a},ftKO:function(A,e){A.exports=function(A){return this.__data__.set(A,"__lodash_hash_undefined__"),this}},g7np:function(A,e,t){"use strict";var r=t("2SVd"),n=t("5oMp");A.exports=function(A,e){return A&&!r(e)?n(A,e):e}},gCq4:function(A,e){A.exports=function(A){return this.__data__.get(A)}},gFfm:function(A,e){A.exports=function(A,e){for(var t=-1,r=null==A?0:A.length;++t<r&&!1!==e(A[t],t,A););return A}},hIuj:function(A,e,t){"use strict";var r=t("XM5P").version,n=t("eRe6"),o={};["object","boolean","number","function","string","symbol"].forEach((function(A,e){o[A]=function(t){return typeof t===A||"a"+(e<1?"n ":" ")+A}}));var i={};o.transitional=function(A,e,t){function o(A,e){return"[Axios v"+r+"] Transitional option '"+A+"'"+e+(t?". "+t:"")}return function(t,r,s){if(!1===A)throw new n(o(r," has been removed"+(e?" in "+e:"")),n.ERR_DEPRECATED);return e&&!i[r]&&(i[r]=!0,console.warn(o(r," has been deprecated since v"+e+" and will be removed in the near future"))),!A||A(t,r,s)}},A.exports={assertOptions:function(A,e,t){if("object"!=typeof A)throw new n("options must be an object",n.ERR_BAD_OPTION_VALUE);for(var r=Object.keys(A),o=r.length;o-- >0;){var i=r[o],s=e[i];if(s){var B=A[i],a=void 0===B||s(B,i,A);if(!0!==a)throw new n("option "+i+" must be "+a,n.ERR_BAD_OPTION_VALUE)}else if(!0!==t)throw new n("Unknown option "+i,n.ERR_BAD_OPTION)}},validators:o}},heNW:function(A,e){A.exports=function(A,e,t){switch(t.length){case 0:return A.call(e);case 1:return A.call(e,t[0]);case 2:return A.call(e,t[0],t[1]);case 3:return A.call(e,t[0],t[1],t[2])}return A.apply(e,t)}},hgQt:function(A,e,t){var r=t("Juji"),n=t("4sDh");A.exports=function(A,e){return null!=A&&n(A,e,r)}},hypo:function(A,e,t){var r=t("O0oS");A.exports=function(A,e,t){"__proto__"==e&&r?r(A,e,{configurable:!0,enumerable:!0,value:t,writable:!0}):A[e]=t}},idmN:function(A,e,t){var r=t("ZWtO"),n=t("FZoo"),o=t("4uTw");A.exports=function(A,e,t){for(var i=-1,s=e.length,B={};++i<s;){var a=e[i],c=r(A,a);t(c,a)&&n(B,o(a,A),c)}return B}},itsj:function(A,e){A.exports=function(A,e){if(("constructor"!==e||"function"!=typeof A[e])&&"__proto__"!=e)return A[e]}},jeLo:function(A,e,t){var r=t("juv8"),n=t("mTTR");A.exports=function(A){return r(A,n(A))}},"jfS+":function(A,e,t){"use strict";var r=t("+2B0");function n(A){if("function"!=typeof A)throw new TypeError("executor must be a function.");var e;this.promise=new Promise((function(A){e=A}));var t=this;this.promise.then((function(A){if(t._listeners){var e,r=t._listeners.length;for(e=0;e<r;e++)t._listeners[e](A);t._listeners=null}})),this.promise.then=function(A){var e,r=new Promise((function(A){t.subscribe(A),e=A})).then(A);return r.cancel=function(){t.unsubscribe(e)},r},A((function(A){t.reason||(t.reason=new r(A),e(t.reason))}))}n.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},n.prototype.subscribe=function(A){this.reason?A(this.reason):this._listeners?this._listeners.push(A):this._listeners=[A]},n.prototype.unsubscribe=function(A){if(this._listeners){var e=this._listeners.indexOf(A);-1!==e&&this._listeners.splice(e,1)}},n.source=function(){var A;return{token:new n((function(e){A=e})),cancel:A}},A.exports=n},juv8:function(A,e,t){var r=t("MrPd"),n=t("hypo");A.exports=function(A,e,t,o){var i=!t;t||(t={});for(var s=-1,B=e.length;++s<B;){var a=e[s],c=o?o(t[a],A[a],a,t,A):void 0;void 0===c&&(c=A[a]),i?n(t,a,c):r(t,a,c)}return t}},"k+1r":function(A,e,t){var r=t("QkVE");A.exports=function(A){var e=r(this,A).delete(A);return this.size-=e?1:0,e}},"kVK+":function(A,e){e.read=function(A,e,t,r,n){var o,i,s=8*n-r-1,B=(1<<s)-1,a=B>>1,c=-7,u=t?n-1:0,l=t?-1:1,g=A[e+u];for(u+=l,o=g&(1<<-c)-1,g>>=-c,c+=s;c>0;o=256*o+A[e+u],u+=l,c-=8);for(i=o&(1<<-c)-1,o>>=-c,c+=r;c>0;i=256*i+A[e+u],u+=l,c-=8);if(0===o)o=1-a;else{if(o===B)return i?NaN:1/0*(g?-1:1);i+=Math.pow(2,r),o-=a}return(g?-1:1)*i*Math.pow(2,o-r)},e.write=function(A,e,t,r,n,o){var i,s,B,a=8*o-n-1,c=(1<<a)-1,u=c>>1,l=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,g=r?0:o-1,f=r?1:-1,w=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,i=c):(i=Math.floor(Math.log(e)/Math.LN2),e*(B=Math.pow(2,-i))<1&&(i--,B*=2),(e+=i+u>=1?l/B:l*Math.pow(2,1-u))*B>=2&&(i++,B/=2),i+u>=c?(s=0,i=c):i+u>=1?(s=(e*B-1)*Math.pow(2,n),i+=u):(s=e*Math.pow(2,u-1)*Math.pow(2,n),i=0));n>=8;A[t+g]=255&s,g+=f,s/=256,n-=8);for(i=i<<n|s,a+=n;a>0;A[t+g]=255&i,g+=f,i/=256,a-=8);A[t+g-f]|=128*w}},kekF:function(A,e){A.exports=function(A,e){return function(t){return A(e(t))}}},lSCD:function(A,e,t){var r=t("NykK"),n=t("GoyQ");A.exports=function(A){if(!n(A))return!1;var e=r(A);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},lSNA:function(A,e){A.exports=function(A,e,t){return e in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A},A.exports.__esModule=!0,A.exports.default=A.exports},ljhN:function(A,e){A.exports=function(A,e){return A===e||A!=A&&e!=e}},lwsE:function(A,e){A.exports=function(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")},A.exports.__esModule=!0,A.exports.default=A.exports},mTTR:function(A,e,t){var r=t("b80T"),n=t("QcOe"),o=t("MMmD");A.exports=function(A){return o(A)?r(A,!0):n(A)}},mc0g:function(A,e){A.exports=function(A){return function(e,t,r){for(var n=-1,o=Object(e),i=r(e),s=i.length;s--;){var B=i[A?s:++n];if(!1===t(o[B],B,o))break}return e}}},mdPL:function(A,e,t){(function(A){var r=t("WFqU"),n=e&&!e.nodeType&&e,o=n&&"object"==typeof A&&A&&!A.nodeType&&A,i=o&&o.exports===n&&r.process,s=function(){try{var A=o&&o.require&&o.require("util").types;return A||i&&i.binding&&i.binding("util")}catch(A){}}();A.exports=s}).call(this,t("YuTi")(A))},"mv/X":function(A,e,t){var r=t("ljhN"),n=t("MMmD"),o=t("wJg7"),i=t("GoyQ");A.exports=function(A,e,t){if(!i(t))return!1;var s=typeof e;return!!("number"==s?n(t)&&o(e,t.length):"string"==s&&e in t)&&r(t[e],A)}},mwIZ:function(A,e,t){var r=t("ZWtO");A.exports=function(A,e,t){var n=null==A?void 0:r(A,e);return void 0===n?t:n}},nmnc:function(A,e,t){var r=t("Kz5y").Symbol;A.exports=r},"oCl/":function(A,e,t){var r=t("CH3K"),n=t("LcsW"),o=t("MvSz"),i=t("0ycA"),s=Object.getOwnPropertySymbols?function(A){for(var e=[];A;)r(e,o(A)),A=n(A);return e}:i;A.exports=s},or5M:function(A,e,t){var r=t("1hJj"),n=t("QoRX"),o=t("xYSL");A.exports=function(A,e,t,i,s,B){var a=1&t,c=A.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var l=B.get(A),g=B.get(e);if(l&&g)return l==e&&g==A;var f=-1,w=!0,Q=2&t?new r:void 0;for(B.set(A,e),B.set(e,A);++f<c;){var h=A[f],p=e[f];if(i)var C=a?i(p,h,f,e,A,B):i(h,p,f,A,e,B);if(void 0!==C){if(C)continue;w=!1;break}if(Q){if(!n(e,(function(A,e){if(!o(Q,e)&&(h===A||s(h,A,t,i,B)))return Q.push(e)}))){w=!1;break}}else if(h!==p&&!s(h,p,t,i,B)){w=!1;break}}return B.delete(A),B.delete(e),w}},"otv/":function(A,e,t){var r=t("nmnc"),n=r?r.prototype:void 0,o=n?n.valueOf:void 0;A.exports=function(A){return o?Object(o.call(A)):{}}},pFRH:function(A,e,t){var r=t("cvCv"),n=t("O0oS"),o=t("zZ0H"),i=n?function(A,e){return n(A,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:o;A.exports=i},pSRY:function(A,e,t){var r=t("QkVE");A.exports=function(A){return r(this,A).has(A)}},qZTm:function(A,e,t){var r=t("fR/l"),n=t("MvSz"),o=t("7GkX");A.exports=function(A){return r(A,o,n)}},rEGp:function(A,e){A.exports=function(A){var e=-1,t=Array(A.size);return A.forEach((function(A){t[++e]=A})),t}},sEf8:function(A,e){A.exports=function(A){return function(e){return A(e)}}},sdKN:function(A,e,t){var r=t("NykK"),n=t("ExA7");A.exports=function(A){return n(A)&&"[object Date]"==r(A)}},seXi:function(A,e,t){var r=t("qZTm"),n=Object.prototype.hasOwnProperty;A.exports=function(A,e,t,o,i,s){var B=1&t,a=r(A),c=a.length;if(c!=r(e).length&&!B)return!1;for(var u=c;u--;){var l=a[u];if(!(B?l in e:n.call(e,l)))return!1}var g=s.get(A),f=s.get(e);if(g&&f)return g==e&&f==A;var w=!0;s.set(A,e),s.set(e,A);for(var Q=B;++u<c;){var h=A[l=a[u]],p=e[l];if(o)var C=B?o(p,h,l,e,A,s):o(h,p,l,A,e,s);if(!(void 0===C?h===p||i(h,p,t,o,s):C)){w=!1;break}Q||(Q="constructor"==l)}if(w&&!Q){var U=A.constructor,d=e.constructor;U==d||!("constructor"in A)||!("constructor"in e)||"function"==typeof U&&U instanceof U&&"function"==typeof d&&d instanceof d||(w=!1)}return s.delete(A),s.delete(e),w}},shjB:function(A,e){A.exports=function(A){return"number"==typeof A&&A>-1&&A%1==0&&A<=9007199254740991}},t2Dn:function(A,e,t){var r=t("hypo"),n=t("ljhN");A.exports=function(A,e,t){(void 0!==t&&!n(A[e],t)||void 0===t&&!(e in A))&&r(A,e,t)}},tMB7:function(A,e,t){var r=t("y1pI");A.exports=function(A){var e=this.__data__,t=r(e,A);return t<0?void 0:e[t][1]}},tQ2B:function(A,e,t){"use strict";var r=t("xTJ+"),n=t("Rn+g"),o=t("eqyj"),i=t("MLWZ"),s=t("g7np"),B=t("w0Vi"),a=t("OTTw"),c=t("yvr/"),u=t("eRe6"),l=t("+2B0"),g=t("toob");A.exports=function(A){return new Promise((function(e,t){var f,w=A.data,Q=A.headers,h=A.responseType;function p(){A.cancelToken&&A.cancelToken.unsubscribe(f),A.signal&&A.signal.removeEventListener("abort",f)}r.isFormData(w)&&r.isStandardBrowserEnv()&&delete Q["Content-Type"];var C=new XMLHttpRequest;if(A.auth){var U=A.auth.username||"",d=A.auth.password?unescape(encodeURIComponent(A.auth.password)):"";Q.Authorization="Basic "+btoa(U+":"+d)}var F=s(A.baseURL,A.url);function y(){if(C){var r="getAllResponseHeaders"in C?B(C.getAllResponseHeaders()):null,o={data:h&&"text"!==h&&"json"!==h?C.response:C.responseText,status:C.status,statusText:C.statusText,headers:r,config:A,request:C};n((function(A){e(A),p()}),(function(A){t(A),p()}),o),C=null}}if(C.open(A.method.toUpperCase(),i(F,A.params,A.paramsSerializer),!0),C.timeout=A.timeout,"onloadend"in C?C.onloadend=y:C.onreadystatechange=function(){C&&4===C.readyState&&(0!==C.status||C.responseURL&&0===C.responseURL.indexOf("file:"))&&setTimeout(y)},C.onabort=function(){C&&(t(new u("Request aborted",u.ECONNABORTED,A,C)),C=null)},C.onerror=function(){t(new u("Network Error",u.ERR_NETWORK,A,C,C)),C=null},C.ontimeout=function(){var e=A.timeout?"timeout of "+A.timeout+"ms exceeded":"timeout exceeded",r=A.transitional||c;A.timeoutErrorMessage&&(e=A.timeoutErrorMessage),t(new u(e,r.clarifyTimeoutError?u.ETIMEDOUT:u.ECONNABORTED,A,C)),C=null},r.isStandardBrowserEnv()){var E=(A.withCredentials||a(F))&&A.xsrfCookieName?o.read(A.xsrfCookieName):void 0;E&&(Q[A.xsrfHeaderName]=E)}"setRequestHeader"in C&&r.forEach(Q,(function(A,e){void 0===w&&"content-type"===e.toLowerCase()?delete Q[e]:C.setRequestHeader(e,A)})),r.isUndefined(A.withCredentials)||(C.withCredentials=!!A.withCredentials),h&&"json"!==h&&(C.responseType=A.responseType),"function"==typeof A.onDownloadProgress&&C.addEventListener("progress",A.onDownloadProgress),"function"==typeof A.onUploadProgress&&C.upload&&C.upload.addEventListener("progress",A.onUploadProgress),(A.cancelToken||A.signal)&&(f=function(A){C&&(t(!A||A&&A.type?new l:A),C.abort(),C=null)},A.cancelToken&&A.cancelToken.subscribe(f),A.signal&&(A.signal.aborted?f():A.signal.addEventListener("abort",f))),w||(w=null);var H=g(F);H&&-1===["http","https","file"].indexOf(H)?t(new u("Unsupported protocol "+H+":",u.ERR_BAD_REQUEST,A)):C.send(w)}))}},tadb:function(A,e,t){var r=t("Cwc5")(t("Kz5y"),"DataView");A.exports=r},toob:function(A,e,t){"use strict";A.exports=function(A){var e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(A);return e&&e[1]||""}},u8Dt:function(A,e,t){var r=t("YESw"),n=Object.prototype.hasOwnProperty;A.exports=function(A){var e=this.__data__;if(r){var t=e[A];return"__lodash_hash_undefined__"===t?void 0:t}return n.call(e,A)?e[A]:void 0}},v3Qx:function(A,e){var t={}.toString;A.exports=Array.isArray||function(A){return"[object Array]"==t.call(A)}},vDqi:function(A,e,t){A.exports=t("zuR4")},"w/wX":function(A,e,t){var r=t("QqLw"),n=t("ExA7");A.exports=function(A){return n(A)&&"[object Set]"==r(A)}},w0Vi:function(A,e,t){"use strict";var r=t("xTJ+"),n=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];A.exports=function(A){var e,t,o,i={};return A?(r.forEach(A.split("\n"),(function(A){if(o=A.indexOf(":"),e=r.trim(A.substr(0,o)).toLowerCase(),t=r.trim(A.substr(o+1)),e){if(i[e]&&n.indexOf(e)>=0)return;i[e]="set-cookie"===e?(i[e]?i[e]:[]).concat([t]):i[e]?i[e]+", "+t:t}})),i):i}},wAXd:function(A,e,t){var r=t("JoaM"),n=t("sEf8"),o=t("mdPL"),i=o&&o.isRegExp,s=i?n(i):r;A.exports=s},"wF/u":function(A,e,t){var r=t("e5cp"),n=t("ExA7");A.exports=function A(e,t,o,i,s){return e===t||(null==e||null==t||!n(e)&&!n(t)?e!=e&&t!=t:r(e,t,o,i,A,s))}},wJg7:function(A,e){var t=/^(?:0|[1-9]\d*)$/;A.exports=function(A,e){var r=typeof A;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&t.test(A))&&A>-1&&A%1==0&&A<e}},wOnQ:function(A,e,t){A.exports=function(){"use strict";var A=function(e,t){return A=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])},A(e,t)};function e(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}A(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var t=function(){return t=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A},t.apply(this,arguments)};function r(A,e,t,r){function n(A){return A instanceof t?A:new t((function(e){e(A)}))}return new(t||(t=Promise))((function(t,o){function i(A){try{B(r.next(A))}catch(A){o(A)}}function s(A){try{B(r.throw(A))}catch(A){o(A)}}function B(A){A.done?t(A.value):n(A.value).then(i,s)}B((r=r.apply(A,e||[])).next())}))}function n(A,e){var t,r,n,o,i={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(A){return function(e){return B([A,e])}}function B(o){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(n=2&o[0]?r.return:o[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,o[1])).done)return n;switch(r=0,n&&(o=[2&o[0],n.value]),o[0]){case 0:case 1:n=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!((n=(n=i.trys).length>0&&n[n.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!n||o[1]>n[0]&&o[1]<n[3])){i.label=o[1];break}if(6===o[0]&&i.label<n[1]){i.label=n[1],n=o;break}if(n&&i.label<n[2]){i.label=n[2],i.ops.push(o);break}n[2]&&i.ops.pop(),i.trys.pop();continue}o=e.call(A,i)}catch(A){o=[6,A],r=0}finally{t=n=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}}function o(A,e,t){if(t||2===arguments.length)for(var r,n=0,o=e.length;n<o;n++)!r&&n in e||(r||(r=Array.prototype.slice.call(e,0,n)),r[n]=e[n]);return A.concat(r||e)}for(var i=function(){function A(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e,t){return new A(t.left+e.windowBounds.left,t.top+e.windowBounds.top,t.width,t.height)},A.fromDOMRectList=function(e,t){var r=Array.from(t).find((function(A){return 0!==A.width}));return r?new A(r.left+e.windowBounds.left,r.top+e.windowBounds.top,r.width,r.height):A.EMPTY},A.EMPTY=new A(0,0,0,0),A}(),s=function(A,e){return i.fromClientRect(A,e.getBoundingClientRect())},B=function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new i(0,0,r,n)},a=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var o=A.charCodeAt(t++);56320==(64512&o)?e.push(((1023&n)<<10)+(1023&o)+65536):(e.push(n),t--)}else e.push(n)}return e},c=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,o="";++n<t;){var i=A[n];i<=65535?r.push(i):(i-=65536,r.push(55296+(i>>10),i%1024+56320)),(n+1===t||r.length>16384)&&(o+=String.fromCharCode.apply(String,r),r.length=0)}return o},u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l="undefined"==typeof Uint8Array?[]:new Uint8Array(256),g=0;g<u.length;g++)l[u.charCodeAt(g)]=g;for(var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",w="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Q=0;Q<f.length;Q++)w[f.charCodeAt(Q)]=Q;for(var h=function(A){var e,t,r,n,o,i=.75*A.length,s=A.length,B=0;"="===A[A.length-1]&&(i--,"="===A[A.length-2]&&i--);var a="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(i):new Array(i),c=Array.isArray(a)?a:new Uint8Array(a);for(e=0;e<s;e+=4)t=w[A.charCodeAt(e)],r=w[A.charCodeAt(e+1)],n=w[A.charCodeAt(e+2)],o=w[A.charCodeAt(e+3)],c[B++]=t<<2|r>>4,c[B++]=(15&r)<<4|n>>2,c[B++]=(3&n)<<6|63&o;return a},p=function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t},C=function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t},U=5,d=11,F=2,y=65536>>U,E=(1<<U)-1,H=y+(1024>>U)+32,v=65536>>d,m=(1<<d-U)-1,I=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},b=function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))},K=function(A,e){var t=h(A),r=Array.isArray(t)?C(t):new Uint32Array(t),n=Array.isArray(t)?p(t):new Uint16Array(t),o=24,i=I(n,o/2,r[4]/2),s=2===r[5]?I(n,(o+r[4])/2):b(r,Math.ceil((o+r[4])/4));return new x(r[0],r[1],r[2],r[3],i,s)},x=function(){function A(A,e,t,r,n,o){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=o}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>U])<<F)+(A&E),this.data[e];if(A<=65535)return e=((e=this.index[y+(A-55296>>U)])<<F)+(A&E),this.data[e];if(A<this.highStart)return e=H-v+(A>>d),e=this.index[e],e+=A>>U&m,e=((e=this.index[e])<<F)+(A&E),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),L="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",D="undefined"==typeof Uint8Array?[]:new Uint8Array(256),S=0;S<L.length;S++)D[L.charCodeAt(S)]=S;var O=50,T=1,M=2,R=3,k=4,G=5,P=7,V=8,N=9,_=10,J=11,X=12,j=13,Y=14,W=15,Z=16,z=17,q=18,$=19,AA=20,eA=21,tA=22,rA=23,nA=24,oA=25,iA=26,sA=27,BA=28,aA=29,cA=30,uA=31,lA=32,gA=33,fA=34,wA=35,QA=36,hA=37,pA=38,CA=39,UA=40,dA=41,FA=42,yA=43,EA=[9001,65288],HA="!",vA="×",mA="÷",IA=K("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"),bA=[cA,QA],KA=[T,M,R,G],xA=[_,V],LA=[sA,iA],DA=KA.concat(xA),SA=[pA,CA,UA,fA,wA],OA=[W,j],TA=function(A,e){void 0===e&&(e="strict");var t=[],r=[],n=[];return A.forEach((function(A,o){var i=IA.get(A);if(i>O?(n.push(!0),i-=O):n.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A))return r.push(o),t.push(Z);if(i===k||i===J){if(0===o)return r.push(o),t.push(cA);var s=t[o-1];return-1===DA.indexOf(s)?(r.push(r[o-1]),t.push(s)):(r.push(o),t.push(cA))}return r.push(o),i===uA?t.push("strict"===e?eA:hA):i===FA||i===aA?t.push(cA):i===yA?A>=131072&&A<=196605||A>=196608&&A<=262141?t.push(hA):t.push(cA):void t.push(i)})),[r,t,n]},MA=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var o=t;o<=r.length;){if((B=r[++o])===e)return!0;if(B!==_)break}if(n===_)for(o=t;o>0;){var i=r[--o];if(Array.isArray(A)?-1!==A.indexOf(i):A===i)for(var s=t;s<=r.length;){var B;if((B=r[++s])===e)return!0;if(B!==_)break}if(i!==_)break}return!1},RA=function(A,e){for(var t=A;t>=0;){var r=e[t];if(r!==_)return r;t--}return 0},kA=function(A,e,t,r,n){if(0===t[r])return vA;var o=r-1;if(Array.isArray(n)&&!0===n[o])return vA;var i=o-1,s=o+1,B=e[o],a=i>=0?e[i]:0,c=e[s];if(B===M&&c===R)return vA;if(-1!==KA.indexOf(B))return HA;if(-1!==KA.indexOf(c))return vA;if(-1!==xA.indexOf(c))return vA;if(RA(o,e)===V)return mA;if(IA.get(A[o])===J)return vA;if((B===lA||B===gA)&&IA.get(A[s])===J)return vA;if(B===P||c===P)return vA;if(B===N)return vA;if(-1===[_,j,W].indexOf(B)&&c===N)return vA;if(-1!==[z,q,$,nA,BA].indexOf(c))return vA;if(RA(o,e)===tA)return vA;if(MA(rA,tA,o,e))return vA;if(MA([z,q],eA,o,e))return vA;if(MA(X,X,o,e))return vA;if(B===_)return mA;if(B===rA||c===rA)return vA;if(c===Z||B===Z)return mA;if(-1!==[j,W,eA].indexOf(c)||B===Y)return vA;if(a===QA&&-1!==OA.indexOf(B))return vA;if(B===BA&&c===QA)return vA;if(c===AA)return vA;if(-1!==bA.indexOf(c)&&B===oA||-1!==bA.indexOf(B)&&c===oA)return vA;if(B===sA&&-1!==[hA,lA,gA].indexOf(c)||-1!==[hA,lA,gA].indexOf(B)&&c===iA)return vA;if(-1!==bA.indexOf(B)&&-1!==LA.indexOf(c)||-1!==LA.indexOf(B)&&-1!==bA.indexOf(c))return vA;if(-1!==[sA,iA].indexOf(B)&&(c===oA||-1!==[tA,W].indexOf(c)&&e[s+1]===oA)||-1!==[tA,W].indexOf(B)&&c===oA||B===oA&&-1!==[oA,BA,nA].indexOf(c))return vA;if(-1!==[oA,BA,nA,z,q].indexOf(c))for(var u=o;u>=0;){if((l=e[u])===oA)return vA;if(-1===[BA,nA].indexOf(l))break;u--}if(-1!==[sA,iA].indexOf(c))for(u=-1!==[z,q].indexOf(B)?i:o;u>=0;){var l;if((l=e[u])===oA)return vA;if(-1===[BA,nA].indexOf(l))break;u--}if(pA===B&&-1!==[pA,CA,fA,wA].indexOf(c)||-1!==[CA,fA].indexOf(B)&&-1!==[CA,UA].indexOf(c)||-1!==[UA,wA].indexOf(B)&&c===UA)return vA;if(-1!==SA.indexOf(B)&&-1!==[AA,iA].indexOf(c)||-1!==SA.indexOf(c)&&B===sA)return vA;if(-1!==bA.indexOf(B)&&-1!==bA.indexOf(c))return vA;if(B===nA&&-1!==bA.indexOf(c))return vA;if(-1!==bA.concat(oA).indexOf(B)&&c===tA&&-1===EA.indexOf(A[s])||-1!==bA.concat(oA).indexOf(c)&&B===q)return vA;if(B===dA&&c===dA){for(var g=t[o],f=1;g>0&&e[--g]===dA;)f++;if(f%2!=0)return vA}return B===lA&&c===gA?vA:mA},GA=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var t=TA(A,e.lineBreak),r=t[0],n=t[1],o=t[2];"break-all"!==e.wordBreak&&"break-word"!==e.wordBreak||(n=n.map((function(A){return-1!==[oA,cA,FA].indexOf(A)?hA:A})));var i="keep-all"===e.wordBreak?o.map((function(e,t){return e&&A[t]>=19968&&A[t]<=40959})):void 0;return[r,n,i]},PA=function(){function A(A,e,t,r){this.codePoints=A,this.required=e===HA,this.start=t,this.end=r}return A.prototype.slice=function(){return c.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),VA=function(A,e){var t=a(A),r=GA(t,e),n=r[0],o=r[1],i=r[2],s=t.length,B=0,c=0;return{next:function(){if(c>=s)return{done:!0,value:null};for(var A=vA;c<s&&(A=kA(t,o,n,++c,i))===vA;);if(A!==vA||c===s){var e=new PA(t,A,B,c);return B=c,{value:e,done:!1}}return{done:!0,value:null}}}},NA=1,_A=2,JA=4,XA=8,jA=10,YA=47,WA=92,ZA=9,zA=32,qA=34,$A=61,Ae=35,ee=36,te=37,re=39,ne=40,oe=41,ie=95,se=45,Be=33,ae=60,ce=62,ue=64,le=91,ge=93,fe=61,we=123,Qe=63,he=125,pe=124,Ce=126,Ue=128,de=65533,Fe=42,ye=43,Ee=44,He=58,ve=59,me=46,Ie=0,be=8,Ke=11,xe=14,Le=31,De=127,Se=-1,Oe=48,Te=97,Me=101,Re=102,ke=117,Ge=122,Pe=65,Ve=69,Ne=70,_e=85,Je=90,Xe=function(A){return A>=Oe&&A<=57},je=function(A){return A>=55296&&A<=57343},Ye=function(A){return Xe(A)||A>=Pe&&A<=Ne||A>=Te&&A<=Re},We=function(A){return A>=Te&&A<=Ge},Ze=function(A){return A>=Pe&&A<=Je},ze=function(A){return We(A)||Ze(A)},qe=function(A){return A>=Ue},$e=function(A){return A===jA||A===ZA||A===zA},At=function(A){return ze(A)||qe(A)||A===ie},et=function(A){return At(A)||Xe(A)||A===se},tt=function(A){return A>=Ie&&A<=be||A===Ke||A>=xe&&A<=Le||A===De},rt=function(A,e){return A===WA&&e!==jA},nt=function(A,e,t){return A===se?At(e)||rt(e,t):!!At(A)||!(A!==WA||!rt(A,e))},ot=function(A,e,t){return A===ye||A===se?!!Xe(e)||e===me&&Xe(t):Xe(A===me?e:A)},it=function(A){var e=0,t=1;A[e]!==ye&&A[e]!==se||(A[e]===se&&(t=-1),e++);for(var r=[];Xe(A[e]);)r.push(A[e++]);var n=r.length?parseInt(c.apply(void 0,r),10):0;A[e]===me&&e++;for(var o=[];Xe(A[e]);)o.push(A[e++]);var i=o.length,s=i?parseInt(c.apply(void 0,o),10):0;A[e]!==Ve&&A[e]!==Me||e++;var B=1;A[e]!==ye&&A[e]!==se||(A[e]===se&&(B=-1),e++);for(var a=[];Xe(A[e]);)a.push(A[e++]);var u=a.length?parseInt(c.apply(void 0,a),10):0;return t*(n+s*Math.pow(10,-i))*Math.pow(10,B*u)},st={type:2},Bt={type:3},at={type:4},ct={type:13},ut={type:8},lt={type:21},gt={type:9},ft={type:10},wt={type:11},Qt={type:12},ht={type:14},pt={type:23},Ct={type:1},Ut={type:25},dt={type:24},Ft={type:26},yt={type:27},Et={type:28},Ht={type:29},vt={type:31},mt={type:32},It=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(a(A))},A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==mt;)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case qA:return this.consumeStringToken(qA);case Ae:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(et(e)||rt(t,r)){var n=nt(e,t,r)?_A:NA;return{type:5,value:this.consumeName(),flags:n}}break;case ee:if(this.peekCodePoint(0)===$A)return this.consumeCodePoint(),ct;break;case re:return this.consumeStringToken(re);case ne:return st;case oe:return Bt;case Fe:if(this.peekCodePoint(0)===$A)return this.consumeCodePoint(),ht;break;case ye:if(ot(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Ee:return at;case se:var o=A,i=this.peekCodePoint(0),s=this.peekCodePoint(1);if(ot(o,i,s))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(nt(o,i,s))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(i===se&&s===ce)return this.consumeCodePoint(),this.consumeCodePoint(),dt;break;case me:if(ot(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case YA:if(this.peekCodePoint(0)===Fe)for(this.consumeCodePoint();;){var B=this.consumeCodePoint();if(B===Fe&&(B=this.consumeCodePoint())===YA)return this.consumeToken();if(B===Se)return this.consumeToken()}break;case He:return Ft;case ve:return yt;case ae:if(this.peekCodePoint(0)===Be&&this.peekCodePoint(1)===se&&this.peekCodePoint(2)===se)return this.consumeCodePoint(),this.consumeCodePoint(),Ut;break;case ue:var a=this.peekCodePoint(0),u=this.peekCodePoint(1),l=this.peekCodePoint(2);if(nt(a,u,l))return{type:7,value:this.consumeName()};break;case le:return Et;case WA:if(rt(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case ge:return Ht;case fe:if(this.peekCodePoint(0)===$A)return this.consumeCodePoint(),ut;break;case we:return wt;case he:return Qt;case ke:case _e:var g=this.peekCodePoint(0),f=this.peekCodePoint(1);return g!==ye||!Ye(f)&&f!==Qe||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case pe:if(this.peekCodePoint(0)===$A)return this.consumeCodePoint(),gt;if(this.peekCodePoint(0)===pe)return this.consumeCodePoint(),lt;break;case Ce:if(this.peekCodePoint(0)===$A)return this.consumeCodePoint(),ft;break;case Se:return mt}return $e(A)?(this.consumeWhiteSpace(),vt):Xe(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):At(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:c(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();Ye(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;e===Qe&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(c.apply(void 0,A.map((function(A){return A===Qe?Oe:A}))),16),end:parseInt(c.apply(void 0,A.map((function(A){return A===Qe?Ne:A}))),16)};var r=parseInt(c.apply(void 0,A),16);if(this.peekCodePoint(0)===se&&Ye(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var n=[];Ye(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();return{type:30,start:r,end:parseInt(c.apply(void 0,n),16)}}return{type:30,start:r,end:r}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&this.peekCodePoint(0)===ne?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===ne?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===Se)return{type:22,value:""};var e=this.peekCodePoint(0);if(e===re||e===qA){var t=this.consumeStringToken(this.consumeCodePoint());return 0===t.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===Se||this.peekCodePoint(0)===oe)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),pt)}for(;;){var r=this.consumeCodePoint();if(r===Se||r===oe)return{type:22,value:c.apply(void 0,A)};if($e(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===Se||this.peekCodePoint(0)===oe?(this.consumeCodePoint(),{type:22,value:c.apply(void 0,A)}):(this.consumeBadUrlRemnants(),pt);if(r===qA||r===re||r===ne||tt(r))return this.consumeBadUrlRemnants(),pt;if(r===WA){if(!rt(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),pt;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},A.prototype.consumeWhiteSpace=function(){for(;$e(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===oe||A===Se)return;rt(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){for(var e=5e4,t="";A>0;){var r=Math.min(e,A);t+=c.apply(void 0,this._value.splice(0,r)),A-=r}return this._value.shift(),t},A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r=this._value[t];if(r===Se||void 0===r||r===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(r===jA)return this._value.splice(0,t),Ct;if(r===WA){var n=this._value[t+1];n!==Se&&void 0!==n&&(n===jA?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):rt(r,n)&&(e+=this.consumeStringSlice(t),e+=c(this.consumeEscapedCodePoint()),t=-1))}t++}},A.prototype.consumeNumber=function(){var A=[],e=JA,t=this.peekCodePoint(0);for(t!==ye&&t!==se||A.push(this.consumeCodePoint());Xe(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(t===me&&Xe(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=XA;Xe(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((t===Ve||t===Me)&&((r===ye||r===se)&&Xe(n)||Xe(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=XA;Xe(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[it(A),e]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),o=this.peekCodePoint(2);return nt(r,n,o)?{type:15,number:e,flags:t,unit:this.consumeName()}:r===te?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(Ye(A)){for(var e=c(A);Ye(this.peekCodePoint(0))&&e.length<6;)e+=c(this.consumeCodePoint());$e(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||je(t)||t>1114111?de:t}return A===Se?de:A},A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(et(e))A+=c(e);else{if(!rt(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=c(this.consumeEscapedCodePoint())}}},A}(),bt=function(){function A(A){this._tokens=A}return A.create=function(e){var t=new It;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var e=this.consumeComponentValue();do{A=this.consumeToken()}while(31===A.type);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||Rt(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},A.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?mt:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),Kt=function(A){return 15===A.type},xt=function(A){return 17===A.type},Lt=function(A){return 20===A.type},Dt=function(A){return 0===A.type},St=function(A,e){return Lt(A)&&A.value===e},Ot=function(A){return 31!==A.type},Tt=function(A){return 31!==A.type&&4!==A.type},Mt=function(A){var e=[],t=[];return A.forEach((function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)})),t.length&&e.push(t),e},Rt=function(A,e){return 11===e&&12===A.type||28===e&&29===A.type||2===e&&3===A.type},kt=function(A){return 17===A.type||15===A.type},Gt=function(A){return 16===A.type||kt(A)},Pt=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},Vt={type:17,number:0,flags:JA},Nt={type:16,number:50,flags:JA},_t={type:16,number:100,flags:JA},Jt=function(A,e,t){var r=A[0],n=A[1];return[Xt(r,e),Xt(void 0!==n?n:r,t)]},Xt=function(A,e){if(16===A.type)return A.number/100*e;if(Kt(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},jt="deg",Yt="grad",Wt="rad",Zt="turn",zt={name:"angle",parse:function(A,e){if(15===e.type)switch(e.unit){case jt:return Math.PI*e.number/180;case Yt:return Math.PI/200*e.number;case Wt:return e.number;case Zt:return 2*Math.PI*e.number}throw new Error("Unsupported angle type")}},qt=function(A){return 15===A.type&&(A.unit===jt||A.unit===Yt||A.unit===Wt||A.unit===Zt)},$t=function(A){switch(A.filter(Lt).map((function(A){return A.value})).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[Vt,Vt];case"to top":case"bottom":return Ar(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[Vt,_t];case"to right":case"left":return Ar(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[_t,_t];case"to bottom":case"top":return Ar(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[_t,Vt];case"to left":case"right":return Ar(270)}return 0},Ar=function(A){return Math.PI*A/180},er={name:"color",parse:function(A,e){if(18===e.type){var t=ar[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),o=e.value.substring(2,3);return nr(parseInt(r+r,16),parseInt(n+n,16),parseInt(o+o,16),1)}if(4===e.value.length){r=e.value.substring(0,1),n=e.value.substring(1,2),o=e.value.substring(2,3);var i=e.value.substring(3,4);return nr(parseInt(r+r,16),parseInt(n+n,16),parseInt(o+o,16),parseInt(i+i,16)/255)}if(6===e.value.length)return r=e.value.substring(0,2),n=e.value.substring(2,4),o=e.value.substring(4,6),nr(parseInt(r,16),parseInt(n,16),parseInt(o,16),1);if(8===e.value.length)return r=e.value.substring(0,2),n=e.value.substring(2,4),o=e.value.substring(4,6),i=e.value.substring(6,8),nr(parseInt(r,16),parseInt(n,16),parseInt(o,16),parseInt(i,16)/255)}if(20===e.type){var s=ur[e.value.toUpperCase()];if(void 0!==s)return s}return ur.TRANSPARENT}},tr=function(A){return 0==(255&A)},rr=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?"rgba("+n+","+r+","+t+","+e/255+")":"rgb("+n+","+r+","+t+")"},nr=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},or=function(A,e){if(17===A.type)return A.number;if(16===A.type){var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}return 0},ir=function(A,e){var t=e.filter(Tt);if(3===t.length){var r=t.map(or),n=r[0],o=r[1],i=r[2];return nr(n,o,i,1)}if(4===t.length){var s=t.map(or),B=(n=s[0],o=s[1],i=s[2],s[3]);return nr(n,o,i,B)}return 0};function sr(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var Br=function(A,e){var t=e.filter(Tt),r=t[0],n=t[1],o=t[2],i=t[3],s=(17===r.type?Ar(r.number):zt.parse(A,r))/(2*Math.PI),B=Gt(n)?n.number/100:0,a=Gt(o)?o.number/100:0,c=void 0!==i&&Gt(i)?Xt(i,1):1;if(0===B)return nr(255*a,255*a,255*a,1);var u=a<=.5?a*(B+1):a+B-a*B,l=2*a-u,g=sr(l,u,s+1/3),f=sr(l,u,s),w=sr(l,u,s-1/3);return nr(255*g,255*f,255*w,c)},ar={hsl:Br,hsla:Br,rgb:ir,rgba:ir},cr=function(A,e){return er.parse(A,bt.create(e).parseComponentValue())},ur={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},lr={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(Lt(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},gr={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},fr=function(A,e){var t=er.parse(A,e[0]),r=e[1];return r&&Gt(r)?{color:t,stop:r}:{color:t,stop:null}},wr=function(A,e){var t=A[0],r=A[A.length-1];null===t.stop&&(t.stop=Vt),null===r.stop&&(r.stop=_t);for(var n=[],o=0,i=0;i<A.length;i++){var s=A[i].stop;if(null!==s){var B=Xt(s,e);B>o?n.push(B):n.push(o),o=B}else n.push(null)}var a=null;for(i=0;i<n.length;i++){var c=n[i];if(null===c)null===a&&(a=i);else if(null!==a){for(var u=i-a,l=(c-n[a-1])/(u+1),g=1;g<=u;g++)n[a+g-1]=l*g;a=null}}return A.map((function(A,t){return{color:A.color,stop:Math.max(Math.min(1,n[t]/e),0)}}))},Qr=function(A,e,t){var r=e/2,n=t/2,o=Xt(A[0],e)-r,i=n-Xt(A[1],t);return(Math.atan2(i,o)+2*Math.PI)%(2*Math.PI)},hr=function(A,e,t){var r="number"==typeof A?A:Qr(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),o=e/2,i=t/2,s=n/2,B=Math.sin(r-Math.PI/2)*s,a=Math.cos(r-Math.PI/2)*s;return[n,o-a,o+a,i-B,i+B]},pr=function(A,e){return Math.sqrt(A*A+e*e)},Cr=function(A,e,t,r,n){return[[0,0],[0,e],[A,0],[A,e]].reduce((function(A,e){var o=e[0],i=e[1],s=pr(t-o,r-i);return(n?s<A.optimumDistance:s>A.optimumDistance)?{optimumCorner:e,optimumDistance:s}:A}),{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Ur=function(A,e,t,r,n){var o=0,i=0;switch(A.size){case 0:0===A.shape?o=i=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(o=Math.min(Math.abs(e),Math.abs(e-r)),i=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(0===A.shape)o=i=Math.min(pr(e,t),pr(e,t-n),pr(e-r,t),pr(e-r,t-n));else if(1===A.shape){var s=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),B=Cr(r,n,e,t,!0),a=B[0],c=B[1];i=s*(o=pr(a-e,(c-t)/s))}break;case 1:0===A.shape?o=i=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(o=Math.max(Math.abs(e),Math.abs(e-r)),i=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(0===A.shape)o=i=Math.max(pr(e,t),pr(e,t-n),pr(e-r,t),pr(e-r,t-n));else if(1===A.shape){s=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r));var u=Cr(r,n,e,t,!1);a=u[0],c=u[1],i=s*(o=pr(a-e,(c-t)/s))}}return Array.isArray(A.size)&&(o=Xt(A.size[0],r),i=2===A.size.length?Xt(A.size[1],n):o),[o,i]},dr=function(A,e){var t=Ar(180),r=[];return Mt(e).forEach((function(e,n){if(0===n){var o=e[0];if(20===o.type&&-1!==["top","left","right","bottom"].indexOf(o.value))return void(t=$t(e));if(qt(o))return void(t=(zt.parse(A,o)+Ar(270))%Ar(360))}var i=fr(A,e);r.push(i)})),{angle:t,stops:r,type:1}},Fr="closest-side",yr="farthest-side",Er="closest-corner",Hr="farthest-corner",vr="circle",mr="ellipse",Ir="cover",br="contain",Kr=function(A,e){var t=0,r=3,n=[],o=[];return Mt(e).forEach((function(e,i){var s=!0;if(0===i?s=e.reduce((function(A,e){if(Lt(e))switch(e.value){case"center":return o.push(Nt),!1;case"top":case"left":return o.push(Vt),!1;case"right":case"bottom":return o.push(_t),!1}else if(Gt(e)||kt(e))return o.push(e),!1;return A}),s):1===i&&(s=e.reduce((function(A,e){if(Lt(e))switch(e.value){case vr:return t=0,!1;case mr:return t=1,!1;case br:case Fr:return r=0,!1;case yr:return r=1,!1;case Er:return r=2,!1;case Ir:case Hr:return r=3,!1}else if(kt(e)||Gt(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),s)),s){var B=fr(A,e);n.push(B)}})),{size:r,shape:t,stops:n,position:o,type:2}},xr=function(A){return 1===A.type},Lr=function(A){return 2===A.type},Dr={name:"image",parse:function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18===e.type){var r=Tr[e.name];if(void 0===r)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return r(A,e.values)}throw new Error("Unsupported image type "+e.type)}};function Sr(A){return!(20===A.type&&"none"===A.value||18===A.type&&!Tr[A.name])}var Or,Tr={"linear-gradient":function(A,e){var t=Ar(180),r=[];return Mt(e).forEach((function(e,n){if(0===n){var o=e[0];if(20===o.type&&"to"===o.value)return void(t=$t(e));if(qt(o))return void(t=zt.parse(A,o))}var i=fr(A,e);r.push(i)})),{angle:t,stops:r,type:1}},"-moz-linear-gradient":dr,"-ms-linear-gradient":dr,"-o-linear-gradient":dr,"-webkit-linear-gradient":dr,"radial-gradient":function(A,e){var t=0,r=3,n=[],o=[];return Mt(e).forEach((function(e,i){var s=!0;if(0===i){var B=!1;s=e.reduce((function(A,e){if(B)if(Lt(e))switch(e.value){case"center":return o.push(Nt),A;case"top":case"left":return o.push(Vt),A;case"right":case"bottom":return o.push(_t),A}else(Gt(e)||kt(e))&&o.push(e);else if(Lt(e))switch(e.value){case vr:return t=0,!1;case mr:return t=1,!1;case"at":return B=!0,!1;case Fr:return r=0,!1;case Ir:case yr:return r=1,!1;case br:case Er:return r=2,!1;case Hr:return r=3,!1}else if(kt(e)||Gt(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),s)}if(s){var a=fr(A,e);n.push(a)}})),{size:r,shape:t,stops:n,position:o,type:2}},"-moz-radial-gradient":Kr,"-ms-radial-gradient":Kr,"-o-radial-gradient":Kr,"-webkit-radial-gradient":Kr,"-webkit-gradient":function(A,e){var t=Ar(180),r=[],n=1,o=0,i=3,s=[];return Mt(e).forEach((function(e,t){var o=e[0];if(0===t){if(Lt(o)&&"linear"===o.value)return void(n=1);if(Lt(o)&&"radial"===o.value)return void(n=2)}if(18===o.type)if("from"===o.name){var i=er.parse(A,o.values[0]);r.push({stop:Vt,color:i})}else if("to"===o.name)i=er.parse(A,o.values[0]),r.push({stop:_t,color:i});else if("color-stop"===o.name){var s=o.values.filter(Tt);if(2===s.length){i=er.parse(A,s[1]);var B=s[0];xt(B)&&r.push({stop:{type:16,number:100*B.number,flags:B.flags},color:i})}}})),1===n?{angle:(t+Ar(180))%Ar(360),stops:r,type:n}:{size:i,shape:o,stops:r,position:s,type:n}}},Mr={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e.filter((function(A){return Tt(A)&&Sr(A)})).map((function(e){return Dr.parse(A,e)}))}},Rr={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(Lt(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},kr={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return Mt(e).map((function(A){return A.filter(Gt)})).map(Pt)}},Gr={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return Mt(e).map((function(A){return A.filter(Lt).map((function(A){return A.value})).join(" ")})).map(Pr)}},Pr=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};!function(A){A.AUTO="auto",A.CONTAIN="contain",A.COVER="cover"}(Or||(Or={}));var Vr,Nr={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,e){return Mt(e).map((function(A){return A.filter(_r)}))}},_r=function(A){return Lt(A)||Gt(A)},Jr=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Xr=Jr("top"),jr=Jr("right"),Yr=Jr("bottom"),Wr=Jr("left"),Zr=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return Pt(e.filter(Gt))}}},zr=Zr("top-left"),qr=Zr("top-right"),$r=Zr("bottom-right"),An=Zr("bottom-left"),en=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},tn=en("top"),rn=en("right"),nn=en("bottom"),on=en("left"),sn=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return Kt(e)?e.number:0}}},Bn=sn("top"),an=sn("right"),cn=sn("bottom"),un=sn("left"),ln={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},gn={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"===e?1:0}},fn={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(Lt).reduce((function(A,e){return A|wn(e.value)}),0)}},wn=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Qn={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},hn={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return 20===e.type&&"normal"===e.value?0:17===e.type||15===e.type?e.number:0}};!function(A){A.NORMAL="normal",A.STRICT="strict"}(Vr||(Vr={}));var pn,Cn={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"strict"===e?Vr.STRICT:Vr.NORMAL}},Un={name:"line-height",initialValue:"normal",prefix:!1,type:4},dn=function(A,e){return Lt(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:Gt(A)?Xt(A,e):e},Fn={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:Dr.parse(A,e)}},yn={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"===e?0:1}},En={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},Hn=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},vn=Hn("top"),mn=Hn("right"),In=Hn("bottom"),bn=Hn("left"),Kn={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(Lt).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}}))}},xn={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"===e?"break-word":"normal"}},Ln=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Dn=Ln("top"),Sn=Ln("right"),On=Ln("bottom"),Tn=Ln("left"),Mn={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},Rn={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},kn={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&St(e[0],"none")?[]:Mt(e).map((function(e){for(var t={color:ur.TRANSPARENT,offsetX:Vt,offsetY:Vt,blur:Vt},r=0,n=0;n<e.length;n++){var o=e[n];kt(o)?(0===r?t.offsetX=o:1===r?t.offsetY=o:t.blur=o,r++):t.color=er.parse(A,o)}return t}))}},Gn={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Pn={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18===e.type){var t=Vn[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}return null}},Vn={matrix:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 6===e.length?e:null},matrix3d:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number})),t=e[0],r=e[1];e[2],e[3];var n=e[4],o=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var i=e[12],s=e[13];return e[14],e[15],16===e.length?[t,r,n,o,i,s]:null}},Nn={type:16,number:50,flags:JA},_n=[Nn,Nn],Jn={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){var t=e.filter(Gt);return 2!==t.length?_n:[t[0],t[1]]}},Xn={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};!function(A){A.NORMAL="normal",A.BREAK_ALL="break-all",A.KEEP_ALL="keep-all"}(pn||(pn={}));for(var jn={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"break-all":return pn.BREAK_ALL;case"keep-all":return pn.KEEP_ALL;default:return pn.NORMAL}}},Yn={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(xt(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},Wn={name:"time",parse:function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")}},Zn={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return xt(e)?e.number:1}},zn={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},qn={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(Lt).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},$n={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],r=[];return e.forEach((function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:r.push(t.join(" ")),t.length=0}})),t.length&&r.push(t.join(" ")),r.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},Ao={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},eo={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return xt(e)?e.number:Lt(e)&&"bold"===e.value?700:400}},to={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(Lt).map((function(A){return A.value}))}},ro={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},no=function(A,e){return 0!=(A&e)},oo={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},io={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var r=[],n=e.filter(Ot),o=0;o<n.length;o++){var i=n[o],s=n[o+1];if(20===i.type){var B=s&&xt(s)?s.number:1;r.push({counter:i.value,increment:B})}}return r}},so={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(Ot),n=0;n<r.length;n++){var o=r[n],i=r[n+1];if(Lt(o)&&"none"!==o.value){var s=i&&xt(i)?i.number:0;t.push({counter:o.value,reset:s})}}return t}},Bo={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,e){return e.filter(Kt).map((function(e){return Wn.parse(A,e)}))}},ao={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var r=[],n=e.filter(Dt);if(n.length%2!=0)return null;for(var o=0;o<n.length;o+=2){var i=n[o].value,s=n[o+1].value;r.push({open:i,close:s})}return r}},co=function(A,e,t){if(!A)return"";var r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},uo={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&St(e[0],"none")?[]:Mt(e).map((function(e){for(var t={color:255,offsetX:Vt,offsetY:Vt,blur:Vt,spread:Vt,inset:!1},r=0,n=0;n<e.length;n++){var o=e[n];St(o,"inset")?t.inset=!0:kt(o)?(0===r?t.offsetX=o:1===r?t.offsetY=o:2===r?t.blur=o:t.spread=o,r++):t.color=er.parse(A,o)}return t}))}},lo={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[0,1,2],r=[];return e.filter(Lt).forEach((function(A){switch(A.value){case"stroke":r.push(1);break;case"fill":r.push(0);break;case"markers":r.push(2)}})),t.forEach((function(A){-1===r.indexOf(A)&&r.push(A)})),r}},go={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},fo={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return Kt(e)?e.number:0}},wo=function(){function A(A,e){var t,r;this.animationDuration=po(A,Bo,e.animationDuration),this.backgroundClip=po(A,lr,e.backgroundClip),this.backgroundColor=po(A,gr,e.backgroundColor),this.backgroundImage=po(A,Mr,e.backgroundImage),this.backgroundOrigin=po(A,Rr,e.backgroundOrigin),this.backgroundPosition=po(A,kr,e.backgroundPosition),this.backgroundRepeat=po(A,Gr,e.backgroundRepeat),this.backgroundSize=po(A,Nr,e.backgroundSize),this.borderTopColor=po(A,Xr,e.borderTopColor),this.borderRightColor=po(A,jr,e.borderRightColor),this.borderBottomColor=po(A,Yr,e.borderBottomColor),this.borderLeftColor=po(A,Wr,e.borderLeftColor),this.borderTopLeftRadius=po(A,zr,e.borderTopLeftRadius),this.borderTopRightRadius=po(A,qr,e.borderTopRightRadius),this.borderBottomRightRadius=po(A,$r,e.borderBottomRightRadius),this.borderBottomLeftRadius=po(A,An,e.borderBottomLeftRadius),this.borderTopStyle=po(A,tn,e.borderTopStyle),this.borderRightStyle=po(A,rn,e.borderRightStyle),this.borderBottomStyle=po(A,nn,e.borderBottomStyle),this.borderLeftStyle=po(A,on,e.borderLeftStyle),this.borderTopWidth=po(A,Bn,e.borderTopWidth),this.borderRightWidth=po(A,an,e.borderRightWidth),this.borderBottomWidth=po(A,cn,e.borderBottomWidth),this.borderLeftWidth=po(A,un,e.borderLeftWidth),this.boxShadow=po(A,uo,e.boxShadow),this.color=po(A,ln,e.color),this.direction=po(A,gn,e.direction),this.display=po(A,fn,e.display),this.float=po(A,Qn,e.cssFloat),this.fontFamily=po(A,$n,e.fontFamily),this.fontSize=po(A,Ao,e.fontSize),this.fontStyle=po(A,ro,e.fontStyle),this.fontVariant=po(A,to,e.fontVariant),this.fontWeight=po(A,eo,e.fontWeight),this.letterSpacing=po(A,hn,e.letterSpacing),this.lineBreak=po(A,Cn,e.lineBreak),this.lineHeight=po(A,Un,e.lineHeight),this.listStyleImage=po(A,Fn,e.listStyleImage),this.listStylePosition=po(A,yn,e.listStylePosition),this.listStyleType=po(A,En,e.listStyleType),this.marginTop=po(A,vn,e.marginTop),this.marginRight=po(A,mn,e.marginRight),this.marginBottom=po(A,In,e.marginBottom),this.marginLeft=po(A,bn,e.marginLeft),this.opacity=po(A,Zn,e.opacity);var n=po(A,Kn,e.overflow);this.overflowX=n[0],this.overflowY=n[n.length>1?1:0],this.overflowWrap=po(A,xn,e.overflowWrap),this.paddingTop=po(A,Dn,e.paddingTop),this.paddingRight=po(A,Sn,e.paddingRight),this.paddingBottom=po(A,On,e.paddingBottom),this.paddingLeft=po(A,Tn,e.paddingLeft),this.paintOrder=po(A,lo,e.paintOrder),this.position=po(A,Rn,e.position),this.textAlign=po(A,Mn,e.textAlign),this.textDecorationColor=po(A,zn,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=po(A,qn,null!==(r=e.textDecorationLine)&&void 0!==r?r:e.textDecoration),this.textShadow=po(A,kn,e.textShadow),this.textTransform=po(A,Gn,e.textTransform),this.transform=po(A,Pn,e.transform),this.transformOrigin=po(A,Jn,e.transformOrigin),this.visibility=po(A,Xn,e.visibility),this.webkitTextStrokeColor=po(A,go,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=po(A,fo,e.webkitTextStrokeWidth),this.wordBreak=po(A,jn,e.wordBreak),this.zIndex=po(A,Yn,e.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&0===this.visibility},A.prototype.isTransparent=function(){return tr(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return no(this.display,4)||no(this.display,33554432)||no(this.display,268435456)||no(this.display,536870912)||no(this.display,67108864)||no(this.display,134217728)},A}(),Qo=function(){function A(A,e){this.content=po(A,oo,e.content),this.quotes=po(A,ao,e.quotes)}return A}(),ho=function(){function A(A,e){this.counterIncrement=po(A,io,e.counterIncrement),this.counterReset=po(A,so,e.counterReset)}return A}(),po=function(A,e,t){var r=new It,n=null!=t?t.toString():e.initialValue;r.write(n);var o=new bt(r.read());switch(e.type){case 2:var i=o.parseComponentValue();return e.parse(A,Lt(i)?i.value:e.initialValue);case 0:return e.parse(A,o.parseComponentValue());case 1:return e.parse(A,o.parseComponentValues());case 4:return o.parseComponentValue();case 3:switch(e.format){case"angle":return zt.parse(A,o.parseComponentValue());case"color":return er.parse(A,o.parseComponentValue());case"image":return Dr.parse(A,o.parseComponentValue());case"length":var s=o.parseComponentValue();return kt(s)?s:Vt;case"length-percentage":var B=o.parseComponentValue();return Gt(B)?B:Vt;case"time":return Wn.parse(A,o.parseComponentValue())}}},Co="data-html2canvas-debug",Uo=function(A){switch(A.getAttribute(Co)){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Fo=function(A,e){var t=Uo(A);return 1===t||e===t},yo=function(){function A(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Fo(e,3),this.styles=new wo(A,window.getComputedStyle(e,null)),cs(e)&&(this.styles.animationDuration.some((function(A){return A>0}))&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=s(this.context,e),Fo(e,4)&&(this.flags|=16)}return A}(),Eo="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",Ho="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",vo="undefined"==typeof Uint8Array?[]:new Uint8Array(256),mo=0;mo<Ho.length;mo++)vo[Ho.charCodeAt(mo)]=mo;for(var Io=function(A){var e,t,r,n,o,i=.75*A.length,s=A.length,B=0;"="===A[A.length-1]&&(i--,"="===A[A.length-2]&&i--);var a="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(i):new Array(i),c=Array.isArray(a)?a:new Uint8Array(a);for(e=0;e<s;e+=4)t=vo[A.charCodeAt(e)],r=vo[A.charCodeAt(e+1)],n=vo[A.charCodeAt(e+2)],o=vo[A.charCodeAt(e+3)],c[B++]=t<<2|r>>4,c[B++]=(15&r)<<4|n>>2,c[B++]=(3&n)<<6|63&o;return a},bo=function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t},Ko=function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t},xo=5,Lo=11,Do=2,So=65536>>xo,Oo=(1<<xo)-1,To=So+(1024>>xo)+32,Mo=65536>>Lo,Ro=(1<<Lo-xo)-1,ko=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},Go=function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))},Po=function(A,e){var t=Io(A),r=Array.isArray(t)?Ko(t):new Uint32Array(t),n=Array.isArray(t)?bo(t):new Uint16Array(t),o=24,i=ko(n,o/2,r[4]/2),s=2===r[5]?ko(n,(o+r[4])/2):Go(r,Math.ceil((o+r[4])/4));return new Vo(r[0],r[1],r[2],r[3],i,s)},Vo=function(){function A(A,e,t,r,n,o){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=o}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=((e=this.index[A>>xo])<<Do)+(A&Oo),this.data[e];if(A<=65535)return e=((e=this.index[So+(A-55296>>xo)])<<Do)+(A&Oo),this.data[e];if(A<this.highStart)return e=To-Mo+(A>>Lo),e=this.index[e],e+=A>>xo&Ro,e=((e=this.index[e])<<Do)+(A&Oo),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),No="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_o="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Jo=0;Jo<No.length;Jo++)_o[No.charCodeAt(Jo)]=Jo;var Xo,jo=1,Yo=2,Wo=3,Zo=4,zo=5,qo=7,$o=8,Ai=9,ei=10,ti=11,ri=12,ni=13,oi=14,ii=15,si=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var o=A.charCodeAt(t++);56320==(64512&o)?e.push(((1023&n)<<10)+(1023&o)+65536):(e.push(n),t--)}else e.push(n)}return e},Bi=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,o="";++n<t;){var i=A[n];i<=65535?r.push(i):(i-=65536,r.push(55296+(i>>10),i%1024+56320)),(n+1===t||r.length>16384)&&(o+=String.fromCharCode.apply(String,r),r.length=0)}return o},ai=Po(Eo),ci="×",ui="÷",li=function(A){return ai.get(A)},gi=function(A,e,t){var r=t-2,n=e[r],o=e[t-1],i=e[t];if(o===Yo&&i===Wo)return ci;if(o===Yo||o===Wo||o===Zo)return ui;if(i===Yo||i===Wo||i===Zo)return ui;if(o===$o&&-1!==[$o,Ai,ti,ri].indexOf(i))return ci;if(!(o!==ti&&o!==Ai||i!==Ai&&i!==ei))return ci;if((o===ri||o===ei)&&i===ei)return ci;if(i===ni||i===zo)return ci;if(i===qo)return ci;if(o===jo)return ci;if(o===ni&&i===oi){for(;n===zo;)n=e[--r];if(n===oi)return ci}if(o===ii&&i===ii){for(var s=0;n===ii;)s++,n=e[--r];if(s%2==0)return ci}return ui},fi=function(A){var e=si(A),t=e.length,r=0,n=0,o=e.map(li);return{next:function(){if(r>=t)return{done:!0,value:null};for(var A=ci;r<t&&(A=gi(e,o,++r))===ci;);if(A!==ci||r===t){var i=Bi.apply(null,e.slice(n,r));return n=r,{value:i,done:!1}}return{done:!0,value:null}}}},wi=function(A){for(var e,t=fi(A),r=[];!(e=t.next()).done;)e.value&&r.push(e.value.slice());return r},Qi=function(A){var e=123;if(A.createRange){var t=A.createRange();if(t.getBoundingClientRect){var r=A.createElement("boundtest");r.style.height=e+"px",r.style.display="block",A.body.appendChild(r),t.selectNode(r);var n=t.getBoundingClientRect(),o=Math.round(n.height);if(A.body.removeChild(r),o===e)return!0}}return!1},hi=function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var t=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var r=e.firstChild,n=a(r.data).map((function(A){return c(A)})),o=0,i={},s=n.every((function(A,e){t.setStart(r,o),t.setEnd(r,o+A.length);var n=t.getBoundingClientRect();o+=A.length;var s=n.x>i.x||n.y>i.y;return i=n,0===e||s}));return A.body.removeChild(e),s},pi=function(){return void 0!==(new Image).crossOrigin},Ci=function(){return"string"==typeof(new XMLHttpRequest).responseType},Ui=function(A){var e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch(A){return!1}return!0},di=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},Fi=function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,o=e.toDataURL();n.src=o;var i=yi(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),Ei(i).then((function(e){r.drawImage(e,0,0);var n=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var i=A.createElement("div");return i.style.backgroundImage="url("+o+")",i.style.height=t+"px",di(n)?Ei(yi(t,t,0,0,i)):Promise.reject(!1)})).then((function(A){return r.drawImage(A,0,0),di(r.getImageData(0,0,t,t).data)})).catch((function(){return!1}))},yi=function(A,e,t,r,n){var o="http://www.w3.org/2000/svg",i=document.createElementNS(o,"svg"),s=document.createElementNS(o,"foreignObject");return i.setAttributeNS(null,"width",A.toString()),i.setAttributeNS(null,"height",e.toString()),s.setAttributeNS(null,"width","100%"),s.setAttributeNS(null,"height","100%"),s.setAttributeNS(null,"x",t.toString()),s.setAttributeNS(null,"y",r.toString()),s.setAttributeNS(null,"externalResourcesRequired","true"),i.appendChild(s),s.appendChild(n),i},Ei=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Hi={get SUPPORT_RANGE_BOUNDS(){var A=Qi(document);return Object.defineProperty(Hi,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=Hi.SUPPORT_RANGE_BOUNDS&&hi(document);return Object.defineProperty(Hi,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=Ui(document);return Object.defineProperty(Hi,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?Fi(document):Promise.resolve(!1);return Object.defineProperty(Hi,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=pi();return Object.defineProperty(Hi,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A=Ci();return Object.defineProperty(Hi,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Hi,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(Hi,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},vi=function(){function A(A,e){this.text=A,this.bounds=e}return A}(),mi=function(A,e,t,r){var n=Li(e,t),o=[],s=0;return n.forEach((function(e){if(t.textDecorationLine.length||e.trim().length>0)if(Hi.SUPPORT_RANGE_BOUNDS){var n=bi(r,s,e.length).getClientRects();if(n.length>1){var B=Ki(e),a=0;B.forEach((function(e){o.push(new vi(e,i.fromDOMRectList(A,bi(r,a+s,e.length).getClientRects()))),a+=e.length}))}else o.push(new vi(e,i.fromDOMRectList(A,n)))}else{var c=r.splitText(e.length);o.push(new vi(e,Ii(A,r))),r=c}else Hi.SUPPORT_RANGE_BOUNDS||(r=r.splitText(e.length));s+=e.length})),o},Ii=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));var n=e.parentNode;if(n){n.replaceChild(r,e);var o=s(A,r);return r.firstChild&&n.replaceChild(r.firstChild,r),o}}return i.EMPTY},bi=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),n},Ki=function(A){if(Hi.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return wi(A)},xi=function(A,e){if(Hi.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return Si(A,e)},Li=function(A,e){return 0!==e.letterSpacing?Ki(A):xi(A,e)},Di=[32,160,4961,65792,65793,4153,4241],Si=function(A,e){for(var t,r=VA(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),n=[],o=function(){if(t.value){var A=t.value.slice(),e=a(A),r="";e.forEach((function(A){-1===Di.indexOf(A)?r+=c(A):(r.length&&n.push(r),n.push(c(A)),r="")})),r.length&&n.push(r)}};!(t=r.next()).done;)o();return n},Oi=function(){function A(A,e,t){this.text=Ti(e.data,t.textTransform),this.textBounds=mi(A,this.text,t,e)}return A}(),Ti=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(Mi,Ri);case 2:return A.toUpperCase();default:return A}},Mi=/(^|\s|:|-|\(|\))([a-z])/g,Ri=function(A,e,t){return A.length>0?e+t.toUpperCase():A},ki=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,r.context.cache.addImage(r.src),r}return e(t,A),t}(yo),Gi=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return e(t,A),t}(yo),Pi=function(A){function t(e,t){var r=A.call(this,e,t)||this,n=new XMLSerializer,o=s(e,t);return t.setAttribute("width",o.width+"px"),t.setAttribute("height",o.height+"px"),r.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(t)),r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,r.context.cache.addImage(r.svg),r}return e(t,A),t}(yo),Vi=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return e(t,A),t}(yo),Ni=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r.start=t.start,r.reversed="boolean"==typeof t.reversed&&!0===t.reversed,r}return e(t,A),t}(yo),_i=[{type:15,flags:0,unit:"px",number:3}],Ji=[{type:16,flags:0,number:50}],Xi=function(A){return A.width>A.height?new i(A.left+(A.width-A.height)/2,A.top,A.height,A.height):A.width<A.height?new i(A.left,A.top+(A.height-A.width)/2,A.width,A.width):A},ji=function(A){var e=A.type===Zi?new Array(A.value.length+1).join("•"):A.value;return 0===e.length?A.placeholder||"":e},Yi="checkbox",Wi="radio",Zi="password",zi=707406591,qi=function(A){function t(e,t){var r=A.call(this,e,t)||this;switch(r.type=t.type.toLowerCase(),r.checked=t.checked,r.value=ji(t),r.type!==Yi&&r.type!==Wi||(r.styles.backgroundColor=3739148031,r.styles.borderTopColor=r.styles.borderRightColor=r.styles.borderBottomColor=r.styles.borderLeftColor=2779096575,r.styles.borderTopWidth=r.styles.borderRightWidth=r.styles.borderBottomWidth=r.styles.borderLeftWidth=1,r.styles.borderTopStyle=r.styles.borderRightStyle=r.styles.borderBottomStyle=r.styles.borderLeftStyle=1,r.styles.backgroundClip=[0],r.styles.backgroundOrigin=[0],r.bounds=Xi(r.bounds)),r.type){case Yi:r.styles.borderTopRightRadius=r.styles.borderTopLeftRadius=r.styles.borderBottomRightRadius=r.styles.borderBottomLeftRadius=_i;break;case Wi:r.styles.borderTopRightRadius=r.styles.borderTopLeftRadius=r.styles.borderBottomRightRadius=r.styles.borderBottomLeftRadius=Ji}return r}return e(t,A),t}(yo),$i=function(A){function t(e,t){var r=A.call(this,e,t)||this,n=t.options[t.selectedIndex||0];return r.value=n&&n.text||"",r}return e(t,A),t}(yo),As=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return e(t,A),t}(yo),es=function(A){function t(e,t){var r=A.call(this,e,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=os(e,t.contentWindow.document.documentElement);var n=t.contentWindow.document.documentElement?cr(e,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):ur.TRANSPARENT,o=t.contentWindow.document.body?cr(e,getComputedStyle(t.contentWindow.document.body).backgroundColor):ur.TRANSPARENT;r.backgroundColor=tr(n)?tr(o)?r.styles.backgroundColor:o:n}}catch(A){}return r}return e(t,A),t}(yo),ts=["OL","UL","MENU"],rs=function(A,e,t,r){for(var n=e.firstChild,o=void 0;n;n=o)if(o=n.nextSibling,Bs(n)&&n.data.trim().length>0)t.textNodes.push(new Oi(A,n,t.styles));else if(as(n))if(vs(n)&&n.assignedNodes)n.assignedNodes().forEach((function(e){return rs(A,e,t,r)}));else{var i=ns(A,n);i.styles.isVisible()&&(is(n,i,r)?i.flags|=4:ss(i.styles)&&(i.flags|=2),-1!==ts.indexOf(n.tagName)&&(i.flags|=8),t.elements.push(i),n.slot,n.shadowRoot?rs(A,n.shadowRoot,i,r):Es(n)||Qs(n)||Hs(n)||rs(A,n,i,r))}},ns=function(A,e){return Us(e)?new ki(A,e):ps(e)?new Gi(A,e):Qs(e)?new Pi(A,e):ls(e)?new Vi(A,e):gs(e)?new Ni(A,e):fs(e)?new qi(A,e):Hs(e)?new $i(A,e):Es(e)?new As(A,e):ds(e)?new es(A,e):new yo(A,e)},os=function(A,e){var t=ns(A,e);return t.flags|=4,rs(A,e,t,t),t},is=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||hs(A)&&t.styles.isTransparent()},ss=function(A){return A.isPositioned()||A.isFloating()},Bs=function(A){return A.nodeType===Node.TEXT_NODE},as=function(A){return A.nodeType===Node.ELEMENT_NODE},cs=function(A){return as(A)&&void 0!==A.style&&!us(A)},us=function(A){return"object"==typeof A.className},ls=function(A){return"LI"===A.tagName},gs=function(A){return"OL"===A.tagName},fs=function(A){return"INPUT"===A.tagName},ws=function(A){return"HTML"===A.tagName},Qs=function(A){return"svg"===A.tagName},hs=function(A){return"BODY"===A.tagName},ps=function(A){return"CANVAS"===A.tagName},Cs=function(A){return"VIDEO"===A.tagName},Us=function(A){return"IMG"===A.tagName},ds=function(A){return"IFRAME"===A.tagName},Fs=function(A){return"STYLE"===A.tagName},ys=function(A){return"SCRIPT"===A.tagName},Es=function(A){return"TEXTAREA"===A.tagName},Hs=function(A){return"SELECT"===A.tagName},vs=function(A){return"SLOT"===A.tagName},ms=function(A){return A.tagName.indexOf("-")>0},Is=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var e=this.counters[A];return e&&e.length?e[e.length-1]:1},A.prototype.getCounterValues=function(A){var e=this.counters[A];return e||[]},A.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},A.prototype.parse=function(A){var e=this,t=A.counterIncrement,r=A.counterReset,n=!0;null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&0!==A.increment&&(n=!1,t.length||t.push(1),t[Math.max(0,t.length-1)]+=A.increment)}));var o=[];return n&&r.forEach((function(A){var t=e.counters[A.counter];o.push(A.counter),t||(t=e.counters[A.counter]=[]),t.push(A.reset)})),o},A}(),bs={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Ks={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},xs={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Ls={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},Ds=function(A,e,t,r,n,o){return A<e||A>t?Xs(A,n,o.length>0):r.integers.reduce((function(e,t,n){for(;A>=t;)A-=t,e+=r.values[n];return e}),"")+o},Ss=function(A,e,t,r){var n="";do{t||A--,n=r(A)+n,A/=e}while(A*e>=e);return n},Os=function(A,e,t,r,n){var o=t-e+1;return(A<0?"-":"")+(Ss(Math.abs(A),o,r,(function(A){return c(Math.floor(A%o)+e)}))+n)},Ts=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return Ss(Math.abs(A),r,!1,(function(A){return e[Math.floor(A%r)]}))+t},Ms=1,Rs=2,ks=4,Gs=8,Ps=function(A,e,t,r,n,o){if(A<-9999||A>9999)return Xs(A,4,n.length>0);var i=Math.abs(A),s=n;if(0===i)return e[0]+s;for(var B=0;i>0&&B<=4;B++){var a=i%10;0===a&&no(o,Ms)&&""!==s?s=e[a]+s:a>1||1===a&&0===B||1===a&&1===B&&no(o,Rs)||1===a&&1===B&&no(o,ks)&&A>100||1===a&&B>1&&no(o,Gs)?s=e[a]+(B>0?t[B-1]:"")+s:1===a&&B>0&&(s=t[B-1]+s),i=Math.floor(i/10)}return(A<0?r:"")+s},Vs="十百千萬",Ns="拾佰仟萬",_s="マイナス",Js="마이너스",Xs=function(A,e,t){var r=t?". ":"",n=t?"、":"",o=t?", ":"",i=t?" ":"";switch(e){case 0:return"•"+i;case 1:return"◦"+i;case 2:return"◾"+i;case 5:var s=Os(A,48,57,!0,r);return s.length<4?"0"+s:s;case 4:return Ts(A,"〇一二三四五六七八九",n);case 6:return Ds(A,1,3999,bs,3,r).toLowerCase();case 7:return Ds(A,1,3999,bs,3,r);case 8:return Os(A,945,969,!1,r);case 9:return Os(A,97,122,!1,r);case 10:return Os(A,65,90,!1,r);case 11:return Os(A,1632,1641,!0,r);case 12:case 49:return Ds(A,1,9999,Ks,3,r);case 35:return Ds(A,1,9999,Ks,3,r).toLowerCase();case 13:return Os(A,2534,2543,!0,r);case 14:case 30:return Os(A,6112,6121,!0,r);case 15:return Ts(A,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return Ts(A,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return Ps(A,"零一二三四五六七八九",Vs,"負",n,Rs|ks|Gs);case 47:return Ps(A,"零壹貳參肆伍陸柒捌玖",Ns,"負",n,Ms|Rs|ks|Gs);case 42:return Ps(A,"零一二三四五六七八九",Vs,"负",n,Rs|ks|Gs);case 41:return Ps(A,"零壹贰叁肆伍陆柒捌玖",Ns,"负",n,Ms|Rs|ks|Gs);case 26:return Ps(A,"〇一二三四五六七八九","十百千万",_s,n,0);case 25:return Ps(A,"零壱弐参四伍六七八九","拾百千万",_s,n,Ms|Rs|ks);case 31:return Ps(A,"영일이삼사오육칠팔구","십백천만",Js,o,Ms|Rs|ks);case 33:return Ps(A,"零一二三四五六七八九","十百千萬",Js,o,0);case 32:return Ps(A,"零壹貳參四五六七八九","拾百千",Js,o,Ms|Rs|ks);case 18:return Os(A,2406,2415,!0,r);case 20:return Ds(A,1,19999,Ls,3,r);case 21:return Os(A,2790,2799,!0,r);case 22:return Os(A,2662,2671,!0,r);case 22:return Ds(A,1,10999,xs,3,r);case 23:return Ts(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return Ts(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return Os(A,3302,3311,!0,r);case 28:return Ts(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return Ts(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return Os(A,3792,3801,!0,r);case 37:return Os(A,6160,6169,!0,r);case 38:return Os(A,4160,4169,!0,r);case 39:return Os(A,2918,2927,!0,r);case 40:return Os(A,1776,1785,!0,r);case 43:return Os(A,3046,3055,!0,r);case 44:return Os(A,3174,3183,!0,r);case 45:return Os(A,3664,3673,!0,r);case 46:return Os(A,3872,3881,!0,r);default:return Os(A,48,57,!0,r)}},js="data-html2canvas-ignore",Ys=function(){function A(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new Is,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,e){var t=this,o=Zs(A,e);if(!o.contentWindow)return Promise.reject("Unable to find iframe window");var i=A.defaultView.pageXOffset,s=A.defaultView.pageYOffset,B=o.contentWindow,a=B.document,c=$s(o).then((function(){return r(t,void 0,void 0,(function(){var A,t;return n(this,(function(r){switch(r.label){case 0:return this.scrolledElements.forEach(nB),B&&(B.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||B.scrollY===e.top&&B.scrollX===e.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(B.scrollX-e.left,B.scrollY-e.top,0,0))),A=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:a.fonts&&a.fonts.ready?[4,a.fonts.ready]:[3,2];case 1:r.sent(),r.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,qs(a)]:[3,4];case 3:r.sent(),r.label=4;case 4:return"function"==typeof A?[2,Promise.resolve().then((function(){return A(a,t)})).then((function(){return o}))]:[2,o]}}))}))}));return a.open(),a.write(tB(document.doctype)+"<html></html>"),rB(this.referenceElement.ownerDocument,i,s),a.replaceChild(a.adoptNode(this.documentElement),a.documentElement),a.close(),c},A.prototype.createElementClone=function(A){if(Fo(A,2),ps(A))return this.createCanvasClone(A);if(Cs(A))return this.createVideoClone(A);if(Fs(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return Us(e)&&(Us(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),ms(e)?this.createCustomElementClone(e):e},A.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return eB(A.style,e),e},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A}),""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(A){if(this.context.logger.error("Unable to access cssRules property",A),"SecurityError"!==A.name)throw A}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){var e;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(e){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var r=A.cloneNode(!1);try{r.width=A.width,r.height=A.height;var n=A.getContext("2d"),o=r.getContext("2d");if(o)if(!this.options.allowTaint&&n)o.putImageData(n.getImageData(0,0,A.width,A.height),0,0);else{var i=null!==(e=A.getContext("webgl2"))&&void 0!==e?e:A.getContext("webgl");if(i){var s=i.getContextAttributes();!1===(null==s?void 0:s.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}o.drawImage(A,0,0)}return r}catch(e){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return r},A.prototype.createVideoClone=function(A){var e=A.ownerDocument.createElement("canvas");e.width=A.offsetWidth,e.height=A.offsetHeight;var t=e.getContext("2d");try{return t&&(t.drawImage(A,0,0,e.width,e.height),this.options.allowTaint||t.getImageData(0,0,e.width,e.height)),e}catch(e){this.context.logger.info("Unable to clone video as it is tainted",A)}var r=A.ownerDocument.createElement("canvas");return r.width=A.offsetWidth,r.height=A.offsetHeight,r},A.prototype.appendChildNode=function(A,e,t){as(e)&&(ys(e)||e.hasAttribute(js)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&as(e)&&Fs(e)||A.appendChild(this.cloneNode(e,t))},A.prototype.cloneChildNodes=function(A,e,t){for(var r=this,n=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;n;n=n.nextSibling)if(as(n)&&vs(n)&&"function"==typeof n.assignedNodes){var o=n.assignedNodes();o.length&&o.forEach((function(A){return r.appendChildNode(e,A,t)}))}else this.appendChildNode(e,n,t)},A.prototype.cloneNode=function(A,e){if(Bs(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&as(A)&&(cs(A)||us(A))){var r=this.createElementClone(A);r.style.transitionProperty="none";var n=t.getComputedStyle(A),o=t.getComputedStyle(A,":before"),i=t.getComputedStyle(A,":after");this.referenceElement===A&&cs(r)&&(this.clonedReferenceElement=r),hs(r)&&cB(r);var s=this.counters.parse(new ho(this.context,n)),B=this.resolvePseudoContent(A,r,o,Xo.BEFORE);ms(A)&&(e=!0),Cs(A)||this.cloneChildNodes(A,r,e),B&&r.insertBefore(B,r.firstChild);var a=this.resolvePseudoContent(A,r,i,Xo.AFTER);return a&&r.appendChild(a),this.counters.pop(s),(n&&(this.options.copyStyles||us(A))&&!ds(A)||e)&&eB(n,r),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(Es(A)||Hs(A))&&(Es(r)||Hs(r))&&(r.value=A.value),r}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,e,t,r){var n=this;if(t){var o=t.content,i=e.ownerDocument;if(i&&o&&"none"!==o&&"-moz-alt-content"!==o&&"none"!==t.display){this.counters.parse(new ho(this.context,t));var s=new Qo(this.context,t),B=i.createElement("html2canvaspseudoelement");eB(t,B),s.content.forEach((function(e){if(0===e.type)B.appendChild(i.createTextNode(e.value));else if(22===e.type){var t=i.createElement("img");t.src=e.value,t.style.opacity="1",B.appendChild(t)}else if(18===e.type){if("attr"===e.name){var r=e.values.filter(Lt);r.length&&B.appendChild(i.createTextNode(A.getAttribute(r[0].value)||""))}else if("counter"===e.name){var o=e.values.filter(Tt),a=o[0],c=o[1];if(a&&Lt(a)){var u=n.counters.getCounterValue(a.value),l=c&&Lt(c)?En.parse(n.context,c.value):3;B.appendChild(i.createTextNode(Xs(u,l,!1)))}}else if("counters"===e.name){var g=e.values.filter(Tt),f=(a=g[0],g[1]);if(c=g[2],a&&Lt(a)){var w=n.counters.getCounterValues(a.value),Q=c&&Lt(c)?En.parse(n.context,c.value):3,h=f&&0===f.type?f.value:"",p=w.map((function(A){return Xs(A,Q,!1)})).join(h);B.appendChild(i.createTextNode(p))}}}else if(20===e.type)switch(e.value){case"open-quote":B.appendChild(i.createTextNode(co(s.quotes,n.quoteDepth++,!0)));break;case"close-quote":B.appendChild(i.createTextNode(co(s.quotes,--n.quoteDepth,!1)));break;default:B.appendChild(i.createTextNode(e.value))}})),B.className=sB+" "+BB;var a=r===Xo.BEFORE?" "+sB:" "+BB;return us(e)?e.className.baseValue+=a:e.className+=a,B}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();!function(A){A[A.BEFORE=0]="BEFORE",A[A.AFTER=1]="AFTER"}(Xo||(Xo={}));var Ws,Zs=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(js,"true"),A.body.appendChild(t),t},zs=function(A){return new Promise((function(e){A.complete?e():A.src?(A.onload=e,A.onerror=e):e()}))},qs=function(A){return Promise.all([].slice.call(A.images,0).map(zs))},$s=function(A){return new Promise((function(e,t){var r=A.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=A.onload=function(){r.onload=A.onload=null;var t=setInterval((function(){n.body.childNodes.length>0&&"complete"===n.readyState&&(clearInterval(t),e(A))}),50)}}))},AB=["all","d","content"],eB=function(A,e){for(var t=A.length-1;t>=0;t--){var r=A.item(t);-1===AB.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},tB=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},rB=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},nB=function(A){var e=A[0],t=A[1],r=A[2];e.scrollLeft=t,e.scrollTop=r},oB=":before",iB=":after",sB="___html2canvas___pseudoelement_before",BB="___html2canvas___pseudoelement_after",aB='{\n    content: "" !important;\n    display: none !important;\n}',cB=function(A){uB(A,"."+sB+oB+aB+"\n         ."+BB+iB+aB)},uB=function(A,e){var t=A.ownerDocument;if(t){var r=t.createElement("style");r.textContent=e,A.appendChild(r)}},lB=function(){function A(){}return A.getOrigin=function(e){var t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A._origin="about:blank",A}(),gB=function(){function A(A,e){this.context=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)?e:UB(A)||hB(A)?((this._cache[A]=this.loadImage(A)).catch((function(){})),e):e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return r(this,void 0,void 0,(function(){var e,t,r,o,i=this;return n(this,(function(n){switch(n.label){case 0:return e=lB.isSameOrigin(A),t=!pB(A)&&!0===this._options.useCORS&&Hi.SUPPORT_CORS_IMAGES&&!e,r=!pB(A)&&!e&&!UB(A)&&"string"==typeof this._options.proxy&&Hi.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||pB(A)||UB(A)||r||t?(o=A,r?[4,this.proxy(o)]:[3,2]):[2];case 1:o=n.sent(),n.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var r=new Image;r.onload=function(){return A(r)},r.onerror=e,(CB(o)||t)&&(r.crossOrigin="anonymous"),r.src=o,!0===r.complete&&setTimeout((function(){return A(r)}),500),i._options.imageTimeout>0&&setTimeout((function(){return e("Timed out ("+i._options.imageTimeout+"ms) loading image")}),i._options.imageTimeout)}))];case 3:return[2,n.sent()]}}))}))},A.prototype.has=function(A){return void 0!==this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var r=A.substring(0,256);return new Promise((function(n,o){var i=Hi.SUPPORT_RESPONSE_TYPE?"blob":"text",s=new XMLHttpRequest;s.onload=function(){if(200===s.status)if("text"===i)n(s.response);else{var A=new FileReader;A.addEventListener("load",(function(){return n(A.result)}),!1),A.addEventListener("error",(function(A){return o(A)}),!1),A.readAsDataURL(s.response)}else o("Failed to proxy resource "+r+" with status code "+s.status)},s.onerror=o;var B=t.indexOf("?")>-1?"&":"?";if(s.open("GET",""+t+B+"url="+encodeURIComponent(A)+"&responseType="+i),"text"!==i&&s instanceof XMLHttpRequest&&(s.responseType=i),e._options.imageTimeout){var a=e._options.imageTimeout;s.timeout=a,s.ontimeout=function(){return o("Timed out ("+a+"ms) proxying "+r)}}s.send()}))},A}(),fB=/^data:image\/svg\+xml/i,wB=/^data:image\/.*;base64,/i,QB=/^data:image\/.*/i,hB=function(A){return Hi.SUPPORT_SVG_DRAWING||!dB(A)},pB=function(A){return QB.test(A)},CB=function(A){return wB.test(A)},UB=function(A){return"blob"===A.substr(0,4)},dB=function(A){return"svg"===A.substr(-3).toLowerCase()||fB.test(A)},FB=function(){function A(A,e){this.type=0,this.x=A,this.y=e}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),yB=function(A,e,t){return new FB(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},EB=function(){function A(A,e,t,r){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return A.prototype.subdivide=function(e,t){var r=yB(this.start,this.startControl,e),n=yB(this.startControl,this.endControl,e),o=yB(this.endControl,this.end,e),i=yB(r,n,e),s=yB(n,o,e),B=yB(i,s,e);return t?new A(this.start,r,i,B):new A(B,s,o,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),HB=function(A){return 1===A.type},vB=function(){function A(A){var e=A.styles,t=A.bounds,r=Jt(e.borderTopLeftRadius,t.width,t.height),n=r[0],o=r[1],i=Jt(e.borderTopRightRadius,t.width,t.height),s=i[0],B=i[1],a=Jt(e.borderBottomRightRadius,t.width,t.height),c=a[0],u=a[1],l=Jt(e.borderBottomLeftRadius,t.width,t.height),g=l[0],f=l[1],w=[];w.push((n+s)/t.width),w.push((g+c)/t.width),w.push((o+f)/t.height),w.push((B+u)/t.height);var Q=Math.max.apply(Math,w);Q>1&&(n/=Q,o/=Q,s/=Q,B/=Q,c/=Q,u/=Q,g/=Q,f/=Q);var h=t.width-s,p=t.height-u,C=t.width-c,U=t.height-f,d=e.borderTopWidth,F=e.borderRightWidth,y=e.borderBottomWidth,E=e.borderLeftWidth,H=Xt(e.paddingTop,A.bounds.width),v=Xt(e.paddingRight,A.bounds.width),m=Xt(e.paddingBottom,A.bounds.width),I=Xt(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=n>0||o>0?mB(t.left+E/3,t.top+d/3,n-E/3,o-d/3,Ws.TOP_LEFT):new FB(t.left+E/3,t.top+d/3),this.topRightBorderDoubleOuterBox=n>0||o>0?mB(t.left+h,t.top+d/3,s-F/3,B-d/3,Ws.TOP_RIGHT):new FB(t.left+t.width-F/3,t.top+d/3),this.bottomRightBorderDoubleOuterBox=c>0||u>0?mB(t.left+C,t.top+p,c-F/3,u-y/3,Ws.BOTTOM_RIGHT):new FB(t.left+t.width-F/3,t.top+t.height-y/3),this.bottomLeftBorderDoubleOuterBox=g>0||f>0?mB(t.left+E/3,t.top+U,g-E/3,f-y/3,Ws.BOTTOM_LEFT):new FB(t.left+E/3,t.top+t.height-y/3),this.topLeftBorderDoubleInnerBox=n>0||o>0?mB(t.left+2*E/3,t.top+2*d/3,n-2*E/3,o-2*d/3,Ws.TOP_LEFT):new FB(t.left+2*E/3,t.top+2*d/3),this.topRightBorderDoubleInnerBox=n>0||o>0?mB(t.left+h,t.top+2*d/3,s-2*F/3,B-2*d/3,Ws.TOP_RIGHT):new FB(t.left+t.width-2*F/3,t.top+2*d/3),this.bottomRightBorderDoubleInnerBox=c>0||u>0?mB(t.left+C,t.top+p,c-2*F/3,u-2*y/3,Ws.BOTTOM_RIGHT):new FB(t.left+t.width-2*F/3,t.top+t.height-2*y/3),this.bottomLeftBorderDoubleInnerBox=g>0||f>0?mB(t.left+2*E/3,t.top+U,g-2*E/3,f-2*y/3,Ws.BOTTOM_LEFT):new FB(t.left+2*E/3,t.top+t.height-2*y/3),this.topLeftBorderStroke=n>0||o>0?mB(t.left+E/2,t.top+d/2,n-E/2,o-d/2,Ws.TOP_LEFT):new FB(t.left+E/2,t.top+d/2),this.topRightBorderStroke=n>0||o>0?mB(t.left+h,t.top+d/2,s-F/2,B-d/2,Ws.TOP_RIGHT):new FB(t.left+t.width-F/2,t.top+d/2),this.bottomRightBorderStroke=c>0||u>0?mB(t.left+C,t.top+p,c-F/2,u-y/2,Ws.BOTTOM_RIGHT):new FB(t.left+t.width-F/2,t.top+t.height-y/2),this.bottomLeftBorderStroke=g>0||f>0?mB(t.left+E/2,t.top+U,g-E/2,f-y/2,Ws.BOTTOM_LEFT):new FB(t.left+E/2,t.top+t.height-y/2),this.topLeftBorderBox=n>0||o>0?mB(t.left,t.top,n,o,Ws.TOP_LEFT):new FB(t.left,t.top),this.topRightBorderBox=s>0||B>0?mB(t.left+h,t.top,s,B,Ws.TOP_RIGHT):new FB(t.left+t.width,t.top),this.bottomRightBorderBox=c>0||u>0?mB(t.left+C,t.top+p,c,u,Ws.BOTTOM_RIGHT):new FB(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=g>0||f>0?mB(t.left,t.top+U,g,f,Ws.BOTTOM_LEFT):new FB(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||o>0?mB(t.left+E,t.top+d,Math.max(0,n-E),Math.max(0,o-d),Ws.TOP_LEFT):new FB(t.left+E,t.top+d),this.topRightPaddingBox=s>0||B>0?mB(t.left+Math.min(h,t.width-F),t.top+d,h>t.width+F?0:Math.max(0,s-F),Math.max(0,B-d),Ws.TOP_RIGHT):new FB(t.left+t.width-F,t.top+d),this.bottomRightPaddingBox=c>0||u>0?mB(t.left+Math.min(C,t.width-E),t.top+Math.min(p,t.height-y),Math.max(0,c-F),Math.max(0,u-y),Ws.BOTTOM_RIGHT):new FB(t.left+t.width-F,t.top+t.height-y),this.bottomLeftPaddingBox=g>0||f>0?mB(t.left+E,t.top+Math.min(U,t.height-y),Math.max(0,g-E),Math.max(0,f-y),Ws.BOTTOM_LEFT):new FB(t.left+E,t.top+t.height-y),this.topLeftContentBox=n>0||o>0?mB(t.left+E+I,t.top+d+H,Math.max(0,n-(E+I)),Math.max(0,o-(d+H)),Ws.TOP_LEFT):new FB(t.left+E+I,t.top+d+H),this.topRightContentBox=s>0||B>0?mB(t.left+Math.min(h,t.width+E+I),t.top+d+H,h>t.width+E+I?0:s-E+I,B-(d+H),Ws.TOP_RIGHT):new FB(t.left+t.width-(F+v),t.top+d+H),this.bottomRightContentBox=c>0||u>0?mB(t.left+Math.min(C,t.width-(E+I)),t.top+Math.min(p,t.height+d+H),Math.max(0,c-(F+v)),u-(y+m),Ws.BOTTOM_RIGHT):new FB(t.left+t.width-(F+v),t.top+t.height-(y+m)),this.bottomLeftContentBox=g>0||f>0?mB(t.left+E+I,t.top+U,Math.max(0,g-(E+I)),f-(y+m),Ws.BOTTOM_LEFT):new FB(t.left+E+I,t.top+t.height-(y+m))}return A}();!function(A){A[A.TOP_LEFT=0]="TOP_LEFT",A[A.TOP_RIGHT=1]="TOP_RIGHT",A[A.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",A[A.BOTTOM_LEFT=3]="BOTTOM_LEFT"}(Ws||(Ws={}));var mB=function(A,e,t,r,n){var o=(Math.sqrt(2)-1)/3*4,i=t*o,s=r*o,B=A+t,a=e+r;switch(n){case Ws.TOP_LEFT:return new EB(new FB(A,a),new FB(A,a-s),new FB(B-i,e),new FB(B,e));case Ws.TOP_RIGHT:return new EB(new FB(A,e),new FB(A+i,e),new FB(B,a-s),new FB(B,a));case Ws.BOTTOM_RIGHT:return new EB(new FB(B,e),new FB(B,e+s),new FB(A+i,a),new FB(A,a));case Ws.BOTTOM_LEFT:default:return new EB(new FB(B,a),new FB(B-i,a),new FB(A,e+s),new FB(A,e))}},IB=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},bB=function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]},KB=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},xB=function(){function A(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6}return A}(),LB=function(){function A(A,e){this.path=A,this.target=e,this.type=1}return A}(),DB=function(){function A(A){this.opacity=A,this.type=2,this.target=6}return A}(),SB=function(A){return 0===A.type},OB=function(A){return 1===A.type},TB=function(A){return 2===A.type},MB=function(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))},RB=function(A,e,t,r,n){return A.map((function(A,o){switch(o){case 0:return A.add(e,t);case 1:return A.add(e+r,t);case 2:return A.add(e+r,t+n);case 3:return A.add(e,t+n)}return A}))},kB=function(){function A(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return A}(),GB=function(){function A(A,e){if(this.container=A,this.parent=e,this.effects=[],this.curves=new vB(this.container),this.container.styles.opacity<1&&this.effects.push(new DB(this.container.styles.opacity)),null!==this.container.styles.transform){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,r=this.container.bounds.top+this.container.styles.transformOrigin[1].number,n=this.container.styles.transform;this.effects.push(new xB(t,r,n))}if(0!==this.container.styles.overflowX){var o=IB(this.curves),i=KB(this.curves);MB(o,i)?this.effects.push(new LB(o,6)):(this.effects.push(new LB(o,2)),this.effects.push(new LB(i,4)))}}return A.prototype.getEffects=function(A){for(var e=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,r=this.effects.slice(0);t;){var n=t.effects.filter((function(A){return!OB(A)}));if(e||0!==t.container.styles.position||!t.parent){if(r.unshift.apply(r,n),e=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX){var o=IB(t.curves),i=KB(t.curves);MB(o,i)||r.unshift(new LB(i,6))}}else r.unshift.apply(r,n);t=t.parent}return r.filter((function(e){return no(e.target,A)}))},A}(),PB=function(A,e,t,r){A.container.elements.forEach((function(n){var o=no(n.flags,4),i=no(n.flags,2),s=new GB(n,A);no(n.styles.display,2048)&&r.push(s);var B=no(n.flags,8)?[]:r;if(o||i){var a=o||n.styles.isPositioned()?t:e,c=new kB(s);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var u=n.styles.zIndex.order;if(u<0){var l=0;a.negativeZIndex.some((function(A,e){return u>A.element.container.styles.zIndex.order?(l=e,!1):l>0})),a.negativeZIndex.splice(l,0,c)}else if(u>0){var g=0;a.positiveZIndex.some((function(A,e){return u>=A.element.container.styles.zIndex.order?(g=e+1,!1):g>0})),a.positiveZIndex.splice(g,0,c)}else a.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?a.nonPositionedFloats.push(c):a.nonPositionedInlineLevel.push(c);PB(s,c,o?c:t,B)}else n.styles.isInlineLevel()?e.inlineLevel.push(s):e.nonInlineLevel.push(s),PB(s,e,t,B);no(n.flags,8)&&VB(n,B)}))},VB=function(A,e){for(var t=A instanceof Ni?A.start:1,r=A instanceof Ni&&A.reversed,n=0;n<e.length;n++){var o=e[n];o.container instanceof Vi&&"number"==typeof o.container.value&&0!==o.container.value&&(t=o.container.value),o.listValue=Xs(t,o.container.styles.listStyleType,!0),t+=r?-1:1}},NB=function(A){var e=new GB(A,null),t=new kB(e),r=[];return PB(e,t,t,r),VB(e.container,r),t},_B=function(A,e){switch(e){case 0:return WB(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return WB(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return WB(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return WB(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},JB=function(A,e){switch(e){case 0:return WB(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return WB(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return WB(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return WB(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}},XB=function(A,e){switch(e){case 0:return WB(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return WB(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return WB(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return WB(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}},jB=function(A,e){switch(e){case 0:return YB(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return YB(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return YB(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return YB(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}},YB=function(A,e){var t=[];return HB(A)?t.push(A.subdivide(.5,!1)):t.push(A),HB(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},WB=function(A,e,t,r){var n=[];return HB(A)?n.push(A.subdivide(.5,!1)):n.push(A),HB(t)?n.push(t.subdivide(.5,!0)):n.push(t),HB(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),HB(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},ZB=function(A){var e=A.bounds,t=A.styles;return e.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},zB=function(A){var e=A.styles,t=A.bounds,r=Xt(e.paddingLeft,t.width),n=Xt(e.paddingRight,t.width),o=Xt(e.paddingTop,t.width),i=Xt(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,o+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+o+i))},qB=function(A,e){return 0===A?e.bounds:2===A?zB(e):ZB(e)},$B=function(A,e){return 0===A?e.bounds:2===A?zB(e):ZB(e)},Aa=function(A,e,t){var r=qB(na(A.styles.backgroundOrigin,e),A),n=$B(na(A.styles.backgroundClip,e),A),o=ra(na(A.styles.backgroundSize,e),t,r),i=o[0],s=o[1],B=Jt(na(A.styles.backgroundPosition,e),r.width-i,r.height-s);return[oa(na(A.styles.backgroundRepeat,e),B,o,r,n),Math.round(r.left+B[0]),Math.round(r.top+B[1]),i,s]},ea=function(A){return Lt(A)&&A.value===Or.AUTO},ta=function(A){return"number"==typeof A},ra=function(A,e,t){var r=e[0],n=e[1],o=e[2],i=A[0],s=A[1];if(!i)return[0,0];if(Gt(i)&&s&&Gt(s))return[Xt(i,t.width),Xt(s,t.height)];var B=ta(o);if(Lt(i)&&(i.value===Or.CONTAIN||i.value===Or.COVER))return ta(o)?t.width/t.height<o!=(i.value===Or.COVER)?[t.width,t.width/o]:[t.height*o,t.height]:[t.width,t.height];var a=ta(r),c=ta(n),u=a||c;if(ea(i)&&(!s||ea(s)))return a&&c?[r,n]:B||u?u&&B?[a?r:n*o,c?n:r/o]:[a?r:t.width,c?n:t.height]:[t.width,t.height];if(B){var l=0,g=0;return Gt(i)?l=Xt(i,t.width):Gt(s)&&(g=Xt(s,t.height)),ea(i)?l=g*o:s&&!ea(s)||(g=l/o),[l,g]}var f=null,w=null;if(Gt(i)?f=Xt(i,t.width):s&&Gt(s)&&(w=Xt(s,t.height)),null===f||s&&!ea(s)||(w=a&&c?f/r*n:t.height),null!==w&&ea(i)&&(f=a&&c?w/n*r:t.width),null!==f&&null!==w)return[f,w];throw new Error("Unable to calculate background-size for element")},na=function(A,e){var t=A[e];return void 0===t?A[0]:t},oa=function(A,e,t,r,n){var o=e[0],i=e[1],s=t[0],B=t[1];switch(A){case 2:return[new FB(Math.round(r.left),Math.round(r.top+i)),new FB(Math.round(r.left+r.width),Math.round(r.top+i)),new FB(Math.round(r.left+r.width),Math.round(B+r.top+i)),new FB(Math.round(r.left),Math.round(B+r.top+i))];case 3:return[new FB(Math.round(r.left+o),Math.round(r.top)),new FB(Math.round(r.left+o+s),Math.round(r.top)),new FB(Math.round(r.left+o+s),Math.round(r.height+r.top)),new FB(Math.round(r.left+o),Math.round(r.height+r.top))];case 1:return[new FB(Math.round(r.left+o),Math.round(r.top+i)),new FB(Math.round(r.left+o+s),Math.round(r.top+i)),new FB(Math.round(r.left+o+s),Math.round(r.top+i+B)),new FB(Math.round(r.left+o),Math.round(r.top+i+B))];default:return[new FB(Math.round(n.left),Math.round(n.top)),new FB(Math.round(n.left+n.width),Math.round(n.top)),new FB(Math.round(n.left+n.width),Math.round(n.height+n.top)),new FB(Math.round(n.left),Math.round(n.height+n.top))]}},ia="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",sa="Hidden Text",Ba=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),o=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",o.appendChild(t),r.src=ia,r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(sa)),t.appendChild(n),t.appendChild(r);var i=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(sa)),t.style.lineHeight="normal",r.style.verticalAlign="super";var s=r.offsetTop-t.offsetTop+2;return o.removeChild(t),{baseline:i,middle:s}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),aa=function(){function A(A,e){this.context=A,this.options=e}return A}(),ca=1e4,ua=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r._activeEffects=[],r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),t.canvas||(r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px"),r.fontMetrics=new Ba(document),r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.ctx.textBaseline="bottom",r._activeEffects=[],r.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),r}return e(t,A),t.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach((function(A){return e.applyEffect(A)}))},t.prototype.applyEffect=function(A){this.ctx.save(),TB(A)&&(this.ctx.globalAlpha=A.opacity),SB(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),OB(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},t.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},t.prototype.renderStack=function(A){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return A.element.container.styles.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},t.prototype.renderNode=function(A){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return no(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},t.prototype.renderTextWithLetterSpacing=function(A,e,t){var r=this;0===e?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+t):Ki(A.text).reduce((function(e,n){return r.ctx.fillText(n,e,A.bounds.top+t),e+r.ctx.measureText(n).width}),A.bounds.left)},t.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=Qa(A.fontFamily).join(", "),r=Kt(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},t.prototype.renderTextNode=function(A,e){return r(this,void 0,void 0,(function(){var t,r,o,i,s,B,a,c,u=this;return n(this,(function(n){return t=this.createFontStyle(e),r=t[0],o=t[1],i=t[2],this.ctx.font=r,this.ctx.direction=1===e.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",s=this.fontMetrics.getMetrics(o,i),B=s.baseline,a=s.middle,c=e.paintOrder,A.textBounds.forEach((function(A){c.forEach((function(t){switch(t){case 0:u.ctx.fillStyle=rr(e.color),u.renderTextWithLetterSpacing(A,e.letterSpacing,B);var r=e.textShadow;r.length&&A.text.trim().length&&(r.slice(0).reverse().forEach((function(t){u.ctx.shadowColor=rr(t.color),u.ctx.shadowOffsetX=t.offsetX.number*u.options.scale,u.ctx.shadowOffsetY=t.offsetY.number*u.options.scale,u.ctx.shadowBlur=t.blur.number,u.renderTextWithLetterSpacing(A,e.letterSpacing,B)})),u.ctx.shadowColor="",u.ctx.shadowOffsetX=0,u.ctx.shadowOffsetY=0,u.ctx.shadowBlur=0),e.textDecorationLine.length&&(u.ctx.fillStyle=rr(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:u.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+B),A.bounds.width,1);break;case 2:u.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:u.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+a),A.bounds.width,1)}})));break;case 1:e.webkitTextStrokeWidth&&A.text.trim().length&&(u.ctx.strokeStyle=rr(e.webkitTextStrokeColor),u.ctx.lineWidth=e.webkitTextStrokeWidth,u.ctx.lineJoin=window.chrome?"miter":"round",u.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+B)),u.ctx.strokeStyle="",u.ctx.lineWidth=0,u.ctx.lineJoin="miter"}}))})),[2]}))}))},t.prototype.renderReplacedElement=function(A,e,t){if(t&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var r=zB(A),n=KB(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},t.prototype.renderNodeContent=function(A){return r(this,void 0,void 0,(function(){var e,r,o,s,B,a,c,u,l,g,f,w,Q,h,p,C,U,d;return n(this,(function(n){switch(n.label){case 0:this.applyEffects(A.getEffects(4)),e=A.container,r=A.curves,o=e.styles,s=0,B=e.textNodes,n.label=1;case 1:return s<B.length?(a=B[s],[4,this.renderTextNode(a,o)]):[3,4];case 2:n.sent(),n.label=3;case 3:return s++,[3,1];case 4:if(!(e instanceof ki))return[3,8];n.label=5;case 5:return n.trys.push([5,7,,8]),[4,this.context.cache.match(e.src)];case 6:return p=n.sent(),this.renderReplacedElement(e,r,p),[3,8];case 7:return n.sent(),this.context.logger.error("Error loading image "+e.src),[3,8];case 8:if(e instanceof Gi&&this.renderReplacedElement(e,r,e.canvas),!(e instanceof Pi))return[3,12];n.label=9;case 9:return n.trys.push([9,11,,12]),[4,this.context.cache.match(e.svg)];case 10:return p=n.sent(),this.renderReplacedElement(e,r,p),[3,12];case 11:return n.sent(),this.context.logger.error("Error loading svg "+e.svg.substring(0,255)),[3,12];case 12:return e instanceof es&&e.tree?[4,new t(this.context,{scale:this.options.scale,backgroundColor:e.backgroundColor,x:0,y:0,width:e.width,height:e.height}).render(e.tree)]:[3,14];case 13:c=n.sent(),e.width&&e.height&&this.ctx.drawImage(c,0,0,e.width,e.height,e.bounds.left,e.bounds.top,e.bounds.width,e.bounds.height),n.label=14;case 14:if(e instanceof qi&&(u=Math.min(e.bounds.width,e.bounds.height),e.type===Yi?e.checked&&(this.ctx.save(),this.path([new FB(e.bounds.left+.39363*u,e.bounds.top+.79*u),new FB(e.bounds.left+.16*u,e.bounds.top+.5549*u),new FB(e.bounds.left+.27347*u,e.bounds.top+.44071*u),new FB(e.bounds.left+.39694*u,e.bounds.top+.5649*u),new FB(e.bounds.left+.72983*u,e.bounds.top+.23*u),new FB(e.bounds.left+.84*u,e.bounds.top+.34085*u),new FB(e.bounds.left+.39363*u,e.bounds.top+.79*u)]),this.ctx.fillStyle=rr(zi),this.ctx.fill(),this.ctx.restore()):e.type===Wi&&e.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(e.bounds.left+u/2,e.bounds.top+u/2,u/4,0,2*Math.PI,!0),this.ctx.fillStyle=rr(zi),this.ctx.fill(),this.ctx.restore())),la(e)&&e.value.length){switch(l=this.createFontStyle(o),U=l[0],g=l[1],f=this.fontMetrics.getMetrics(U,g).baseline,this.ctx.font=U,this.ctx.fillStyle=rr(o.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=fa(e.styles.textAlign),d=zB(e),w=0,e.styles.textAlign){case 1:w+=d.width/2;break;case 2:w+=d.width}Q=d.add(w,0,0,-d.height/2+1),this.ctx.save(),this.path([new FB(d.left,d.top),new FB(d.left+d.width,d.top),new FB(d.left+d.width,d.top+d.height),new FB(d.left,d.top+d.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new vi(e.value,Q),o.letterSpacing,f),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!no(e.styles.display,2048))return[3,20];if(null===e.styles.listStyleImage)return[3,19];if(0!==(h=e.styles.listStyleImage).type)return[3,18];p=void 0,C=h.url,n.label=15;case 15:return n.trys.push([15,17,,18]),[4,this.context.cache.match(C)];case 16:return p=n.sent(),this.ctx.drawImage(p,e.bounds.left-(p.width+10),e.bounds.top),[3,18];case 17:return n.sent(),this.context.logger.error("Error loading list-style-image "+C),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==e.styles.listStyleType&&(U=this.createFontStyle(o)[0],this.ctx.font=U,this.ctx.fillStyle=rr(o.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",d=new i(e.bounds.left,e.bounds.top+Xt(e.styles.paddingTop,e.bounds.width),e.bounds.width,dn(o.lineHeight,o.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new vi(A.listValue,d),o.letterSpacing,dn(o.lineHeight,o.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),n.label=20;case 20:return[2]}}))}))},t.prototype.renderStackContent=function(A){return r(this,void 0,void 0,(function(){var e,t,r,o,i,s,B,a,c,u,l,g,f,w,Q;return n(this,(function(n){switch(n.label){case 0:return no(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:n.sent(),e=0,t=A.negativeZIndex,n.label=2;case 2:return e<t.length?(Q=t[e],[4,this.renderStack(Q)]):[3,5];case 3:n.sent(),n.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:n.sent(),r=0,o=A.nonInlineLevel,n.label=7;case 7:return r<o.length?(Q=o[r],[4,this.renderNode(Q)]):[3,10];case 8:n.sent(),n.label=9;case 9:return r++,[3,7];case 10:i=0,s=A.nonPositionedFloats,n.label=11;case 11:return i<s.length?(Q=s[i],[4,this.renderStack(Q)]):[3,14];case 12:n.sent(),n.label=13;case 13:return i++,[3,11];case 14:B=0,a=A.nonPositionedInlineLevel,n.label=15;case 15:return B<a.length?(Q=a[B],[4,this.renderStack(Q)]):[3,18];case 16:n.sent(),n.label=17;case 17:return B++,[3,15];case 18:c=0,u=A.inlineLevel,n.label=19;case 19:return c<u.length?(Q=u[c],[4,this.renderNode(Q)]):[3,22];case 20:n.sent(),n.label=21;case 21:return c++,[3,19];case 22:l=0,g=A.zeroOrAutoZIndexOrTransformedOrOpacity,n.label=23;case 23:return l<g.length?(Q=g[l],[4,this.renderStack(Q)]):[3,26];case 24:n.sent(),n.label=25;case 25:return l++,[3,23];case 26:f=0,w=A.positiveZIndex,n.label=27;case 27:return f<w.length?(Q=w[f],[4,this.renderStack(Q)]):[3,30];case 28:n.sent(),n.label=29;case 29:return f++,[3,27];case 30:return[2]}}))}))},t.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},t.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},t.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var r=HB(A)?A.start:A;0===t?e.ctx.moveTo(r.x,r.y):e.ctx.lineTo(r.x,r.y),HB(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},t.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},t.prototype.resizeImage=function(A,e,t){var r;if(A.width===e&&A.height===t)return A;var n=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return n.width=Math.max(1,e),n.height=Math.max(1,t),n.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),n},t.prototype.renderBackgroundImage=function(A){return r(this,void 0,void 0,(function(){var e,t,r,o,i,s;return n(this,(function(B){switch(B.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var o,i,s,B,a,c,u,l,g,f,w,Q,h,p,C,U,d,F,y,E,H,v,m,I,b,K,x,L,D,S,O;return n(this,(function(n){switch(n.label){case 0:if(0!==t.type)return[3,5];o=void 0,i=t.url,n.label=1;case 1:return n.trys.push([1,3,,4]),[4,r.context.cache.match(i)];case 2:return o=n.sent(),[3,4];case 3:return n.sent(),r.context.logger.error("Error loading background-image "+i),[3,4];case 4:return o&&(s=Aa(A,e,[o.width,o.height,o.width/o.height]),U=s[0],v=s[1],m=s[2],y=s[3],E=s[4],p=r.ctx.createPattern(r.resizeImage(o,y,E),"repeat"),r.renderRepeat(U,p,v,m)),[3,6];case 5:xr(t)?(B=Aa(A,e,[null,null,null]),U=B[0],v=B[1],m=B[2],y=B[3],E=B[4],a=hr(t.angle,y,E),c=a[0],u=a[1],l=a[2],g=a[3],f=a[4],(w=document.createElement("canvas")).width=y,w.height=E,Q=w.getContext("2d"),h=Q.createLinearGradient(u,g,l,f),wr(t.stops,c).forEach((function(A){return h.addColorStop(A.stop,rr(A.color))})),Q.fillStyle=h,Q.fillRect(0,0,y,E),y>0&&E>0&&(p=r.ctx.createPattern(w,"repeat"),r.renderRepeat(U,p,v,m))):Lr(t)&&(C=Aa(A,e,[null,null,null]),U=C[0],d=C[1],F=C[2],y=C[3],E=C[4],H=0===t.position.length?[Nt]:t.position,v=Xt(H[0],y),m=Xt(H[H.length-1],E),I=Ur(t,v,m,y,E),b=I[0],K=I[1],b>0&&K>0&&(x=r.ctx.createRadialGradient(d+v,F+m,0,d+v,F+m,b),wr(t.stops,2*b).forEach((function(A){return x.addColorStop(A.stop,rr(A.color))})),r.path(U),r.ctx.fillStyle=x,b!==K?(L=A.bounds.left+.5*A.bounds.width,D=A.bounds.top+.5*A.bounds.height,O=1/(S=K/b),r.ctx.save(),r.ctx.translate(L,D),r.ctx.transform(1,0,0,S,0,0),r.ctx.translate(-L,-D),r.ctx.fillRect(d,O*(F-D)+D,y,E*O),r.ctx.restore()):r.ctx.fill())),n.label=6;case 6:return e--,[2]}}))},r=this,o=0,i=A.styles.backgroundImage.slice(0).reverse(),B.label=1;case 1:return o<i.length?(s=i[o],[5,t(s)]):[3,4];case 2:B.sent(),B.label=3;case 3:return o++,[3,1];case 4:return[2]}}))}))},t.prototype.renderSolidBorder=function(A,e,t){return r(this,void 0,void 0,(function(){return n(this,(function(r){return this.path(_B(t,e)),this.ctx.fillStyle=rr(A),this.ctx.fill(),[2]}))}))},t.prototype.renderDoubleBorder=function(A,e,t,o){return r(this,void 0,void 0,(function(){var r,i;return n(this,(function(n){switch(n.label){case 0:return e<3?[4,this.renderSolidBorder(A,t,o)]:[3,2];case 1:return n.sent(),[2];case 2:return r=JB(o,t),this.path(r),this.ctx.fillStyle=rr(A),this.ctx.fill(),i=XB(o,t),this.path(i),this.ctx.fill(),[2]}}))}))},t.prototype.renderNodeBackgroundAndBorders=function(A){return r(this,void 0,void 0,(function(){var e,t,r,o,i,s,B,a,c=this;return n(this,(function(n){switch(n.label){case 0:return this.applyEffects(A.getEffects(2)),e=A.container.styles,t=!tr(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],o=ga(na(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(o),this.ctx.clip(),tr(e.backgroundColor)||(this.ctx.fillStyle=rr(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:n.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){c.ctx.save();var t=IB(A.curves),r=e.inset?0:ca,n=RB(t,-r+(e.inset?1:-1)*e.spread.number,(e.inset?1:-1)*e.spread.number,e.spread.number*(e.inset?-2:2),e.spread.number*(e.inset?-2:2));e.inset?(c.path(t),c.ctx.clip(),c.mask(n)):(c.mask(t),c.ctx.clip(),c.path(n)),c.ctx.shadowOffsetX=e.offsetX.number+r,c.ctx.shadowOffsetY=e.offsetY.number,c.ctx.shadowColor=rr(e.color),c.ctx.shadowBlur=e.blur.number,c.ctx.fillStyle=e.inset?rr(e.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()})),n.label=2;case 2:i=0,s=0,B=r,n.label=3;case 3:return s<B.length?0!==(a=B[s]).style&&!tr(a.color)&&a.width>0?2!==a.style?[3,5]:[4,this.renderDashedDottedBorder(a.color,a.width,i,A.curves,2)]:[3,11]:[3,13];case 4:return n.sent(),[3,11];case 5:return 3!==a.style?[3,7]:[4,this.renderDashedDottedBorder(a.color,a.width,i,A.curves,3)];case 6:return n.sent(),[3,11];case 7:return 4!==a.style?[3,9]:[4,this.renderDoubleBorder(a.color,a.width,i,A.curves)];case 8:return n.sent(),[3,11];case 9:return[4,this.renderSolidBorder(a.color,i,A.curves)];case 10:n.sent(),n.label=11;case 11:i++,n.label=12;case 12:return s++,[3,3];case 13:return[2]}}))}))},t.prototype.renderDashedDottedBorder=function(A,e,t,o,i){return r(this,void 0,void 0,(function(){var r,s,B,a,c,u,l,g,f,w,Q,h,p,C,U,d;return n(this,(function(n){return this.ctx.save(),r=jB(o,t),s=_B(o,t),2===i&&(this.path(s),this.ctx.clip()),HB(s[0])?(B=s[0].start.x,a=s[0].start.y):(B=s[0].x,a=s[0].y),HB(s[1])?(c=s[1].end.x,u=s[1].end.y):(c=s[1].x,u=s[1].y),l=0===t||2===t?Math.abs(B-c):Math.abs(a-u),this.ctx.beginPath(),3===i?this.formatPath(r):this.formatPath(s.slice(0,2)),g=e<3?3*e:2*e,f=e<3?2*e:e,3===i&&(g=e,f=e),w=!0,l<=2*g?w=!1:l<=2*g+f?(g*=Q=l/(2*g+f),f*=Q):(h=Math.floor((l+f)/(g+f)),p=(l-h*g)/(h-1),f=(C=(l-(h+1)*g)/h)<=0||Math.abs(f-p)<Math.abs(f-C)?p:C),w&&(3===i?this.ctx.setLineDash([0,g+f]):this.ctx.setLineDash([g,f])),3===i?(this.ctx.lineCap="round",this.ctx.lineWidth=e):this.ctx.lineWidth=2*e+1.1,this.ctx.strokeStyle=rr(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===i&&(HB(s[0])&&(U=s[3],d=s[0],this.ctx.beginPath(),this.formatPath([new FB(U.end.x,U.end.y),new FB(d.start.x,d.start.y)]),this.ctx.stroke()),HB(s[1])&&(U=s[1],d=s[2],this.ctx.beginPath(),this.formatPath([new FB(U.end.x,U.end.y),new FB(d.start.x,d.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},t.prototype.render=function(A){return r(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=rr(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),e=NB(A),[4,this.renderStack(e)];case 1:return t.sent(),this.applyEffects([]),[2,this.canvas]}}))}))},t}(aa),la=function(A){return A instanceof As||A instanceof $i||A instanceof qi&&A.type!==Wi&&A.type!==Yi},ga=function(A,e){switch(A){case 0:return IB(e);case 2:return bB(e);default:return KB(e)}},fa=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},wa=["-apple-system","system-ui"],Qa=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===wa.indexOf(A)})):A},ha=function(A){function t(e,t){var r=A.call(this,e,t)||this;return r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),r.options=t,r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px",r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),r}return e(t,A),t.prototype.render=function(A){return r(this,void 0,void 0,(function(){var e,t;return n(this,(function(r){switch(r.label){case 0:return e=yi(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,pa(e)];case 1:return t=r.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=rr(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(t,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},t}(aa),pa=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Ca=function(){function A(A){var e=A.id,t=A.enabled;this.id=e,this.enabled=t,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,o([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,o([this.id,this.getTime()+"ms"],A))},A.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn?console.warn.apply(console,o([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,o([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.instances={},A}(),Ua=function(){function A(e,t){var r;this.windowBounds=t,this.instanceName="#"+A.instanceCount++,this.logger=new Ca({id:this.instanceName,enabled:e.logging}),this.cache=null!==(r=e.cache)&&void 0!==r?r:new gB(this,e)}return A.instanceCount=1,A}(),da=function(A,e){return void 0===e&&(e={}),Fa(A,e)};"undefined"!=typeof window&&lB.setContext(window);var Fa=function(A,e){return r(void 0,void 0,void 0,(function(){var r,o,a,c,u,l,g,f,w,Q,h,p,C,U,d,F,y,E,H,v,m,I,b,K,x,L,D,S,O,T,M,R,k,G,P,V,N,_;return n(this,(function(n){switch(n.label){case 0:if(!A||"object"!=typeof A)return[2,Promise.reject("Invalid element provided as first argument")];if(!(r=A.ownerDocument))throw new Error("Element is not attached to a Document");if(!(o=r.defaultView))throw new Error("Document is not attached to a Window");return a={allowTaint:null!==(I=e.allowTaint)&&void 0!==I&&I,imageTimeout:null!==(b=e.imageTimeout)&&void 0!==b?b:15e3,proxy:e.proxy,useCORS:null!==(K=e.useCORS)&&void 0!==K&&K},c=t({logging:null===(x=e.logging)||void 0===x||x,cache:e.cache},a),u={windowWidth:null!==(L=e.windowWidth)&&void 0!==L?L:o.innerWidth,windowHeight:null!==(D=e.windowHeight)&&void 0!==D?D:o.innerHeight,scrollX:null!==(S=e.scrollX)&&void 0!==S?S:o.pageXOffset,scrollY:null!==(O=e.scrollY)&&void 0!==O?O:o.pageYOffset},l=new i(u.scrollX,u.scrollY,u.windowWidth,u.windowHeight),g=new Ua(c,l),f=null!==(T=e.foreignObjectRendering)&&void 0!==T&&T,w={allowTaint:null!==(M=e.allowTaint)&&void 0!==M&&M,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:f,copyStyles:f},g.logger.debug("Starting document clone with size "+l.width+"x"+l.height+" scrolled to "+-l.left+","+-l.top),Q=new Ys(g,A,w),(h=Q.clonedReferenceElement)?[4,Q.toIFrame(r,l)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return p=n.sent(),C=hs(h)||ws(h)?B(h.ownerDocument):s(g,h),U=C.width,d=C.height,F=C.left,y=C.top,E=ya(g,h,e.backgroundColor),H={canvas:e.canvas,backgroundColor:E,scale:null!==(k=null!==(R=e.scale)&&void 0!==R?R:o.devicePixelRatio)&&void 0!==k?k:1,x:(null!==(G=e.x)&&void 0!==G?G:0)+F,y:(null!==(P=e.y)&&void 0!==P?P:0)+y,width:null!==(V=e.width)&&void 0!==V?V:Math.ceil(U),height:null!==(N=e.height)&&void 0!==N?N:Math.ceil(d)},f?(g.logger.debug("Document cloned, using foreign object rendering"),[4,new ha(g,H).render(h)]):[3,3];case 2:return v=n.sent(),[3,5];case 3:return g.logger.debug("Document cloned, element located at "+F+","+y+" with size "+U+"x"+d+" using computed rendering"),g.logger.debug("Starting DOM parsing"),m=os(g,h),E===m.styles.backgroundColor&&(m.styles.backgroundColor=ur.TRANSPARENT),g.logger.debug("Starting renderer for element at "+H.x+","+H.y+" with size "+H.width+"x"+H.height),[4,new ua(g,H).render(m)];case 4:v=n.sent(),n.label=5;case 5:return(null===(_=e.removeContainer)||void 0===_||_)&&(Ys.destroy(p)||g.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),g.logger.debug("Finished rendering"),[2,v]}}))}))},ya=function(A,e,t){var r=e.ownerDocument,n=r.documentElement?cr(A,getComputedStyle(r.documentElement).backgroundColor):ur.TRANSPARENT,o=r.body?cr(A,getComputedStyle(r.body).backgroundColor):ur.TRANSPARENT,i="string"==typeof t?cr(A,t):null===t?ur.TRANSPARENT:4294967295;return e===r.documentElement?tr(n)?tr(o)?i:o:n:i};return da}()},wTB4:function(A,e,t){"use strict";function r(A,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(A,r.key,r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.CookieStorage=void 0;var n=t("4Cfb"),o=t("LbSc"),i=function(){function A(e){if(function(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}(this,A),this._defaultOptions=Object.assign({domain:null,expires:null,path:null,secure:!1},e),"undefined"!=typeof Proxy)return new Proxy(this,s)}var e,t,i;return e=A,(t=[{key:"clear",value:function(){var A=this,e=o.parseCookies(this._getCookie());Object.keys(e).forEach((function(e){return A.removeItem(e)}))}},{key:"getItem",value:function(A){var e=o.parseCookies(this._getCookie());return Object.prototype.hasOwnProperty.call(e,A)?e[A]:null}},{key:"key",value:function(A){var e=o.parseCookies(this._getCookie()),t=Object.keys(e).sort();return A<t.length?t[A]:null}},{key:"removeItem",value:function(A,e){var t=Object.assign(Object.assign(Object.assign({},this._defaultOptions),e),{expires:new Date(0)}),r=n.formatCookie(A,"",t);this._setCookie(r)}},{key:"setItem",value:function(A,e,t){var r=Object.assign(Object.assign({},this._defaultOptions),t),o=n.formatCookie(A,e,r);this._setCookie(o)}},{key:"_getCookie",value:function(){return"undefined"==typeof document||void 0===document.cookie?"":document.cookie}},{key:"_setCookie",value:function(A){document.cookie=A}},{key:"length",get:function(){var A=o.parseCookies(this._getCookie());return Object.keys(A).length}}])&&r(e.prototype,t),i&&r(e,i),A}();e.CookieStorage=i;var s={defineProperty:function(A,e,t){return A.setItem(e.toString(),String(t.value)),!0},deleteProperty:function(A,e){return A.removeItem(e.toString()),!0},get:function(A,e,t){if("string"==typeof e&&e in A)return A[e];var r=A.getItem(e.toString());return null!==r?r:void 0},getOwnPropertyDescriptor:function(A,e){if(!(e in A))return{configurable:!0,enumerable:!0,value:A.getItem(e.toString()),writable:!0}},has:function(A,e){return"string"==typeof e&&e in A||null!==A.getItem(e.toString())},ownKeys:function(A){for(var e=[],t=0;t<A.length;t++){var r=A.key(t);null!==r&&e.push(r)}return e},preventExtensions:function(A){throw new TypeError("can't prevent extensions on this proxy object")},set:function(A,e,t,r){return A.setItem(e.toString(),String(t)),!0}}},wclG:function(A,e,t){var r=t("pFRH"),n=t("88Gu")(r);A.exports=n},wrZu:function(A,e,t){var r=t("+K+b"),n=t("XYm9"),o=t("b2z7"),i=t("otv/"),s=t("yP5f");A.exports=function(A,e,t){var B=A.constructor;switch(e){case"[object ArrayBuffer]":return r(A);case"[object Boolean]":case"[object Date]":return new B(+A);case"[object DataView]":return n(A,t);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return s(A,t);case"[object Map]":case"[object Set]":return new B;case"[object Number]":case"[object String]":return new B(A);case"[object RegExp]":return o(A);case"[object Symbol]":return i(A)}}},xAGQ:function(A,e,t){"use strict";var r=t("xTJ+"),n=t("TD3H");A.exports=function(A,e,t){var o=this||n;return r.forEach(t,(function(t){A=t.call(o,A,e)})),A}},"xTJ+":function(A,e,t){"use strict";var r,n=t("HSsa"),o=Object.prototype.toString,i=(r=Object.create(null),function(A){var e=o.call(A);return r[e]||(r[e]=e.slice(8,-1).toLowerCase())});function s(A){return A=A.toLowerCase(),function(e){return i(e)===A}}function B(A){return Array.isArray(A)}function a(A){return void 0===A}var c=s("ArrayBuffer");function u(A){return null!==A&&"object"==typeof A}function l(A){if("object"!==i(A))return!1;var e=Object.getPrototypeOf(A);return null===e||e===Object.prototype}var g=s("Date"),f=s("File"),w=s("Blob"),Q=s("FileList");function h(A){return"[object Function]"===o.call(A)}var p=s("URLSearchParams");function C(A,e){if(null!=A)if("object"!=typeof A&&(A=[A]),B(A))for(var t=0,r=A.length;t<r;t++)e.call(null,A[t],t,A);else for(var n in A)Object.prototype.hasOwnProperty.call(A,n)&&e.call(null,A[n],n,A)}var U,d=(U="undefined"!=typeof Uint8Array&&Object.getPrototypeOf(Uint8Array),function(A){return U&&A instanceof U});A.exports={isArray:B,isArrayBuffer:c,isBuffer:function(A){return null!==A&&!a(A)&&null!==A.constructor&&!a(A.constructor)&&"function"==typeof A.constructor.isBuffer&&A.constructor.isBuffer(A)},isFormData:function(A){var e="[object FormData]";return A&&("function"==typeof FormData&&A instanceof FormData||o.call(A)===e||h(A.toString)&&A.toString()===e)},isArrayBufferView:function(A){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(A):A&&A.buffer&&c(A.buffer)},isString:function(A){return"string"==typeof A},isNumber:function(A){return"number"==typeof A},isObject:u,isPlainObject:l,isUndefined:a,isDate:g,isFile:f,isBlob:w,isFunction:h,isStream:function(A){return u(A)&&h(A.pipe)},isURLSearchParams:p,isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:C,merge:function A(){var e={};function t(t,r){l(e[r])&&l(t)?e[r]=A(e[r],t):l(t)?e[r]=A({},t):B(t)?e[r]=t.slice():e[r]=t}for(var r=0,n=arguments.length;r<n;r++)C(arguments[r],t);return e},extend:function(A,e,t){return C(e,(function(e,r){A[r]=t&&"function"==typeof e?n(e,t):e})),A},trim:function(A){return A.trim?A.trim():A.replace(/^\s+|\s+$/g,"")},stripBOM:function(A){return 65279===A.charCodeAt(0)&&(A=A.slice(1)),A},inherits:function(A,e,t,r){A.prototype=Object.create(e.prototype,r),A.prototype.constructor=A,t&&Object.assign(A.prototype,t)},toFlatObject:function(A,e,t){var r,n,o,i={};e=e||{};do{for(n=(r=Object.getOwnPropertyNames(A)).length;n-- >0;)i[o=r[n]]||(e[o]=A[o],i[o]=!0);A=Object.getPrototypeOf(A)}while(A&&(!t||t(A,e))&&A!==Object.prototype);return e},kindOf:i,kindOfTest:s,endsWith:function(A,e,t){A=String(A),(void 0===t||t>A.length)&&(t=A.length),t-=e.length;var r=A.indexOf(e,t);return-1!==r&&r===t},toArray:function(A){if(!A)return null;var e=A.length;if(a(e))return null;for(var t=new Array(e);e-- >0;)t[e]=A[e];return t},isTypedArray:d,isFileList:Q}},xYSL:function(A,e){A.exports=function(A,e){return A.has(e)}},"xs/l":function(A,e,t){var r=t("TYy9"),n=t("Ioao"),o=t("wclG");A.exports=function(A){return o(n(A,void 0,r),A+"")}},y1pI:function(A,e,t){var r=t("ljhN");A.exports=function(A,e){for(var t=A.length;t--;)if(r(A[t][0],e))return t;return-1}},yGk4:function(A,e,t){var r=t("Cwc5")(t("Kz5y"),"Set");A.exports=r},yHx3:function(A,e){var t=Object.prototype.hasOwnProperty;A.exports=function(A){var e=A.length,r=new A.constructor(e);return e&&"string"==typeof A[0]&&t.call(A,"index")&&(r.index=A.index,r.input=A.input),r}},yK9s:function(A,e,t){"use strict";var r=t("xTJ+");A.exports=function(A,e){r.forEach(A,(function(t,r){r!==e&&r.toUpperCase()===e.toUpperCase()&&(A[e]=t,delete A[r])}))}},yLpj:function(A,e){var t;t=function(){return this}();try{t=t||new Function("return this")()}catch(A){"object"==typeof window&&(t=window)}A.exports=t},yP5f:function(A,e,t){var r=t("+K+b");A.exports=function(A,e){var t=e?r(A.buffer):A.buffer;return new A.constructor(t,A.byteOffset,A.length)}},"yvr/":function(A,e,t){"use strict";A.exports={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},zEVN:function(A,e,t){var r=t("Gi0A"),n=t("sEf8"),o=t("mdPL"),i=o&&o.isMap,s=i?n(i):r;A.exports=s},zZ0H:function(A,e){A.exports=function(A){return A}},zoYe:function(A,e,t){var r=t("nmnc"),n=t("eUgh"),o=t("Z0cm"),i=t("/9aa"),s=r?r.prototype:void 0,B=s?s.toString:void 0;A.exports=function A(e){if("string"==typeof e)return e;if(o(e))return n(e,A)+"";if(i(e))return B?B.call(e):"";var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t}},zuR4:function(A,e,t){"use strict";var r=t("xTJ+"),n=t("HSsa"),o=t("CgaS"),i=t("SntB");var s=function A(e){var t=new o(e),s=n(o.prototype.request,t);return r.extend(s,o.prototype,t),r.extend(s,t),s.create=function(t){return A(i(e,t))},s}(t("TD3H"));s.Axios=o,s.CanceledError=t("+2B0"),s.CancelToken=t("jfS+"),s.isCancel=t("Lmem"),s.VERSION=t("XM5P").version,s.toFormData=t("5GeT"),s.AxiosError=t("eRe6"),s.Cancel=s.CanceledError,s.all=function(A){return Promise.all(A)},s.spread=t("DfZB"),s.isAxiosError=t("XwJu"),A.exports=s,A.exports.default=s}});
//# sourceMappingURL=23761977932751c1aabf.worker.js.map