{"locale": {"en-AU": "<PERSON><PERSON><PERSON> (AU)"}, "sw-privileges": {"permissions": {"parents": {"swag_paypal": "PayPal"}, "swag_paypal": {"label": "PayPal-Einstellungen"}, "swag_paypal_disputes": {"label": "PayPal Konflikte"}}}, "sw-settings-payment-list": {"needOnboardingTooltip": "Schließen Sie das Onboarding ab, um diese Zahlungsmethode zu aktivieren"}, "sw-settings-payment-detail": {"tooltip": "Gehen Sie zu den <a class=\"swag-paypal-go-to-settings-link\" href=\"#/swag/paypal/index\">Einstellungen der PayPal-Erweiterung →</a> und aktivieren Sie PayPal Commerce Platform, um diese Zahlungsmethode zu aktivieren."}, "swag-paypal": {"header": "PayPal", "saleschannelCard": {"button": {"description": "<PERSON><PERSON><PERSON>, um PayPal im ausgewählten Sales Channel zu aktivieren und als Standardzahlungsart zu setzen", "label": "Setze PayPal als Standard"}}, "tabs": {"general": "Allgemein", "storefront": "Storefront-Darstellung"}, "general": {"mainMenuItemGeneral": "PayPal", "descriptionTextModule": "Einstellungen für PayPal"}, "webhook": {"cardTitle": "Webhook", "buttonRefresh": "Webhook aktualisieren", "refreshFailed": {"title": "Aktualisierung ist fehlgeschlagen", "errorUnknown": "Unbekannter Fehler"}, "status": {"unknown": "Laden...", "missing": "<PERSON><PERSON><PERSON>", "invalid": "Regis<PERSON>ert, aber mit abweichender Domain", "valid": "<PERSON><PERSON><PERSON><PERSON>"}, "infoText": "Der Webhook dient dazu, den Status einer Transaktion asynchron zu aktualisieren. Falsch registrierte Webhooks, z.B. auf einem anderen oder nicht öffentlich zugänglichen Server, können zu unbestätigten Transaktionen führen, un<PERSON><PERSON><PERSON><PERSON><PERSON>von, ob die Transaktion von PayPal verarbeitet wurde."}, "cross-border": {"cardTitle": "Später bezahlen - Länderübergreifende Nachrichten", "infoText": "Mit länderübergreifende Nachrichten für \"Später bezahlen\" wird die \"Später bezahlen\"-Nachricht in der Sprache des Kunden anzeigt. Diese Funktion ist nur für bestimmte Länder verfügbar. {0}", "warningText": "Bitte wenden Si<PERSON> sich an Ihren PayPal-Vertreter, um diese Funktion für Ihr Konto zu aktivieren.", "crossBorderBuyerCountryOverride": "Lokalisierung", "crossBorderBuyerCountryAuto": "Automatische Bestimmung", "crossBorderMessagingEnable": "Länderübergreifende Lokalisierung der \"Später bezahlen\"-Nachricht aktivieren"}, "messageNotBlank": "<PERSON>ser Wert darf nicht leer sein.", "settingForm": {"testLive": "API-Zugangsdaten testen", "testSandbox": "Sandbox-API-Zugangsdaten testen", "checkout": {"cardTitle": "PayPal Checkout", "settingsLink": "Einstellungen", "header": "Mehr Zahlungsmöglichkeiten in einer Lösung von PayPal", "description": "PayPal Checkout ist die neue All-in-One-L<PERSON><PERSON>g, mit der Sie leistungsstarke und flexible Funktionen zur Zahlungsabwicklung auf Marktplätzen und anderen Handelsplattformen anbieten können.", "banner": "Mit PayPal Checkout Kannst Du Deinen Kunden die Bezahlung per <PERSON><PERSON><PERSON><PERSON>, Kreditkarte und viele andere lokale Zahlungsmethoden anbieten. PayPal Checkout unterstützt Dich mit der neuesten Technologie und bringt Dir höchste Flexibilität. Du behältst Deine bisherigen Zahlungsarten und die Gebühren bleiben gleich! Aktiviere PayPal Checkout und deaktiviere PayPal PLUS, um doppelte Zahlungsmethoden in Deinem Shop zu vermeiden. <a href=\"https://www.shopware.com/de/news/paypal-launcht-neues-produkt/?utm_source=product&utm_medium=sw6-onprem&utm_campaign=paypal-checkout&utm_content=link-de-blog\" target=\"_blank\">Mehr Informationen</a>", "button": {"liveTitle": "PayPal-Konto verbinden", "sandboxTitle": "PayPal-Sandbox-Konto verbinden", "changeLiveTitle": "Anderes Konto verbinden", "changeSandboxTitle": "Anderes Sandbox-Konto verbinden", "onboardingLiveTitle": "PayPal Checkout-Onboarding starten", "onboardingSandboxTitle": "PayPal Checkout-Sandbox-Onboarding starten", "restartOnboardingLiveTitle": "PayPal Checkout-Onboarding wiederholen", "restartOnboardingSandboxTitle": "PayPal Checkout-Sandbox-Onboarding wiederholen"}, "sandbox": {"label": "Sandbox-Zugangsdaten verwenden", "onlySandboxTooltip": "Du bist bisher nur mit einem PayPal-Sandbox-Konto verbunden.", "onlyLiveTooltip": "Du bist bisher nur mit einem PayPal-Live-Konto verbunden.", "helpText": "Aktiviere diese Option, um die Integration zu testen."}, "switch": {"label": "Aktiv"}, "paymentMethodText": "Zahlungsmöglichkeiten", "showCredentials": "Zeige API-Zugangsdaten", "appImageAlt": "PayPal", "editDetail": "Details bearbeiten", "startOnboardingButtonLabel": "Onboarding starten", "availabilityToolTip": {"bancontactapmhandler": "Bancontact ist für die folgenden Länder verfügbar: Belgien", "blikapmhandler": "BLIK ist für die folgenden Länder verfügbar: Polen", "boletobancarioapmhandler": "<PERSON><PERSON><PERSON> ist für die folgenden Länder verfügbar: Brasilien", "epsapmhandler": "eps ist für die folgenden Länder verfügbar: Österreich", "idealapmhandler": "iDEAL ist für die folgenden Länder verfügbar: Niederlande", "multibancoapmhandler": "Multibanco ist für die folgenden Länder verfügbar: Portugal", "mybankapmhandler": "MyBank ist für die folgenden Länder verfügbar: Italien", "oxxoapmhandler": "OXXO ist für die folgenden Länder verfügbar: Mexiko", "p24apmhandler": "Przelewy24 ist für die folgenden Länder verfügbar: Polen", "paylaterhandler": "Später Bezahlen ist für die folgenden Länder verfügbar: Australien, Deutschland, Frankreich, Italien, Spanien, Vereinigte Staaten, Vereinigtes Königreich", "puihandler": "Rechnungskauf ist für die folgenden Länder verfügbar: Deutschland", "sepahandler": "SEPA Lastschrift ist für die folgenden Länder verfügbar: Deutschland", "venmohandler": "Venmo ist für die folgenden Länder verfügbar: USA", "trustlyapmhandler": "Trustly ist für die folgenden Länder verfügbar: Estland, Finnland, Niederlande, Schweden"}, "onboardingStatusText": {"active": "Autorisiert", "limited": "<PERSON><PERSON><PERSON>", "pending": "Authorisierung ausstehend", "ineligible": "<PERSON><PERSON><PERSON><PERSON>", "inactive": "Onboarding <PERSON><PERSON><PERSON><PERSON>", "mybank": "<PERSON><PERSON><PERSON>"}, "onboardingStatusTooltip": {"ineligible": "PayPal hat uns informiert, dass diese Zahlungsmethode aktuell für Ihren Account nicht freigeschaltet ist.", "limited": "PayPal hat uns informiert, dass Einschränkugen dieser Zahlungsart für Ihren Account bestehen.", "mybank": "<PERSON><PERSON><PERSON><PERSON>, die MyBank nach Februar 2023 aktivieren, ben<PERSON>ti<PERSON> eine manuelle Genehmigung von PayPal. Wenden Si<PERSON> sich an den PayPal-Support, um weitere Informationen zu erhalten."}, "paymentMethodStatusChangedSuccess": {"active": "Zahlungsart \"{name}\" ist jetzt aktiv.", "inactive": "Zahlungsart \"{name}\" ist jetzt inaktiv."}, "deactivatePayPalPlusModal": {"title": "PayPal PLUS deaktivieren", "text": "Herzlichen Glückwunsch! Durch den Wechsel zur PayPal-Kaufabwicklung nutzen Sie weiterhin alle relevanten Zahlungsmethoden - jetzt auf Basis der neuesten Technologie.", "warning": "Vermeide die Anzeige von doppelten Zahlungsarten und deaktiviere PayPal PLUS.", "button": {"deactivate": "Deaktivieren Sie PayPal PLUS", "cancel": "Abbrechen"}}}, "credentials": {"cardTitle": "API-Zugangsdaten", "clientId": {"label": "Client-ID", "tooltipText": "Die Client-ID der REST-API, die das Plugin dazu verwendet, sich mit der PayPal-API zu authentifizieren."}, "clientSecret": {"label": "Client-Secret", "tooltipText": "Das Client-Secret der REST-API, das das Plugin dazu verwendet, sich mit der PayPal-API zu authentifizieren."}, "merchantPayerId": {"label": "PayPal-Händler-ID", "tooltipText": "Die PayPal-Händler-ID, die dem PayPal-Konto zugeordnet ist (siehe Geschäftsangaben in den PayPal Kontoeinstellungen)."}, "clientIdSandbox": {"label": "Sandbox-Client-ID", "tooltipText": "Die Client-ID der REST-API, die das Plugin im Testfall dazu verwendet, sich mit der PayPal-API zu authentifizieren."}, "clientSecretSandbox": {"label": "Sandbox-Client-Secret", "tooltipText": "Das Client-Secret der REST-API, das das Plugin im Testfall dazu verwendet, sich mit der PayPal-API zu authentifizieren."}, "merchantPayerIdSandbox": {"label": "Sandbox-PayPal-Händler-ID", "tooltipText": "Die PayPal-Händler-ID, die dem Sandbox-PayPal-Konto zugeordnet ist (siehe Geschäftsangaben in den PayPal Kontoeinstellungen)."}, "sandbox": {"label": "Sandbox aktivieren", "tooltipText": "Aktiviere diese Option, um die Integration zu testen."}, "button": {"title": "Hole API-Zugangsdaten", "titleSandbox": "Hole Sandbox-API-Zugangsdaten", "messageFetchedError": " Bitte versuche es erneut oder gebe die Zugangsdaten manuell ein."}}, "behavior": {"cardTitle": "Verhalten", "merchantLocation": {"label": "Händlerstandort", "germany": "Deutschland", "other": "Anderer Händlerstandort"}, "intent": {"label": "Zahlungsabschluss", "CAPTURE": "Automatischer Zahlungseinzug (Intent CAPTURE)", "AUTHORIZE": "Manueller Zahlungseinzug (Intent AUTHORIZE)"}, "submitCart": {"label": "Warenkorb übertragen", "helpText": "<PERSON><PERSON> diese Option aktiv ist, werden beim Checkout die Warenkorbdaten an PayPal übertragen."}, "brandName": {"label": "Eigener Markenname auf der PayPal-Seite", "tooltipText": "Dieser Text wird als Markenname auf der PayPal-Zahlungsseite angezeigt."}, "landingPage": {"label": "PayPal-Landingpage", "helpText": {"login": "Anmeldung: Auf der PayPal-Seite wird der Login als Landingpage angezeigt.", "billing": "Registrierung: Auf der PayPal-Seite wird die Registrierung als Landingpage angezeigt.", "no_preference": "<PERSON>ine Präferenz: PayPal entscheidet auf Grundlage früherer Interaktionen des Kunden mit PayPal welche Seite angezeigt wird."}, "options": {"login": "<PERSON><PERSON><PERSON><PERSON>", "billing": "Registrierung", "no_preference": "<PERSON><PERSON>"}}, "sendOrderNumber": {"label": "Bestellnummer übertragen", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird beim Checkout die Bestellnummer an PayPal als Rechnungsnummer übertragen."}, "orderNumberPrefix": {"label": "Bestellnummer-Präfix", "tooltipText": "Dieser Text wird vor die ursprüngliche Bestellnummer gehängt (z.B. MeinShop_SW20001). Das hilft dabei der Identifizierung des Shops, in dem die Zahlung ausgeführt wurde. Du findest diese als Rechnungsnummer in Deinem PayPal-Dashboard."}, "orderNumberSuffix": {"label": "Bestellnummer-Suffix", "tooltipText": "Dieser Text wird an die ursprüngliche Bestellnummer gehängt (z.B. SW20001_MeinShop). Das hilft dabei der Identifizierung des Shops, in dem die Zahlung ausgeführt wurde. Du findest diese als Rechnungsnummer in Deinem PayPal-Dashboard."}, "loggingLevel": {"label": "Protokollierung", "tooltipText": "Die erweiterte Protokollierung erfasst während des Bestellvorgangs zusätzliche Daten, um potentielle Fehler aufzuspüren. Bitte nur vorübergehend verwenden.", "options": {"basic": "<PERSON><PERSON><PERSON>", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "excludedProductIds": {"label": "Ausgeschlossene Produkte", "tooltipText": "Hier ausgewählte Produkte können nicht mit PayPal gekauft werden."}, "excludedProductStreamIds": {"label": "Ausgeschlossene dynamische Produktgruppen", "tooltipText": "Hier ausgewählte dynamische Produktgruppen können nicht mit PayPal gekauft werden."}}, "express": {"cardTitle": "Express Checkout Shortcut", "cardSubtitle": "Express Checkouts erhöhen die Konversionsrate in Deinem Shop und stellen für Dich als Händler kein finanzielles Risiko dar, daher sollten sie immer aktiv sein.", "ecsDetailEnabled": {"label": "'<PERSON><PERSON><PERSON> zu PayPal' auf Detailseite", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der Express Checkout Button auf jeder Produktdetailseite angezeigt."}, "ecsCartEnabled": {"label": "'<PERSON><PERSON><PERSON> zu PayPal' im Warenkorb", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der Express Checkout Button auf der Warenkorbseite angezeigt."}, "ecsOffCanvasEnabled": {"label": "'<PERSON><PERSON><PERSON> zu PayPal' im Off-Canvas <PERSON>nkorb", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der Express Checkout Button in dem Off-Canvas-Warenkorb angezeigt."}, "ecsLoginEnabled": {"label": "'<PERSON><PERSON><PERSON> zu PayPal' au<PERSON>", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der Express Checkout Button auf der Login- und Registrierungsseite angezeigt."}, "ecsListingEnabled": {"label": "'<PERSON><PERSON><PERSON> zu PayPal' auf Listingseiten", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der Express Checkout Button auf Listingseiten angezeigt."}, "ecsButtonColor": {"label": "Buttonfarbe", "options": {"blue": "Blau", "black": "<PERSON><PERSON><PERSON>", "gold": "Gold (empfohlen)", "silver": "<PERSON><PERSON><PERSON>", "white": "<PERSON><PERSON>"}}, "ecsButtonShape": {"label": "Buttonform", "options": {"pill": "Rund", "rect": "<PERSON><PERSON><PERSON>"}}, "ecsSubmitCart": {"label": "Warenkorb übertragen", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der Warenkorb bei Express-Bestellungen an PayPal übertragen."}, "ecsButtonLanguageIso": {"label": "Buttonsprache", "helpText": "<PERSON>n nicht gesetzt, wird die Sprache des Verkaufskanals verwendet."}, "ecsShowPayLater": {"label": "'Später Bezahlen' neben dem 'PayPal Checkout'-<PERSON><PERSON> anzeigen", "helpText": "Die Schaltfläche 'Später Bezahlen' wird auf denselben Seiten und im selben Design wie die Schaltfläche 'Direkt zu PayPal' angezeigt."}}, "installment": {"cardTitle": "'Später Bezahlen'-Banner", "cardSubtitle": "Banner erhöhen die Konversionsrate in Deinem Shop und stellen für Dich als Händler kein finanzielles Risiko dar, daher sollten sie immer aktiv sein.", "installmentBannerEnabled": {"detailPage": {"label": "'Später Bezahlen'-Banner auf Detailseite", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der 'Später Bezahlen'-Banner auf jeder Produktdetailseite angezeigt."}, "cart": {"label": "'Später Bezahlen'-Banner im Warenkorb", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der 'Später Bezahlen'-Banner im Warenkorb angezeigt."}, "offCanvasCart": {"label": "'Später Bezahlen'-Banner im Off-Canvas-Warenkorb", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der 'Später Bezahlen'-Banner im Off-Canvas-Warenkorb angezeigt."}, "loginPage": {"label": "'Später Bezahlen'-Banner auf Loginseite", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der 'Später Bezahlen'-Banner auf der Login- und Registrierungsseite angezeigt."}, "footer": {"label": "'Später Bezahlen'-Banner im Footer", "helpText": "<PERSON><PERSON> diese Option aktiv ist, wird der 'Später Bezahlen'-Banner im Footer angezeigt."}}}, "acdc": {"cardTitle": "Kredit- oder Debitkarte", "acdcForce3DS": {"label": "Zahlungen aus Nicht-3DS-Ländern blockieren", "helpText": "PayPal prüft auf Basis der präsentierten Kredit- oder Debitkarte, ob 3DS (Starke Kunden-Authentifizierung) erforderlich ist. Durch das Setzen dieser Option werden Zahlungsversuche ohne 3DS-Check abgelehnt."}}, "pui": {"cardTitle": "Re<PERSON><PERSON>ngskauf", "customerServiceInstructions": {"label": "Kundenservice-Anweisungen für Rechnungskauf", "helpText": "Diese Anweisungen werden an PayPal & RatePay übermittelt und in Kunden-E-Mails verwendet."}}, "spb": {"cardTitle": "Smart Payment Buttons", "spbCheckoutEnabled": {"label": "Smart Payment Buttons aktivieren"}, "spbButtonLanguageIso": {"label": "Buttonsprache", "helpText": "<PERSON>n nicht gesetzt, wird die Sprache des Verkaufskanals verwendet."}, "spbAlternativePaymentMethodsEnabled": {"label": "Aktiviert die alternativen Zahlungsarten der Smart Payment Buttons.", "helpText": "Die alternativen Zahlungsarten sind Kredit- und Debitkarten und weitere."}, "spbShowPayLater": {"label": "'<PERSON><PERSON><PERSON>er Bezahlen'-<PERSON><PERSON> neben PayPal-<PERSON><PERSON> anzeigen"}}, "plus": {"cardTitle": "PayPal PLUS", "introduction": "<strong>Die Komplettlösung mit PayPal, Lastschrift und Kreditkarte.</strong> PayPal PLUS bietet deinen Kunden durch die Vielzahl der Zahlarten mehr Zahlungsflexibilität. Deine Vorteile als Händler: Die vereinfachte Buchhaltung mit nur einem Vertragspartner, einem Konto und einer Transaktionsübersicht für alle Zahlungseingänge. Dein Geld erhältst du direkt nach Kaufabschluss.<br>Bitte PayPal PLUS <a href='https://www.paypal.com/de/webapps/mpp/paypal-plus' target='_blank'>hier</a> beantragen.", "plusCheckoutEnabled": {"label": "PayPal PLUS aktivieren"}, "hint": "<PERSON><PERSON><PERSON><PERSON>: PayPal PLUS funktioniert nur mit dem Zahlungsabschluss \"Sale\". Dieser wird automatisch gesetzt.", "warning": {"active": "PayPal Checkout bietet Deinen Kunden die gleichen Zahlungsmethoden - basierend auf der neuesten Technologie. Aktiviere jetzt PayPal Checkout und deaktiviere PayPal PLUS, um die Anzeige von doppelten Zahlungsmethoden zu vermeiden. <a href=\"https://www.shopware.com/de/news/paypal-launcht-neues-produkt/?utm_source=product&utm_medium=sw6-onprem&utm_campaign=paypal-checkout&utm_content=link-de-blog\" target=\"_blank\">Mehr Informationen</a>", "inactive": "Vermeide die Anzeige von doppelten Zahlungsarten und deaktiviere PayPal PLUS. <a href=\"https://www.shopware.com/de/news/paypal-launcht-neues-produkt/?utm_source=product&utm_medium=sw6-onprem&utm_campaign=paypal-checkout&utm_content=link-de-blog\" target=\"_blank\">Mehr Informationen</a>"}}, "locale-field": {"infobox": {"text": "Um eine Liste der gültigen Ländercodes zu sehen, klicken Sie ", "href": "hier"}, "error": {"detail": "Der von Ihnen angegebene Ländercode entspricht nicht dem von PayPal geforderten Format. Ein gültiges Format ist \"en_GB\"."}}, "messageSaveSuccess": "Die PayPal-Einstellungen wurden gespeichert.", "messageTestSuccess": "Die API-Zugangsdaten wurden validiert.", "messageTestError": "Die API-Zugangsdaten konnten nicht validiert werden.", "messageWebhookCreated": "Der Webhook wurde registriert.", "messageWebhookUpdated": "Der Webhook wurde aktualisiert.", "messageWebhookError": "Der Webhook konnte nicht gespeichert werden. Folgende Fehler sind aufgetreten:"}}}