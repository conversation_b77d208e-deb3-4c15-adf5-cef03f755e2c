# BsOdoo Plugin Documentation
## Shopware 6 - Odoo Integration Plugin

**Version:** 1.0.0  
**Developer:** BrainStream Technolabs  
**Website:** https://brainstreamtechnolabs.com  
**License:** MIT  

---

## Table of Contents

1. [Overview](#overview)
2. [System Requirements](#system-requirements)
3. [Installation Steps](#installation-steps)
4. [Configuration Setup](#configuration-setup)
5. [Entity Mapping](#entity-mapping)
6. [Sync Details](#sync-details)
7. [API Endpoints](#api-endpoints)
8. [Database Schema](#database-schema)
9. [Troubleshooting](#troubleshooting)
10. [Support](#support)

---

## Overview

The BsOdoo plugin provides seamless integration between Shopware 6 and Odoo ERP systems. It enables automatic synchronization of customers, products, and orders from Shopware to Odoo, maintaining data consistency across both platforms.

### Key Features

- **Real-time Order Synchronization**: Automatically sync orders from Shopware to Odoo
- **Customer Management**: Sync customer data including addresses and contact information
- **Product Integration**: Synchronize product catalog between systems
- **Bi-directional Mapping**: Maintain ID mapping between Shopware and Odoo entities
- **Configuration Verification**: Built-in connection testing and validation
- **Message Queue Support**: Asynchronous processing for better performance
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

---

## System Requirements

### Shopware Requirements
- **Shopware Version**: 6.4.* or higher
- **PHP Extensions**:
  - ext-curl
  - ext-libxml
  - ext-iconv
  - ext-simplexml
  - ext-dom

### Odoo Requirements
- **Odoo Version**: 13.0 or higher
- **XML-RPC API**: Enabled and accessible
- **API User**: Dedicated user account with appropriate permissions

### Server Requirements
- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher / MariaDB 10.3 or higher
- **Memory**: Minimum 512MB PHP memory limit
- **Network**: HTTP/HTTPS access to Odoo server

---

## Installation Steps

### Step 1: Download and Extract
1. Download the BsOdoo plugin package
2. Extract the plugin to your Shopware installation directory:
   ```
   /path/to/shopware/custom/plugins/BsOdoo/
   ```

### Step 2: Install via Command Line
```bash
# Navigate to Shopware root directory
cd /path/to/shopware

# Refresh plugin list
bin/console plugin:refresh

# Install the plugin
bin/console plugin:install --activate BsOdoo

# Clear cache
bin/console cache:clear
```

### Step 3: Install via Administration Panel
1. Log in to Shopware Administration
2. Navigate to **Extensions > My Extensions**
3. Click **Upload extension**
4. Select the BsOdoo plugin ZIP file
5. Click **Install** and then **Activate**

### Step 4: Database Migration
The plugin automatically creates the required database table during installation:
- Table: `bs_odoo` - Stores ID mappings between Shopware and Odoo entities

---

## Configuration Setup

### Access Configuration
1. Navigate to **Settings > System > Plugins**
2. Find **BsOdoo** plugin and click **Configure**

### General Configuration

#### Enable Integration
- **Field**: Enable the integration
- **Type**: Boolean (Checkbox)
- **Default**: False
- **Description**: Master switch to activate/deactivate the Odoo integration

### Odoo Configuration

#### Server URL
- **Field**: Odoo server URL
- **Type**: URL
- **Required**: Yes
- **Example**: `https://your-odoo-instance.com`
- **Description**: Complete URL to your Odoo server

#### Username
- **Field**: Odoo username
- **Type**: Text
- **Required**: Yes
- **Description**: Username of the Odoo API user

#### API Key/Password
- **Field**: Odoo API key/Password
- **Type**: Password
- **Required**: Yes
- **Description**: API key or password for the Odoo user
- **Note**: API key is recommended for security

#### Database Name
- **Field**: Odoo database name
- **Type**: Text
- **Required**: Yes
- **Description**: Name of the Odoo database to connect to

#### Connection Verification
- **Component**: API Verify Button
- **Function**: Tests connection to Odoo server
- **Endpoint**: `/api/bs-odoo/verify`

### Other Configuration

#### UID (Auto-filled)
- **Field**: Odoo UID
- **Type**: Text
- **Disabled**: True
- **Description**: User ID obtained after successful authentication

#### Company ID
- **Field**: Odoo Company Id
- **Type**: Integer
- **Description**: ID of the Odoo company for data synchronization

#### Default Category ID
- **Field**: Default Odoo Category Id
- **Type**: Integer
- **Description**: Default category for new products from Shopware

#### Default Tax ID
- **Field**: Default Odoo Tax Id
- **Type**: Integer
- **Description**: Default tax configuration for new products

#### Default Pricelist ID
- **Field**: Default Odoo Pricelist Id
- **Type**: Integer
- **Description**: Default pricelist for new customers

---

## Entity Mapping

The plugin maintains ID mappings between Shopware and Odoo entities in the `bs_odoo` table.

### Supported Entity Types

| Module | Shopware Entity | Odoo Model | Description |
|--------|----------------|------------|-------------|
| `customer` | Customer | res.partner | Customer records |
| `customer_address` | Customer Address | res.partner | Customer addresses |
| `product` | Product | product.product | Product catalog |
| `order` | Order | sale.order | Sales orders |
| `currency` | Currency | res.currency | Currency definitions |
| `country` | Country | res.country | Country data |
| `state` | Country State | res.country.state | State/Province data |

### Mapping Table Structure
```sql
CREATE TABLE `bs_odoo` (
    `id` BINARY(16) NOT NULL,
    `record_id` BINARY(16) NOT NULL,    -- Shopware entity ID
    `odoo_id` VARCHAR(36) NOT NULL,     -- Odoo record ID
    `module` VARCHAR(20) NOT NULL,      -- Entity type
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3),
    PRIMARY KEY (`id`),
    INDEX `idx_record_module` (`record_id`, `module`),
    INDEX `idx_odoo_module` (`odoo_id`, `module`)
);
```

---

## Sync Details

### Order Synchronization

#### Trigger Events
- **Event**: Order creation/update in Shopware
- **Processing**: Asynchronous via message queue
- **Condition**: Integration must be enabled

#### Sync Process
1. **Order Data Extraction**: Retrieve order with all associations
2. **Customer Sync**: Ensure customer exists in Odoo
3. **Product Sync**: Verify all products exist in Odoo
4. **Address Handling**: Sync billing and shipping addresses
5. **Order Creation**: Create sale.order in Odoo
6. **Mapping Storage**: Store Shopware-Odoo ID relationship

#### Order Data Mapping
```php
$createFields = [
    'order_line' => $odooLineItems,           // Order line items
    'partner_id' => $odooPartnerId,           // Customer ID
    'partner_invoice_id' => $odooInvoiceId,   // Billing address
    'partner_shipping_id' => $odooShippingId, // Shipping address
    'company_id' => $companyId,               // Odoo company
    'user_id' => $userId,                     // Salesperson
    'origin' => $orderNumber,                 // Shopware order number
    'pricelist_id' => $pricelistId           // Customer pricelist
];
```

### Customer Synchronization

#### Customer Data Mapping
- **Name**: Company name or full name
- **Email**: Customer email address
- **Address**: Street, city, postal code
- **Country/State**: Mapped to Odoo location data
- **Phone**: Contact number
- **VAT**: Tax identification number
- **Customer Number**: Reference field

#### Address Handling
- **Billing Address**: Primary contact in Odoo
- **Shipping Address**: Child contact with type 'delivery'
- **Multiple Addresses**: Supported via parent-child relationship

### Product Synchronization

#### Product Data Mapping
- **Name**: Product name
- **SKU**: Product number (default_code in Odoo)
- **Description**: Product description
- **Price**: List price and cost price
- **Category**: Product category
- **Tax**: Tax configuration
- **Company**: Multi-company support

#### Custom Products
For non-catalog items (shipping, discounts):
- Creates generic products in Odoo
- Uses item name and price from Shopware
- Applies default tax and category settings

---

## API Endpoints

### Verification Endpoint
- **URL**: `/api/bs-odoo/verify`
- **Method**: POST
- **Purpose**: Test Odoo connection and authentication
- **Parameters**:
  - `BsOdoo.config.serverUrl`
  - `BsOdoo.config.database`
  - `BsOdoo.config.username`
  - `BsOdoo.config.password`

#### Response Format
```json
{
    "success": true,
    "message": "success",
    "uid": 123
}
```

---

## Database Schema

### Migration File
- **File**: `Migration1754291203BsOdoo.php`
- **Timestamp**: 1754291203
- **Purpose**: Create mapping table and indexes

### Table Details
- **Engine**: InnoDB
- **Charset**: utf8mb4_unicode_ci
- **Indexes**: Optimized for lookup operations
- **Foreign Keys**: None (loose coupling design)

---

## Troubleshooting

### Common Issues

#### Connection Failed
- Verify Odoo server URL is accessible
- Check firewall settings
- Ensure XML-RPC is enabled in Odoo

#### Authentication Failed
- Verify username and password/API key
- Check user permissions in Odoo
- Ensure database name is correct

#### Sync Failures
- Check plugin logs in `var/log/bs_odoo_logs.log`
- Verify required Odoo modules are installed
- Check data integrity and required fields

### Log Files
- **Location**: `var/log/bs_odoo_logs.log`
- **Rotation**: Daily rotation enabled
- **Level**: Error, Warning, Info, Debug

---

## Support

### Developer Information
- **Company**: BrainStream Technolabs
- **Website**: https://brainstreamtechnolabs.com
- **Email**: <EMAIL>

### Documentation
- Plugin source code includes comprehensive PHPDoc comments
- Configuration XML provides field descriptions
- Service classes implement clear interfaces

### Version Information
- **Current Version**: 1.0.0
- **Shopware Compatibility**: 6.4.*
- **License**: MIT License

---

*This documentation covers the complete setup and usage of the BsOdoo plugin for Shopware 6. For additional support or custom requirements, please contact BrainStream Technolabs.*
