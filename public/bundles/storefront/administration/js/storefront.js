/*! For license information please see storefront.js.LICENSE.txt */
!function(e){var t={};function n(a){if(t[a])return t[a].exports;var i=t[a]={i:a,l:!1,exports:{}};return e[a].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(a,i,function(t){return e[t]}.bind(null,i));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/bundles/storefront/",n(n.s="L9KI")}({"/Ssc":function(e,t,n){var a=n("iZ31");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("427a179f",a,!0,{})},"/Zkd":function(e,t,n){var a=n("qGhd");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("26501a18",a,!0,{})},"3jMd":function(e,t){Shopware.Service("privileges").addPrivilegeMappingEntry({category:"permissions",parent:"content",key:"theme",roles:{viewer:{privileges:["theme:read","theme_child:read","sales_channel:read",Shopware.Service("privileges").getPrivileges("media.viewer")],dependencies:[]},editor:{privileges:["theme:update","theme_child:update","tag:read","product_media:read","product:read","category:read","product_manufacturer:read","mail_template_media:read","mail_template:read","document_base_config:read","user:read","payment_method:read","shipping_method:read","custom_field_set:read","custom_field:read","custom_field_set_relation:read",Shopware.Service("privileges").getPrivileges("media.creator")],dependencies:["theme.viewer"]},creator:{privileges:["theme:create","theme_child:create"],dependencies:["theme.viewer","theme.editor"]},deleter:{privileges:["theme:delete","theme_child:delete"],dependencies:["theme.viewer"]}}})},"3zw8":function(e,t,n){},"H/iK":function(e,t,n){var a=n("jxjI");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("3ba43fca",a,!0,{})},L9KI:function(e,t,n){"use strict";n.r(t);n("Ujji"),n("eP6W");function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,i,s,o,l=[],r=!0,c=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;r=!1}else for(;!(r=(a=s.call(n)).done)&&(l.push(a.value),l.length!==t);r=!0);}catch(e){c=!0,i=e}finally{try{if(!r&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var s=Shopware,o=s.Component,l=s.Mixin,r=Shopware.Data.Criteria,c=Shopware.Utils.object,m=c.getObjectDiff,h=c.cloneDeep,d=Shopware.Utils.types.isArray;o.register("sw-theme-manager-detail",{template:'{% block sw_theme_manager_detail %}\n    <sw-page class="sw-theme-manager-detail">\n\n        {% block sw_theme_manager_detail_search_bar %}\n            <template slot="search-bar">\n                <sw-search-bar :placeholder="$tc(\'sw-theme-manager.general.placeholderSearchBar\')"\n                               :entityService="themeRepository"\n                               @search="onSearch">\n                </sw-search-bar>\n            </template>\n        {% endblock %}\n\n        {% block sw_theme_manager_detail_smart_bar_header %}\n            <template slot="smart-bar-header">\n\n                {% block sw_theme_manager_detail_smart_bar_header_title %}\n                    <h2 v-if="theme">\n                        {% block sw_theme_manager_detail_smart_bar_header_title_text %}\n                            {{ theme.name }}\n                        {% endblock %}\n                    </h2>\n\n                    <h2 v-else>\n                        {% block sw_theme_manager_detail_smart_bar_header_title_text_default %}\n                            {{ $tc(\'sw-theme-manager.list.textThemeOverview\') }}\n                        {% endblock %}\n                    </h2>\n                {% endblock %}\n            </template>\n        {% endblock %}\n\n        {% block sw_theme_manager_detail_smart_bar_actions %}\n            <template slot="smart-bar-actions">\n                <sw-button-group\n                    v-tooltip.bottom="{\n                        message: $tc(\'sw-privileges.tooltip.warning\'),\n                        disabled: acl.can(\'theme.editor\'),\n                        showOnDisabledElements: true\n                    }"\n                    class="sw-theme-manager-detail__save-button-group"\n                    :split-button="true"\n                >\n                    {% block sw_theme_manager_detail_smart_bar_actions_save %}\n                        <sw-button-process\n                            v-tooltip.bottom="{\n                                message: $tc(\'sw-privileges.tooltip.warning\'),\n                                disabled: acl.can(\'theme.editor\'),\n                                showOnDisabledElements: true\n                            }"\n                            class="sw_theme_manager_detail__save-action"\n                            :is-loading="isLoading"\n                            :process-success="isSaveSuccessful"\n                            variant="primary"\n                            :disabled="isLoading || !acl.can(\'theme.editor\')"\n                            @process-finish="saveFinish"\n                            @click.prevent="onSave"\n                        >\n                            {{ $tc(\'sw-theme-manager.actions.save\') }}\n                        </sw-button-process>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_smart_bar_actions_save_context_menu %}\n                        <sw-context-button>\n                            <template slot="button">\n                                <sw-button\n                                    class="sw_theme_manager_detail__button-context-menu"\n                                    square\n                                    variant="primary"\n                                    :disabled="isLoading || !acl.can(\'theme.editor\')"\n                                >\n                                    <sw-icon\n                                            name="small-arrow-medium-down"\n                                            size="16"\n                                    />\n                                </sw-button>\n                            </template>\n\n                            {% block sw_theme_manager_detail_smart_bar_actions_save_context_menu_actions %}\n                                {% block sw_theme_manager_detail_smart_bar_actions_save_clean %}\n                                    <sw-context-menu-item\n                                        class="sw_theme_manager_detail__save-clean-action"\n                                        :disabled="!acl.can(\'theme.editor\')"\n                                        @click="onSaveClean"\n                                        v-tooltip.top="{\n                                            message: $tc(\'sw-theme-manager.actions.saveCleanToolTip\'),\n                                            disabled: !acl.can(\'theme.editor\'),\n                                            showOnDisabledElements: true\n                                        }"\n                                    >\n                                        {{ $tc(\'sw-theme-manager.actions.saveClean\') }}\n                                    </sw-context-menu-item>\n                                {% endblock %}\n                            {% endblock %}\n                        </sw-context-button>\n                    {% endblock %}\n                </sw-button-group>\n            </template>\n        {% endblock %}\n\n        {% block sw_theme_manager_detail_content %}\n            <template slot="content">\n                <div\n                    v-if="!shouldShowContent"\n                    class="sw-theme-manager-detail__content-skeleton"\n                >\n                    <sw-skeleton />\n                    <sw-skeleton />\n                </div>\n\n                <div v-else class="sw-theme-manager-detail__content">\n                    <sw-tabs defaultItem="default" @new-item-active="onChangeTab">\n                        <template #default="{ active }">\n                            <sw-tabs-item v-if="hasMoreThanOneTab"\n                                          v-for="(tab, tabName) in structuredThemeFields.tabs"\n                                          :key="tabName"\n                                          :title="tab.label"\n                                          :name="tabName"\n                                          :active="active === tabName">\n                                {{ tab.label || $tc(\'sw-theme-manager.general.defaultTab\') }}\n                            </sw-tabs-item>\n                        </template>\n\n                        <template #content="{ active }">\n                            <template v-if="active === \'default\'">\n                                <template v-if="isDerived">\n                                    {% block sw_theme_manager_detail_content_inheritance %}\n                                        <div class="sw-theme-manager-detail__inheritance">\n                                            {% block sw_theme_manager_detail_content_inheritance_icon %}\n                                                <sw-icon :multicolor="true" size="20" name="custom-inherited"></sw-icon>\n                                            {% endblock %}\n\n                                            {% block sw_theme_manager_detail_content_inheritance_text %}\n                                                <p class="sw-theme-manager-detail__inheritance-text" v-if="parentTheme">\n                                                    {{ $tc(\'sw-theme-manager.detail.inheritanceInfo\', 0, { parentThemeName: parentTheme.name }) }}\n                                                </p>\n                                                <p class="sw-theme-manager-detail__inheritance-text" v-else-if="defaultTheme">\n                                                    {{ $tc(\'sw-theme-manager.detail.inheritanceInfo\', 0, { parentThemeName: defaultTheme.name }) }}\n                                                </p>\n                                            {% endblock %}\n                                        </div>\n                                    {% endblock %}\n                                </template>\n\n                                {% block sw_theme_manager_detail_content_info %}\n                                    <sw-card class="sw-theme-manager-detail__info-card">\n                                        <div class="sw-theme-manager-detail__info">\n\n                                            {% block sw_theme_manager_detail_content_info_image %}\n                                                <div class="sw-theme-manager-detail__info-image" :style="previewMedia"></div>\n                                            {% endblock %}\n\n                                            {% block sw_theme_manager_detail_content_info_content %}\n                                                <div class="sw-theme-manager-detail__info-content">\n                                                    <div class="sw-theme-manager-detail__info-name">{{ theme.name }}</div>\n                                                    <div class="sw-theme-manager-detail__info-author">{{ theme.author }}</div>\n                                                    <div v-if="theme.description" class="sw-theme-manager-detail__info-descr">\n                                                        <p class="sw-theme-manager-detail__info-descr-title">\n                                                            {{ $tc(\'sw-theme-manager.detail.description\') }}:\n                                                        </p>\n                                                        <p>{{ theme.description |truncate(140) }}</p>\n                                                    </div>\n\n                                                    <sw-entity-multi-select\n                                                        v-model="theme.salesChannels"\n                                                        class="sw-theme-manager-detail__saleschannels-select"\n                                                        :label="$tc(\'sw-theme-manager.detail.salesChannel\')"\n                                                        :disabled="!acl.can(\'theme.editor\')"\n                                                        :helpText="isDefaultTheme ? $tc(\'sw-theme-manager.detail.salesChannelHelpText\') : null"\n                                                        :selectionDisablingMethod="selectionDisablingMethod">\n                                                        <template #result-item="{ item, index }">\n                                                            <span v-if="!isThemeCompatible(item)"></span>\n                                                        </template>\n                                                    </sw-entity-multi-select>\n\n                                                </div>\n                                            {% endblock %}\n                                        </div>\n\n                                        {% block sw_theme_manager_detail_content_info_context_button %}\n                                            <sw-context-button\n                                                class="sw-theme-manager-detail__context-button"\n                                                :zIndex="1100"\n                                            >\n\n                                                {% block sw_theme_manager_detail_context_button_option_rename %}\n                                                    <sw-context-menu-item\n                                                        @click="onRenameTheme(theme)"\n                                                        :disabled="!acl.can(\'theme.editor\')">\n                                                        {{ $tc(\'sw-theme-manager.actions.rename\') }}\n                                                    </sw-context-menu-item>\n                                                {% endblock %}\n\n                                                {% block sw_theme_manager_detail_context_button_option_create %}\n                                                    <sw-context-menu-item\n                                                        @click="onDuplicateTheme(theme)"\n                                                        v-if="!theme.parentThemeId"\n                                                        :disabled="!acl.can(\'theme.creator\')">\n                                                        {{ $tc(\'sw-theme-manager.actions.duplicate\') }}\n                                                    </sw-context-menu-item>\n                                                {% endblock %}\n\n                                                {% block sw_theme_manager_detail_context_button_option_reset %}\n                                                    <sw-context-menu-item\n                                                        @click="onReset"\n                                                        v-if="theme.configValues !== null"\n                                                        :disabled="!acl.can(\'theme.editor\')"\n                                                        variant="danger">\n                                                        {{ $tc(\'sw-theme-manager.actions.buttonReset\') }}\n                                                    </sw-context-menu-item>\n                                                {% endblock %}\n\n                                                {% block sw_theme_manager_detail_context_button_option_delete %}\n                                                    <sw-context-menu-item\n                                                        v-if="theme.parentThemeId"\n                                                        :disabled="!acl.can(\'theme.deleter\') || theme.salesChannels.length > 0"\n                                                        v-tooltip.right="deleteDisabledToolTip"\n                                                        class="sw-theme-manager-detail__option-delete"\n                                                        variant="danger"\n                                                        @click="onDeleteTheme(theme)">\n                                                        {{ $tc(\'sw-theme-manager.actions.delete\') }}\n                                                    </sw-context-menu-item>\n                                                {% endblock %}\n                                            </sw-context-button>\n                                        {% endblock %}\n                                    </sw-card>\n                                {% endblock %}\n                            </template>\n\n                            <template v-for="(tab, tabName) in structuredThemeFields.tabs">\n                                <template v-if="tabName === active">\n                                    {% block sw_theme_manager_detail_content_areas %}\n                                        <sw-card v-for="(block, blockName) in tab.blocks"\n                                                 :key="blockName"\n                                                 class="sw-theme-manager-detail__area"\n                                                 :title="block.label">\n\n                                            {% block sw_theme_manager_detail_content_sections %}\n                                                <sw-card-section v-for="(section, sectionName) in block.sections"\n                                                                 :key="sectionName">\n                                                    <div v-if="section.label" class="sw-theme-manager-detail__content--section_title">\n                                                        {{ section.label }}\n                                                    </div>\n\n                                                    {% block sw_theme_manager_detail_content_fields %}\n                                                        <sw-container class="sw-theme-manager-detail__content--section_field">\n                                                            <div v-for="(field, fieldName) in section.fields"\n                                                                 :key="fieldName"\n                                                                 class="sw-theme-manager-detail__content--section_field"\n                                                                 :class="{\'sw-theme-manager-detail__content--section_field-full-width\': field.fullWidth}">\n                                                                <template v-if="themeConfig[fieldName] && baseThemeConfig[fieldName]">\n                                                                    <template v-if="mapSwFieldTypes(field.type) === \'select\'">\n                                                                        <sw-inherit-wrapper\n                                                                                :class="\'sw-field-id-\' + fieldName"\n                                                                                v-model="currentThemeConfig[fieldName].value"\n                                                                                :ref="`wrapper-${fieldName}`"\n                                                                                :has-parent="theme.baseConfig?.fields?.[fieldName] == null"\n                                                                                :inherited-value="baseThemeConfig[fieldName].value"\n                                                                                :label="!field.label ? \'\' : field.label"\n                                                                                :customInheritationCheckFunction="checkInheritanceFunction(fieldName)"\n                                                                                @input="handleInheritanceInput($event, fieldName)"\n                                                                        >\n                                                                            <template #content="{ currentValue, updateCurrentValue, isInherited }">\n                                                                                <sw-field\n                                                                                        type="select"\n                                                                                        :placeholder="field.placeholder"\n                                                                                        :options="field.options"\n                                                                                        :helpText="!field.helpText ? null : field.helpText"\n                                                                                        :disabled="isInherited || !acl.can(\'theme.editor\')"\n                                                                                        @change="updateCurrentValue"\n                                                                                        @input="updateCurrentValue"\n                                                                                        v-model="currentValue">\n                                                                                </sw-field>\n                                                                            </template>\n                                                                        </sw-inherit-wrapper>\n                                                                    </template>\n\n                                                                    <template v-else-if="field.type === \'url\'">\n                                                                        <sw-inherit-wrapper\n                                                                                :class="\'sw-field-id-\' + fieldName"\n                                                                                v-model="currentThemeConfig[fieldName].value"\n                                                                                :ref="`wrapper-${fieldName}`"\n                                                                                :has-parent="theme.baseConfig?.fields?.[fieldName] == null"\n                                                                                :inherited-value="baseThemeConfig[fieldName].value"\n                                                                                :label="!field.label ? \'\' : field.label"\n                                                                                :customInheritationCheckFunction="checkInheritanceFunction(fieldName)"\n                                                                                @input="handleInheritanceInput($event, fieldName)"\n                                                                        >\n                                                                            <template #content="{ currentValue, updateCurrentValue, isInherited }">\n                                                                                <sw-field\n                                                                                        type="url"\n                                                                                        :helpText="!field.helpText ? null : field.helpText"\n                                                                                        :disabled="isInherited || !acl.can(\'theme.editor\')"\n                                                                                        @change="updateCurrentValue"\n                                                                                        @input="updateCurrentValue"\n                                                                                        v-model="currentValue">\n                                                                                </sw-field>\n                                                                            </template>\n                                                                        </sw-inherit-wrapper>\n                                                                    </template>\n                                                                    <div v-else-if="field.type === \'media\'">\n\n                                                                        <sw-upload-listener\n                                                                            :uploadTag="tabName + \'-\' + blockName + \'-\' + sectionName + \'-\' + fieldName"\n                                                                            @media-upload-finish="successfulUpload($event, currentThemeConfig[fieldName])"\n                                                                            autoUpload>\n                                                                        </sw-upload-listener>\n\n                                                                        <sw-inherit-wrapper\n                                                                                :class="\'sw-field-id-\' + fieldName"\n                                                                                v-model="currentThemeConfig[fieldName].value"\n                                                                                :ref="`wrapper-${fieldName}`"\n                                                                                :has-parent="theme.baseConfig?.fields?.[fieldName] == null"\n                                                                                :inherited-value="baseThemeConfig[fieldName].value"\n                                                                                :label="!field.label ? \'\' : field.label"\n                                                                                :customInheritationCheckFunction="checkInheritanceFunction(fieldName)"\n                                                                                @input="handleInheritanceInput($event, fieldName)"\n                                                                        >\n                                                                                <template #content="{ currentValue, updateCurrentValue, isInherited, removeInheritance }">\n                                                                                    <sw-media-upload-v2\n                                                                                        :source="currentValue"\n                                                                                        :uploadTag="tabName + \'-\' + blockName + \'-\' + sectionName + \'-\' + fieldName"\n                                                                                        :helpText="!field.helpText ? null : field.helpText"\n                                                                                        :ref="tabName + \'-\' + blockName + \'-\' + sectionName + \'-\' + fieldName"\n                                                                                        :defaultFolder="themeRepository.schema.entity"\n                                                                                        :allowMultiSelect="false"\n                                                                                        :disabled="!acl.can(\'theme.editor\') || themeConfig[fieldName].editable === false"\n                                                                                        @media-drop="onDropMedia($event, currentThemeConfig[fieldName])"\n                                                                                        @media-upload-sidebar-open="openMediaSidebar()"\n                                                                                        @media-upload-remove-image="removeMediaItem(fieldName, updateCurrentValue, isInherited, removeInheritance)">\n                                                                                    </sw-media-upload-v2>\n                                                                                </template>\n                                                                            </sw-inherit-wrapper>\n                                                                    </div>\n\n                                                                    <template v-else-if="mapSwFieldTypes(field.type)">\n                                                                        <sw-inherit-wrapper\n                                                                            :class="\'sw-field-id-\' + fieldName"\n                                                                            v-model="currentThemeConfig[fieldName].value"\n                                                                            :ref="`wrapper-${fieldName}`"\n                                                                            :has-parent="theme.baseConfig?.fields?.[fieldName] == null"\n                                                                            :inherited-value="baseThemeConfig[fieldName].value"\n                                                                            :label="!field.label ? \'\' : field.label"\n                                                                            :customInheritationCheckFunction="checkInheritanceFunction(fieldName)"\n                                                                            @input="handleInheritanceInput($event, fieldName)"\n                                                                        >\n                                                                            <template #content="{ currentValue, updateCurrentValue, isInherited }">\n                                                                                <sw-field\n                                                                                    :type="mapSwFieldTypes(field.type)"\n                                                                                    :helpText="!field.helpText ? null : field.helpText"\n                                                                                    :disabled="isInherited || !acl.can(\'theme.editor\') || themeConfig[fieldName].editable === false"\n                                                                                    :value="currentValue"\n                                                                                    @change="updateCurrentValue"\n                                                                                    @input="updateCurrentValue"\n                                                                                >\n                                                                                </sw-field>\n                                                                            </template>\n                                                                        </sw-inherit-wrapper>\n                                                                    </template>\n\n                                                                    <template v-else-if="themeConfig[fieldName].editable !== false">\n                                                                        <sw-inherit-wrapper\n                                                                                :class="\'sw-field-id-\' + fieldName"\n                                                                                v-model="currentThemeConfig[fieldName].value"\n                                                                                :ref="`wrapper-${fieldName}`"\n                                                                                :has-parent="theme.baseConfig?.fields?.[fieldName] == null"\n                                                                                :inherited-value="baseThemeConfig[fieldName].value"\n                                                                                :label="!field.label ? \'\' : field.label"\n                                                                                :customInheritationCheckFunction="checkInheritanceFunction(fieldName)"\n                                                                                @input="handleInheritanceInput($event, fieldName)"\n                                                                        >\n                                                                            <template #content="{ currentValue, updateCurrentValue, isInherited }">\n                                                                                <sw-form-field-renderer\n                                                                                    v-bind="getBind(field)"\n                                                                                    :value="currentValue"\n                                                                                    @change="updateCurrentValue"\n                                                                                    @input="updateCurrentValue"\n                                                                                    :disabled="isInherited || !acl.can(\'theme.editor\')">\n                                                                                </sw-form-field-renderer>\n                                                                            </template>\n                                                                        </sw-inherit-wrapper>\n                                                                    </template>\n                                                                </template>\n                                                            </div>\n                                                        </sw-container>\n                                                    {% endblock %}\n                                                </sw-card-section>\n                                            {% endblock %}\n                                        </sw-card>\n                                    {% endblock %}\n                                </template>\n                            </template>\n                        </template>\n                    </sw-tabs>\n                </div>\n            {% endblock %}\n\n            {% block sw_theme_manager_reset_modal %}\n                <sw-modal v-if="showResetModal"\n                          @modal-close="onCloseResetModal"\n                          :title="$tc(\'sw-theme-manager.modal.modalTitleReset\')"\n                          variant="small">\n\n                    {% block sw_theme_manager_reset_modal_reset_text %}\n                        <p class="sw_theme_manager__confirm-reset-text">\n                            {{ $tc(\'sw-theme-manager.modal.modalTextResetInfo\') }}\n                        </p>\n                        <p v-if="theme.salesChannels.length > 0" class="sw_theme_manager__confirm-reset-text">\n                            {{ $tc(\'sw-theme-manager.modal.modalTextResetAssigned\') }}\n                        </p>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_reset_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_theme_manager_reset_modal_cancel %}\n                                <sw-button @click="onCloseResetModal"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.actions.buttonCancel\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_theme_manager_reset_modal_confirm %}\n                                <sw-button @click="onConfirmThemeReset"\n                                           variant="danger"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.actions.buttonReset\') }}\n                                </sw-button>\n                            {% endblock %}\n                     </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n\n            {% block sw_theme_manager_detail_delete_modal %}\n                <sw-modal v-if="showDeleteModal"\n                          @modal-close="onCloseDeleteModal"\n                          :title="$tc(\'global.default.warning\')"\n                          variant="small">\n\n                    {% block sw_theme_manager_detail_delete_modal_info %}\n                        <div class="sw_theme_manager__confirm-delete-text">\n                            {{ $tc(\'sw-theme-manager.modal.textDeleteInfo\', 0,\n                            { themeName: theme.name }) }}\n                        </div>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_delete_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_theme_manager_detail_delete_modal_cancel %}\n                                <sw-button @click="onCloseDeleteModal"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_theme_manager_detail_delete_modal_confirm %}\n                                <sw-button @click="onConfirmThemeDelete"\n                                           variant="danger"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonDelete\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n\n            {% block sw_theme_manager_detail_save_modal %}\n                <sw-modal v-if="showSaveModal"\n                          class="sw-theme-manager-detail-modal"\n                          @modal-close="onCloseSaveModal"\n                          :title="$tc(\'sw-theme-manager.modal.modalTitleSave\')"\n                          variant="large">\n\n                    {% block sw_theme_manager_detail_save_modal_info %}\n                        <div class="sw_theme_manager__confirm-save-text">\n                            {{ $tc(\'sw-theme-manager.modal.textSaveInfo\', 0,\n                            { themeName: theme.name }) }}\n                        </div>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_save_modal_already_assigned_warning %}\n                        <sw-alert\n                            v-if="overwrittenSalesChannelAssignments.length > 0"\n                            variant="warning">\n\n                            <div class="sw-theme-manager-detail__sales-channel-warning" v-if="overwrittenSalesChannelAssignments.length === 1">\n                                <span v-html="$tc(\'sw-theme-manager.modal.salesChannelAlreadyAssignedModal.descriptionSingle\', 0, { newThemeName: theme.name })"></span>\n                            </div>\n\n                            <div class="sw-theme-manager-detail__sales-channel-warning" v-else>\n                                <span v-html="$tc(\'sw-theme-manager.modal.salesChannelAlreadyAssignedModal.descriptionMultiple\', 0, { newThemeName: theme.name })"></span>\n                            </div>\n\n                            <div v-for="salesChannel in overwrittenSalesChannelAssignments">\n                                <b>{{ salesChannel.oldThemeName }}</b> ({{ salesChannel.salesChannelName }})\n                            </div>\n                        </sw-alert>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_save_modal_removed_warning %}\n                        <sw-alert\n                            v-if="removedSalesChannels.length > 0"\n                            variant="warning">\n                            <div class="sw-theme-manager-detail__sales-channel-warning" v-if="removedSalesChannels.length === 1">\n                                <span v-html="$tc(\'sw-theme-manager.modal.salesChannelRemovedModal.descriptionSingle\', 0, { defaultThemeName: defaultTheme.name })"></span>\n                            </div>\n\n                            <div class="sw-theme-manager-detail__sales-channel-warning" v-else>\n                                <span v-html="$tc(\'sw-theme-manager.modal.salesChannelRemovedModal.descriptionMultiple\', 0, { defaultThemeName: defaultTheme.name })"></span>\n                            </div>\n\n                            <div v-for="salesChannel in removedSalesChannels">\n                                <b>{{ theme.name }}</b> ({{ salesChannel.name }})\n                            </div>\n                        </sw-alert>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_save_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_theme_manager_detail_save_modal_cancel %}\n                                <sw-button @click="onCloseSaveModal"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_theme_manager_detail_save_modal_confirm %}\n                                <sw-button @click="onConfirmThemeSave"\n                                           variant="primary"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonSave\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n\n            {% block sw_theme_manager_detail_duplicate_modal %}\n                <sw-modal v-if="showDuplicateModal"\n                          class="sw_theme_manager__duplicate-modal"\n                          @modal-close="onCloseDuplicateModal"\n                          :title="$tc(\'sw-theme-manager.modal.modalTitleDuplicate\')"\n                          variant="small">\n\n                    {% block sw_theme_manager_detail_duplicate_modal_name_input %}\n                        <div class="sw_theme_manager__duplicate-info">\n                            {{ $tc(\'sw-theme-manager.modal.textDuplicateInfo\') }}\n                        </div>\n\n                        <sw-field v-model="newThemeName"\n                                  :label="$tc(\'sw-theme-manager.modal.labelDuplicateThemeName\')"\n                                  :placeholder="$tc(\'sw-theme-manager.modal.placeholderDuplicateThemeName\')"\n                                  tpye="text">\n                        </sw-field>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_duplicate_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_theme_manager_detail_duplicate_modal_cancel %}\n                                <sw-button @click="onCloseDuplicateModal"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_theme_manager_detail_duplicate_modal_confirm %}\n                                <sw-button @click="onConfirmThemeDuplicate"\n                                           variant="primary"\n                                           :disabled="newThemeName.length < 3"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonDuplicateTheme\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n\n            {% block sw_theme_manager_detail_rename_modal %}\n                <sw-modal v-if="showRenameModal"\n                          class="sw_theme_manager__rename-modal"\n                          @modal-close="onCloseRenameModal"\n                          :title="$tc(\'sw-theme-manager.modal.modalTitleRename\')"\n                          variant="small">\n\n                    {% block sw_theme_manager_detail_rename_modal_name_input %}\n                        <div class="sw_theme_manager__rename-info">\n                            {{ $tc(\'sw-theme-manager.modal.textRenameInfo\') }}\n                        </div>\n\n                        <sw-field v-model="newThemeName"\n                                  :label="$tc(\'sw-theme-manager.modal.labelRenameThemeName\')"\n                                  :placeholder="$tc(\'sw-theme-manager.modal.placeholderRenameThemeName\')"\n                                  tpye="text">\n                        </sw-field>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_rename_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_theme_manager_detail_rename_modal_cancel %}\n                                <sw-button @click="onCloseRenameModal"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_theme_manager_detail_rename_modal_confirm %}\n                                <sw-button @click="onConfirmThemeRename"\n                                           variant="primary"\n                                           :disabled="newThemeName.length < 3"\n                                           size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonRenameTheme\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n\n            {% block sw_theme_manager_detail_error_modal %}\n                <sw-modal v-if="errorModalMessage"\n                          @modal-close="onCloseErrorModal"\n                          :title="$tc(\'sw-theme-manager.modal.errorModalTitle\')"\n                          variant="large">\n\n                    {% block sw_theme_manager_detail_error_modal_message %}\n                        <pre style="white-space: pre-line;">{{ errorModalMessage }}</pre>\n                    {% endblock %}\n\n                    {% block sw_theme_manager_detail_error_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_theme_manager_detail_error_modal_close %}\n                                <sw-button @click="onCloseErrorModal" size="small">\n                                    {{ $tc(\'sw-theme-manager.modal.buttonClose\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n        </template>\n\n        {% block sw_theme_manager_detail_sidebar %}\n            <sw-sidebar slot="sidebar" :propagateWidth="true">\n                {% block sw_theme_manager_detail_sidebar_media %}\n                    <sw-sidebar-media-item\n                        ref="mediaSidebarItem"\n                        :initialFolderId="defaultMediaFolderId">\n                        <template #context-menu-items="media">\n                            {% block sw_theme_manager_detail_sidebar_media_items %}\n                                <template v-if="shouldShowContent" v-for="(tab, tabName) in structuredThemeFields.tabs">\n                                    <template v-for="block in tab.blocks">\n                                        <template v-for="section in block.sections">\n                                            <sw-context-menu-item\n                                                v-for="(field, fieldName) in section.fields"\n                                                v-if="themeConfig[fieldName] && field.type === \'media\'"\n                                                :key="fieldName"\n                                                @click="onAddMediaToTheme(media.mediaItem, currentThemeConfig[fieldName])">\n                                                {{ $tc(\'sw-theme-manager.modal.addToMediaLabel\', 0, { name: field.label }) }}\n                                            </sw-context-menu-item>\n                                        </template>\n                                    </template>\n                                </template>\n                            {% endblock %}\n                        </template>\n                    </sw-sidebar-media-item>\n                {% endblock %}\n            </sw-sidebar>\n        {% endblock %}\n    </sw-page>\n{% endblock %}\n',inject:["acl","feature"],mixins:[l.getByName("theme"),l.getByName("notification")],data:function(){return{theme:null,parentTheme:!1,defaultMediaFolderId:null,structuredThemeFields:{},themeConfig:{},currentThemeConfig:{},showResetModal:!1,showSaveModal:!1,errorModalMessage:null,baseThemeConfig:{},currentThemeConfigInitial:{},inheritanceChanged:[],isLoading:!1,isSaveSuccessful:!1,mappedFields:{color:"colorpicker",fontFamily:"text"},defaultTheme:null,themeCompatibleSalesChannels:[],salesChannelsWithTheme:null,newAssignedSalesChannels:[],overwrittenSalesChannelAssignments:[],removedSalesChannels:[]}},metaInfo:function(){return{title:this.$createTitle(this.themeName)}},computed:{themeName:function(){return this.theme?this.theme.name:""},isDerived:function(){var e,t;return!!this.theme&&("Storefront"!==this.theme.technicalName&&(!!this.parentTheme||!(d(null===(e=this.theme)||void 0===e||null===(t=e.baseConfig)||void 0===t?void 0:t.configInheritance)&&!this.theme.baseConfig.configInheritance.includes("@Storefront"))))},mediaRepository:function(){return this.repositoryFactory.create("media")},defaultFolderRepository:function(){return this.repositoryFactory.create("media_default_folder")},salesChannelRepository:function(){return this.repositoryFactory.create("sales_channel")},previewMedia:function(){return this.theme&&this.theme.previewMedia&&this.theme.previewMedia.id&&this.theme.previewMedia.url?{"background-image":"url('".concat(this.theme.previewMedia.url,"')"),"background-size":"cover"}:{"background-image":this.defaultThemeAsset}},defaultThemeAsset:function(){return"url('".concat(Shopware.Context.api.assetsPath,"/administration/static/img/theme/default_theme_preview.jpg')")},deleteDisabledToolTip:function(){return{showDelay:300,message:this.$tc("sw-theme-manager.actions.deleteDisabledToolTip"),disabled:0===this.theme.salesChannels.length}},themeId:function(){return this.$route.params.id},shouldShowContent:function(){return Object.values(this.structuredThemeFields).length>0&&!this.isLoading},hasMoreThanOneTab:function(){return Object.values(this.structuredThemeFields.tabs).length>1},isDefaultTheme:function(){return this.theme.id===this.defaultTheme.id}},created:function(){this.createdComponent()},watch:{themeId:function(){this.getTheme()}},methods:{createdComponent:function(){this.getTheme(),this.setPageContext()},getTheme:function(){var e=this;if(this.themeId){this.isLoading=!0;var t=new r;t.addAssociation("previewMedia"),t.addAssociation("salesChannels"),this.themeRepository.get(this.themeId,Shopware.Context.api,t).then((function(t){e.theme=t,e.getThemeConfig(),e.theme.parentThemeId&&e.getParentTheme(),e.isLoading=!1}))}},checkInheritance:function(e){return!e},checkInheritanceFunction:function(e){var t=this;return function(n){return t.currentThemeConfig[e].isInherited}},handleInheritanceInput:function(e,t){this.currentThemeConfig[t].isInherited=null===e},getThemeConfig:function(){var e=this;this.isLoading=!0,this.theme&&this.themeId&&(this.structuredThemeFields={},this.currentThemeConfig={},this.themeConfig={},this.baseThemeConfig={},this.currentThemeConfigInitial={},this.themeService.getStructuredFields(this.themeId).then((function(t){e.structuredThemeFields=t})),this.themeService.getConfiguration(this.themeId).then((function(t){e.currentThemeConfig=t.currentFields,e.currentThemeConfigInitial=h(e.currentThemeConfig),e.themeConfig=t.fields,e.baseThemeConfig=h(t.baseThemeFields),e.isLoading=!1})))},setPageContext:function(){var e=this;this.getDefaultTheme().then((function(t){e.defaultTheme=t})),this.getDefaultFolderId().then((function(t){e.defaultMediaFolderId=t})),this.getThemeCompatibleSalesChannels().then((function(t){e.themeCompatibleSalesChannels=t})),this.getSalesChannelsWithTheme().then((function(t){e.salesChannelsWithTheme=t}))},getParentTheme:function(){var e=this;this.themeRepository.get(this.theme.parentThemeId,Shopware.Context.api).then((function(t){e.parentTheme=t}))},openMediaSidebar:function(){this.$refs.mediaSidebarItem.openContent()},onAddMediaToTheme:function(e,t){this.setMediaItem(e,t)},onDropMedia:function(e,t){this.setMediaItem(e,t)},setMediaItem:function(e,t){t.value=e.id},successfulUpload:function(e,t){var n=this;this.mediaRepository.get(e.targetId,Shopware.Context.api).then((function(e){return n.setMediaItem(e,t),!0}))},removeMediaItem:function(e,t,n,a){this.currentThemeConfig[e].value=null,this.themeConfig[e].value=null,n?t(null):a(null),this.currentThemeConfigInitial[e].value=!1},restoreMediaInheritance:function(e,t){return e},onReset:function(){this.acl.can("theme.editor")&&null!==this.theme.configValues&&(this.showResetModal=!0)},onCloseResetModal:function(){this.showResetModal=!1},onCloseErrorModal:function(){this.errorModalMessage=null},onConfirmThemeReset:function(){var e=this;this.acl.can("theme.editor")&&(this.themeService.resetTheme(this.themeId).then((function(){e.getTheme()})),this.showResetModal=!1)},onSave:function(){if(this.findChangedSalesChannels(),!(this.theme.salesChannels.length>0||this.removedSalesChannels.length>0))return this.onSaveTheme();this.showSaveModal=!0},onSaveClean:function(){if(this.findChangedSalesChannels(),!(this.theme.salesChannels.length>0||this.removedSalesChannels.length>0))return this.onSaveTheme(!0);this.showSaveModal=!0},onCloseSaveModal:function(){this.showSaveModal=!1},onConfirmThemeSave:function(){this.onSaveTheme(),this.showSaveModal=!1},onSaveTheme:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.acl.can("theme.editor"))return this.isSaveSuccessful=!1,this.isLoading=!0,Promise.all([this.saveSalesChannels(),this.saveThemeConfig(t)]).then((function(){e.getTheme()})).catch((function(t){e.isLoading=!1;var n=t.response.data.errors[0];"THEME__COMPILING_ERROR"!==n.code?e.createNotificationError({title:e.$tc("global.default.error"),message:t.toString(),autoClose:!0}):e.createNotificationError({title:e.$tc("sw-theme-manager.detail.error.themeCompile.title"),message:e.$tc("sw-theme-manager.detail.error.themeCompile.message"),autoClose:!1,actions:[{label:e.$tc("sw-theme-manager.detail.showFullError"),method:function(){this.errorModalMessage=n.detail}.bind(e)}]})}))},saveSalesChannels:function(){var e=this,t=[];return this.newAssignedSalesChannels.length>0&&this.newAssignedSalesChannels.forEach((function(n){t.push(e.themeService.assignTheme(e.themeId,n))})),this.removedSalesChannels.length>0&&this.removedSalesChannels.forEach((function(n){t.push(e.themeService.assignTheme(e.defaultTheme.id,n.id))})),Promise.all(t)},findChangedSalesChannels:function(){this.newAssignedSalesChannels=[],this.removedSalesChannels=[],this.overwrittenSalesChannelAssignments=[];var e=this.themeRepository.getSyncChangeset([this.theme]);e.changeset.length>0&&e.changeset[0].changes.hasOwnProperty("salesChannels")&&this.findAddedSalesChannels(e.changeset[0].changes.salesChannels),e.deletions.length>0&&this.findRemovedSalesChannels(e.deletions)},findAddedSalesChannels:function(e){var t=this;e.forEach((function(e){t.newAssignedSalesChannels.push(e.id);var n=t.salesChannelsWithTheme.get(e.id);null!==n&&t.overwrittenSalesChannelAssignments.push({id:e.id,salesChannelName:t.theme.salesChannels.get(e.id).translated.name,oldThemeName:n.extensions.themes[0].name})}))},findRemovedSalesChannels:function(e){var t=this;e.forEach((function(e){t.removedSalesChannels.push({id:e.key,name:t.theme.getOrigin().salesChannels.get(e.key).translated.name})}))},getCurrentChangeset:function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=m(this.currentThemeConfigInitial,this.currentThemeConfig),i=null!==(e=this.theme.configValues)&&void 0!==e?e:{};if(Object.assign(i,n),!t)return i;for(var s={},o=0,l=Object.entries(i);o<l.length;o++){var r=a(l[o],2),c=r[0],h=r[1];void 0!==this.themeConfig[c]&&void 0!==this.themeConfig[c].type&&null!==this.themeConfig[c].type&&(s[c]=h)}return s},removeInheritedFromChangeset:function(e){for(var t=0,n=Object.entries(e);t<n.length;t++){var i=a(n[t],2),s=i[0];i[1];this.wrapperIsVisible(s)&&this.$refs["wrapper-".concat(s)][0].isInherited?delete e["".concat(s)]:this.wrapperIsVisible(s)||void 0===this.inheritanceChanged["wrapper-".concat(s)]||!0!==this.inheritanceChanged["wrapper-".concat(s)]||delete e["".concat(s)]}},wrapperIsVisible:function(e){return void 0!==this.$refs["wrapper-".concat(e)]&&d(this.$refs["wrapper-".concat(e)])&&void 0!==this.$refs["wrapper-".concat(e)][0]},saveThemeConfig:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=this.getCurrentChangeset(t);return this.removeInheritedFromChangeset(n),this.themeService.resetTheme(this.themeId).then((function(){return e.themeService.updateTheme(e.themeId,{config:n})}))},saveFinish:function(){this.isSaveSuccessful=!1},onSearch:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;!e.length||e.length<=0?this.term=null:this.term=e},onChangeTab:function(){for(var e=0,t=Object.entries(this.$refs);e<t.length;e++){var n=a(t[e],2),i=n[0],s=n[1];i.startsWith("wrapper-")&&void 0!==s&&d(s)&&void 0!==s[0]&&(this.inheritanceChanged[i]=s[0].isInherited)}},mapSwFieldTypes:function(e){return this.mappedFields[e]?this.mappedFields[e]:null},getThemeCompatibleSalesChannels:function(){var e=new r;return e.addAssociation("type"),e.addFilter(r.equalsAny("type.name",["Storefront","Headless"])),this.salesChannelRepository.search(e,Shopware.Context.api).then((function(e){return e.getIds()}))},getSalesChannelsWithTheme:function(){var e=new r;return e.addAssociation("themes"),e.addFilter(r.not("or",[r.equals("themes.id",null)])),this.salesChannelRepository.search(e,Shopware.Context.api).then((function(e){return e}))},getDefaultFolderId:function(){var e=new r(1,1);return e.addAssociation("folder"),e.addFilter(r.equals("entity",this.themeRepository.schema.entity)),this.defaultFolderRepository.search(e,Shopware.Context.api).then((function(e){var t=e.first();return t.folder.id?t.folder.id:null}))},getDefaultTheme:function(){var e=new r;return e.addFilter(r.equals("technicalName","Storefront")),this.themeRepository.search(e,Shopware.Context.api).then((function(e){return e.first()}))},getBind:function(e){var t,n,a,i,s=Object.assign({},e);return"switch"!==(null==s?void 0:s.type)&&"checkbox"!==(null==s?void 0:s.type)&&"sw-switch-field"!==(null===(t=s.custom)||void 0===t?void 0:t.componentName)&&"sw-checkbox-field"!==(null===(n=s.custom)||void 0===n?void 0:n.componentName)&&(s.label=""),delete s.type,Object.assign(s,s.custom),"sw-switch-field"!==(null===(a=s.custom)||void 0===a?void 0:a.componentName)&&"sw-checkbox-field"!==(null===(i=s.custom)||void 0===i?void 0:i.componentName)&&delete s.custom,{type:e.type,config:s}},selectionDisablingMethod:function(e){return!!this.isDefaultTheme&&this.theme.getOrigin().salesChannels.has(e.id)},isThemeCompatible:function(e){return this.themeCompatibleSalesChannels.includes(e.id)}}});n("/Ssc");function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,i,s,o,l=[],r=!0,c=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;r=!1}else for(;!(r=(a=s.call(n)).done)&&(l.push(a.value),l.length!==t);r=!0);}catch(e){c=!0,i=e}finally{try{if(!r&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var _=Shopware,p=_.Component,g=_.Mixin,w=Shopware.Data.Criteria;p.register("sw-theme-manager-list",{template:'{% block sw_theme_manager_list %}\n    <sw-page class="sw-theme-list">\n\n        {% block sw_theme_manager_list_search_bar %}\n            <template slot="search-bar">\n                <sw-search-bar :placeholder="$tc(\'sw-theme-manager.general.placeholderSearchBar\')"\n                               :initialSearchType="$tc(\'sw-theme-manager.general.mainMenuItemGeneral\')"\n                               @search="onSearch">\n                </sw-search-bar>\n            </template>\n        {% endblock %}\n\n        {% block sw_theme_manager_list_toolbar %}\n            <template slot="smart-bar-header">\n                <h2> {{ $tc(\'sw-theme-manager.general.mainMenuItemGeneral\') }}</h2>\n            </template>\n        {% endblock %}\n\n        {% block sw_theme_list_card_view %}\n            <sw-card-view slot="content">\n\n                {% block sw_themes_list_listing %}\n                    <div class="sw-theme-list__content">\n\n                        {% block sw_theme_list_listing_actions %}\n                            <div class="sw-theme-list__actions">\n\n                                {% block sw_theme_list_listing_title %}\n                                    <h3>{{ $tc(\'sw-theme-manager.general.mainMenuHeader\') }}</h3>\n                                {% endblock %}\n\n                                {% block sw_theme_list_listing_actions_sorting %}\n                                    <div class="sw-theme-list__actions-sorting">\n                                        <sw-field type="select"\n                                                  name="sortType"\n                                                  :label="$tc(\'sw-theme-manager.sorting.labelSort\')"\n                                                  :value="sortingConCat"\n                                                  @change="onSortingChanged"\n                                                  aside>\n\n                                            {% block sw_theme_list_listing_actions_sorting_options %}\n                                            <option v-for="sortOption in sortOptions"\n                                                    :value="sortOption.value">\n                                                {{ sortOption.name }}\n                                            </option>\n                                            {% endblock %}\n                                        </sw-field>\n                                    </div>\n                                {% endblock %}\n\n                                {% block sw_theme_list_listing_actions_mode %}\n                                    <div class="sw-theme-list__actions-mode" @click="onListModeChange">\n                                        <sw-icon name="default-view-normal" size="16" v-if="listMode === \'grid\'"></sw-icon>\n                                        <sw-icon name="default-view-grid" size="16" v-if="listMode === \'list\'"></sw-icon>\n                                    </div>\n                                {% endblock %}\n                            </div>\n                        {% endblock %}\n\n                        {% block sw_theme_list_listing_list %}\n                            <div class="sw-theme-list__list">\n\n                                {% block sw_theme_list_listing_list_card %}\n                                    <sw-card class="sw-theme-list__list-card" v-if="listMode === \'list\'">\n                                        <template slot="grid">\n\n                                            {% block sw_theme_list_listing_list_data_grid %}\n                                                <sw-data-grid\n                                                    class="sw-theme-list__list-data-grid"\n                                                    identifier="sw-theme-list"\n                                                    :isLoading="isLoading"\n                                                    :dataSource="themes"\n                                                    :columns="columnConfig"\n                                                    :skeletonItemAmount="limit"\n                                                    :sortBy="sortBy"\n                                                    :sortDirection="sortDirection"\n                                                    :allowInlineEdit="false"\n                                                    :allowColumnEdit="false"\n                                                    :showSettings="false"\n                                                    :showSelection="false"\n                                                    @column-sort="onSortColumn">\n\n                                                    {% block sw_theme_list_listing_list_data_grid_column_name %}\n                                                        <template #column-name="{ item }">\n                                                            <sw-icon\n                                                                name="default-lock-closed"\n                                                                class="sw-theme-list__icon-lock"\n                                                                v-if="!item.parentThemeId"\n                                                                v-tooltip="lockToolTip"\n                                                                size="14">\n                                                            </sw-icon>\n                                                            <router-link :to="{ name: \'sw.theme.manager.detail\', params: { id: item.id } }">\n                                                                {{ item.name }}\n                                                            </router-link>\n                                                        </template>\n                                                    {% endblock %}\n\n                                                    {% block sw_theme_list_listing_list_data_grid_column_assignment %}\n                                                        <template #column-assignment="{ item }">\n                                                            {{ item.salesChannels.length }}\n                                                        </template>\n                                                    {% endblock %}\n\n                                                    {% block sw_theme_list_listing_list_data_grid_column_created %}\n                                                        <template #column-createdAt="{ item }">\n                                                            {{ item.createdAt | date({ hour: \'2-digit\', minute: \'2-digit\' }) }}\n                                                        </template>\n                                                    {% endblock %}\n\n                                                    {% block sw_theme_list_listing_list_data_grid_actions %}\n                                                        <template #actions="{ item }">\n                                                            {% block sw_theme_list_listing_list_data_grid_actions_edit %}\n                                                                <sw-context-menu-item\n                                                                    class="sw-theme-list-item__option-edit"\n                                                                    :routerLink="{ name: \'sw.theme.manager.detail\', params: { id: item.id } }">\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.edit\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_data_grid_actions_rename %}\n                                                                <sw-context-menu-item\n                                                                    @click="onRenameTheme(item)"\n                                                                    class="sw-theme-list-item__option-rename"\n                                                                    :disabled="!acl.can(\'theme.editor\')">\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.rename\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_data_grid_actions_delete %}\n                                                                <sw-context-menu-item\n                                                                    variant="danger"\n                                                                    class="sw-theme-list-item__option-delete"\n                                                                    :disabled="item.salesChannels.length > 0 || !acl.can(\'theme.deleter\')"\n                                                                    v-tooltip="deleteDisabledToolTip(item)"\n                                                                    @click="onDeleteTheme(item)"\n                                                                    v-if="item.parentThemeId">\n\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.delete\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_data_grid_actions_create %}\n                                                                <sw-context-menu-item\n                                                                    v-if="!item.parentThemeId"\n                                                                    class="sw-theme-list-item__option-duplicate"\n                                                                    @click="onDuplicateTheme(item)"\n                                                                    :disabled="!acl.can(\'theme.creator\')">\n                                                                    {{ $tc(\'sw-theme-manager.actions.duplicate\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n                                                        </template>\n                                                    {% endblock %}\n\n                                                    {% block sw_theme_list_listing_list_data_grid_pagination %}\n                                                        <template #pagination>\n                                                            <sw-pagination\n                                                                :page="page"\n                                                                :limit="limit"\n                                                                :total="total"\n                                                                :steps="[5, 10, 25, 50]"\n                                                                @page-change="onPageChange">\n                                                            </sw-pagination>\n                                                        </template>\n                                                    {% endblock %}\n                                                </sw-data-grid>\n                                            {% endblock %}\n                                        </template>\n                                    </sw-card>\n                                {% endblock %}\n\n                                {% block sw_theme_list_listing_list_grid %}\n                                    <div class="sw-theme-list__list-grid" v-if="listMode === \'grid\'">\n\n                                        {% block sw_theme_list_listing_list_grid_content %}\n                                            <div class="sw-theme-list__list-grid-content">\n                                                {% block sw_theme_list_listing_list_item %}\n                                                    <template v-if="!isLoading">\n                                                        <sw-theme-list-item\n                                                        v-for="theme in themes"\n                                                        :theme="theme"\n                                                        :key="theme.id"\n                                                        @preview-image-change="onPreviewChange"\n                                                        @item-click="onListItemClick">\n\n                                                        <sw-context-button slot="contextMenu"\n                                                                           :zIndex="1100"\n                                                                           class="sw-theme-list-item__options">\n\n                                                            {% block sw_theme_list_listing_list_item_option_add_preview %}\n                                                                <sw-context-menu-item\n                                                                    class="sw-theme-list-item__option-preview"\n                                                                    @click="onPreviewChange(theme)"\n                                                                    :disabled="!acl.can(\'theme.editor\')">\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.addPreviewImage\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_item_option_remove_preview %}\n                                                                <sw-context-menu-item\n                                                                    class="sw-theme-list-item__option-preview sw-theme-list-item__option-preview-remove"\n                                                                    v-if="theme.previewMediaId"\n                                                                    variant="danger"\n                                                                    @click="onPreviewImageRemove(theme)"\n                                                                    :disabled="!acl.can(\'theme.editor\')">\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.removePreviewImage\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_item_option_rename %}\n                                                                <sw-context-menu-item\n                                                                    @click="onRenameTheme(theme)"\n                                                                    class="sw-theme-list-item__option-rename"\n                                                                    :disabled="!acl.can(\'theme.editor\')">\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.rename\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_item_option_create %}\n                                                                <sw-context-menu-item\n                                                                    v-if="!theme.parentThemeId"\n                                                                    class="sw-theme-list-item__option-duplicate"\n                                                                    @click="onDuplicateTheme(theme)"\n                                                                    :disabled="!acl.can(\'theme.creator\')">\n                                                                    {{ $tc(\'sw-theme-manager.actions.duplicate\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n\n                                                            {% block sw_theme_list_listing_list_item_option_delete %}\n                                                                <sw-context-menu-item\n                                                                    v-if="theme.parentThemeId"\n                                                                    class="sw-theme-list-item__option-delete"\n                                                                    variant="danger"\n                                                                    :disabled="theme.salesChannels.length > 0 || !acl.can(\'theme.deleter\')"\n                                                                    v-tooltip="deleteDisabledToolTip(theme)"\n                                                                    @click="onDeleteTheme(theme)">\n                                                                    {{ $tc(\'sw-theme-manager.themeListItem.delete\') }}\n                                                                </sw-context-menu-item>\n                                                            {% endblock %}\n                                                        </sw-context-button>\n                                                    </sw-theme-list-item>\n                                                    </template>\n\n                                                    <template v-else>\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                        <sw-skeleton variant="gallery" />\n                                                    </template>\n                                                {% endblock %}\n                                            </div>\n                                        {% endblock %}\n\n                                        {% block sw_theme_list_listing_pagination %}\n                                        <sw-pagination class="sw-theme-list__list-pagination"\n                                                       v-if="!isLoading"\n                                                       :page="page"\n                                                       :limit="limit"\n                                                       :total="total"\n                                                       :steps="[9]"\n                                                       @page-change="onPageChange">\n                                        </sw-pagination>\n                                        {% endblock %}\n                                    </div>\n                                {% endblock %}\n                            </div>\n                        {% endblock %}\n                    </div>\n                {% endblock %}\n\n                {# @deprecated tag:v6.5.0 - Will be removed #}\n                {% block sw_theme_list_listing_list_loader %}{% endblock %}\n\n                {% block sw_theme_list_media_modal %}\n                    <sw-media-modal-v2\n                        variant="regular"\n                        v-if="showMediaModal"\n                        :caption="$tc(\'sw-theme-manager.general.captionMediaUpload\')"\n                        entityContext="theme"\n                        :allowMultiSelect="false"\n                        @media-modal-selection-change="onPreviewImageChange"\n                        @modal-close="onModalClose">\n                    </sw-media-modal-v2>\n                {% endblock %}\n\n                {% block sw_theme_list_delete_modal %}\n                    <sw-modal v-if="showDeleteModal"\n                              @modal-close="onCloseDeleteModal"\n                              :title="$tc(\'global.default.warning\')"\n                              variant="small">\n\n                        {% block sw_theme_list_delete_modal_info %}\n                            <div class="sw_theme_manager__confirm-delete-text">\n                                {{ $tc(\'sw-theme-manager.modal.textDeleteInfo\', 0,\n                                { themeName: modalTheme.name }) }}\n                            </div>\n                        {% endblock %}\n\n                        {% block sw_theme_list_delete_modal_footer %}\n                            <template slot="modal-footer">\n                                {% block sw_theme_list_delete_modal_cancel %}\n                                    <sw-button @click="onCloseDeleteModal"\n                                               size="small">\n                                        {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                    </sw-button>\n                                {% endblock %}\n\n                                {% block sw_theme_list_delete_modal_confirm %}\n                                    <sw-button @click="onConfirmThemeDelete"\n                                               variant="danger"\n                                               size="small">\n                                        {{ $tc(\'sw-theme-manager.modal.buttonDelete\') }}\n                                    </sw-button>\n                                {% endblock %}\n                            </template>\n                        {% endblock %}\n                    </sw-modal>\n                {% endblock %}\n\n                {% block sw_theme_list_duplicate_modal %}\n                    <sw-modal v-if="showDuplicateModal"\n                              class="sw_theme_manager__duplicate-modal"\n                              @modal-close="onCloseDuplicateModal"\n                              :title="$tc(\'sw-theme-manager.modal.modalTitleDuplicate\')"\n                              variant="small">\n\n                        {% block sw_theme_list_duplicate__modal_name_input %}\n                            <div class="sw_theme_manager__duplicate-info">\n                                {{ $tc(\'sw-theme-manager.modal.textDuplicateInfo\') }}\n                            </div>\n\n                            <sw-field v-model="newThemeName"\n                                      :label="$tc(\'sw-theme-manager.modal.labelDuplicateThemeName\')"\n                                      :placeholder="$tc(\'sw-theme-manager.modal.placeholderDuplicateThemeName\')"\n                                      tpye="text">\n                            </sw-field>\n                        {% endblock %}\n\n                        {% block sw_theme_list_duplicate_modal_footer %}\n                            <template slot="modal-footer">\n                                {% block sw_theme_list_duplicate_modal_cancel %}\n                                    <sw-button @click="onCloseDuplicateModal"\n                                               size="small">\n                                        {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                    </sw-button>\n                                {% endblock %}\n\n                                {% block sw_theme_list_duplicate_modal_confirm %}\n                                    <sw-button @click="onConfirmThemeDuplicate"\n                                               variant="primary"\n                                               :disabled="newThemeName.length < 3"\n                                               size="small">\n                                        {{ $tc(\'sw-theme-manager.modal.buttonDuplicateTheme\') }}\n                                    </sw-button>\n                                {% endblock %}\n                            </template>\n                        {% endblock %}\n                    </sw-modal>\n                {% endblock %}\n\n                {% block sw_theme_list_rename_modal %}\n                    <sw-modal v-if="showRenameModal"\n                              class="sw_theme_manager__rename-modal"\n                              @modal-close="onCloseRenameModal"\n                              :title="$tc(\'sw-theme-manager.modal.modalTitleRename\')"\n                              variant="small">\n\n                        {% block sw_theme_list_rename__modal_name_input %}\n                            <div class="sw_theme_manager__rename-info">\n                                {{ $tc(\'sw-theme-manager.modal.textRenameInfo\') }}\n                            </div>\n\n                            <sw-field v-model="newThemeName"\n                                      :label="$tc(\'sw-theme-manager.modal.labelRenameThemeName\')"\n                                      :placeholder="$tc(\'sw-theme-manager.modal.placeholderRenameThemeName\')"\n                                      tpye="text">\n                            </sw-field>\n                        {% endblock %}\n\n                        {% block sw_theme_list_rename_modal_footer %}\n                            <template slot="modal-footer">\n                                {% block sw_theme_list_rename_modal_cancel %}\n                                    <sw-button @click="onCloseRenameModal"\n                                               size="small">\n                                        {{ $tc(\'sw-theme-manager.modal.buttonCancel\') }}\n                                    </sw-button>\n                                {% endblock %}\n\n                                {% block sw_theme_list_rename_modal_confirm %}\n                                    <sw-button @click="onConfirmThemeRename"\n                                               variant="primary"\n                                               :disabled="newThemeName.length < 3"\n                                               size="small">\n                                        {{ $tc(\'sw-theme-manager.modal.buttonRenameTheme\') }}\n                                    </sw-button>\n                                {% endblock %}\n                            </template>\n                        {% endblock %}\n                    </sw-modal>\n                {% endblock %}\n            </sw-card-view>\n        {% endblock %}\n    </sw-page>\n{% endblock %}\n',inject:["acl"],mixins:[g.getByName("notification"),g.getByName("listing"),g.getByName("theme")],data:function(){return{themes:[],isLoading:!1,total:0,disableRouteParams:!0,salesChannelId:this.$route.params.id,listMode:"grid",sortBy:"createdAt",sortDirection:"DESC",limit:9,term:null}},metaInfo:function(){return{title:this.$createTitle(this.identifier)}},computed:{languageRepository:function(){return this.repositoryFactory.create("language")},columnConfig:function(){return this.getColumnConfig()},sortOptions:function(){return[{value:"createdAt:DESC",name:this.$tc("sw-theme-manager.sorting.labelSortByCreatedDsc")},{value:"createdAt:ASC",name:this.$tc("sw-theme-manager.sorting.labelSortByCreatedAsc")},{value:"updatedAt:DESC",name:this.$tc("sw-theme-manager.sorting.labelSortByUpdatedDsc")},{value:"updatedAt:ASC",name:this.$tc("sw-theme-manager.sorting.labelSortByUpdatedAsc")}]},sortingConCat:function(){return"".concat(this.sortBy,":").concat(this.sortDirection)},lockToolTip:function(){return{showDelay:100,message:this.$tc("sw-theme-manager.general.lockedToolTip")}}},methods:{onRefresh:function(){this.getList()},getList:function(){var e=this;this.isLoading=!0;var t=new w(this.page,this.limit);return t.addAssociation("previewMedia"),t.addAssociation("salesChannels"),t.addSorting(w.sort(this.sortBy,this.sortDirection)),t.addFilter(w.equals("active",!0)),null!==this.term&&t.setTerm(this.term),this.themeRepository.search(t,Shopware.Context.api).then((function(t){return e.total=t.total,e.themes=t,e.isLoading=!1,e.pages})).catch((function(){e.isLoading=!1}))},resetList:function(){this.page=1,this.themes=[],this.updateRoute({page:this.page,limit:this.limit,term:this.term,sortBy:this.sortBy,sortDirection:this.sortDirection}),this.getList()},onChangeLanguage:function(e){Shopware.Context.api.languageId=e,this.resetList()},onListItemClick:function(e){this.$router.push({name:"sw.theme.manager.detail",params:{id:e.id}})},onSortingChanged:function(e){var t=u(e.split(":"),2);this.sortBy=t[0],this.sortDirection=t[1],this.resetList()},onSearch:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.term=e.length>0?e:null,this.resetList()},onPageChange:function(e){var t=e.page,n=e.limit;this.page=t,this.limit=n,this.getList(),this.updateRoute({page:this.page,limit:this.limit})},onListModeChange:function(){this.listMode="grid"===this.listMode?"list":"grid",this.limit="grid"===this.listMode?9:10,this.resetList()},onPreviewChange:function(e){this.acl.can("theme.editor")&&(this.showMediaModal=!0,this.currentTheme=e)},onPreviewImageRemove:function(e){this.acl.can("theme.editor")&&(e.previewMediaId=null,e.previewMedia=null,this.saveTheme(e))},onModalClose:function(){this.showMediaModal=!1,this.currentTheme=null},onPreviewImageChange:function(e){var t=u(e,1)[0];this.currentTheme.previewMediaId=t.id,this.saveTheme(this.currentTheme),this.currentTheme.previewMedia=t},saveTheme:function(e){var t=this;return this.isLoading=!0,this.themeRepository.save(e,Shopware.Context.api).then((function(){t.isLoading=!1})).catch((function(){t.isLoading=!1}))},getColumnConfig:function(){return[{property:"name",label:this.$tc("sw-theme-manager.list.gridHeaderName"),primary:!0},{property:"salesChannels.length",label:this.$tc("sw-theme-manager.list.gridHeaderAssignment"),sortable:!1},{property:"createdAt",label:this.$tc("sw-theme-manager.list.gridHeaderCreated")}]},deleteDisabledToolTip:function(e){return{showDelay:300,message:this.$tc("sw-theme-manager.actions.deleteDisabledToolTip"),disabled:0===e.salesChannels.length}}}});n("cu/B");var v=Shopware,b=v.Component;v.Application;b.register("sw-theme-list-item",{template:'{% block sw_theme_list_item %}\n    <div class="sw-theme-list-item" :class="componentClasses">\n\n        {% block sw_theme_list_item_options %}\n            <slot name="contextMenu"></slot>\n        {% endblock %}\n\n        {% block sw_theme_list_item_image %}\n            <div class="sw-theme-list-item__image"\n                 v-if="theme"\n                 :style="previewMedia"\n                 @click="emitItemClick(theme)">\n            </div>\n\n            <div class="sw-theme-list-item__image is--empty" v-else>\n                {{ $tc(\'sw-theme-manager.themeListItem.emptyText\') }}\n            </div>\n        {% endblock %}\n\n        {% block sw_theme_list_item_info %}\n            <div class="sw-theme-list-item__info">\n                <div v-if="theme" class="sw-theme-list-item__status" :class="componentClasses"></div>\n                <div class="sw-theme-list-item__title" v-if="theme" @click="onThemeClick">{{ theme.name }}</div>\n                <sw-icon v-if="theme && !theme.parentThemeId"\n                         class="sw-theme-list-item__locked"\n                         name="small-lock-closed"\n                         v-tooltip="lockToolTip"\n                         small>\n                </sw-icon>\n            </div>\n        {% endblock %}\n    </div>\n{% endblock %}\n',props:{theme:{type:Object,required:!1,default:null},active:{type:Boolean,required:!1,default:!1},disabled:{type:Boolean,required:!1,default:!1}},computed:{previewMedia:function(){return this.theme.previewMedia&&this.theme.previewMedia.id&&this.theme.previewMedia.url?{"background-image":"url('".concat(this.theme.previewMedia.url,"')"),"background-size":"cover"}:{"background-image":this.defaultThemeAsset}},defaultThemeAsset:function(){return"url('".concat(Shopware.Context.api.assetsPath,"/administration/static/img/theme/default_theme_preview.jpg')")},lockToolTip:function(){return{showDelay:100,message:this.$tc("sw-theme-manager.general.lockedToolTip")}},componentClasses:function(){return{"is--active":this.isActive(),"is--disabled":this.disabled}}},methods:{isActive:function(){return this.theme&&this.theme.salesChannels&&this.theme.salesChannels.length>0||this.active},onChangePreviewImage:function(e){this.disabled||this.$emit("preview-image-change",e)},onThemeClick:function(){this.disabled||this.$emit("item-click",this.theme)},onRemovePreviewImage:function(e){e.previewMediaId=null,e.save(),e.previewMedia=null},onDelete:function(e){this.disabled||this.$emit("theme-delete",e)},emitItemClick:function(e){this.disabled||this.$emit("item-click",e)}}});n("H/iK");var k=Shopware,y=k.Component,C=k.Mixin,T=Shopware.Data.Criteria;y.register("sw-theme-modal",{template:'{% block sw_theme_modal %}\n    <sw-modal class="sw-theme-modal" @modal-close="closeModal" :title="$tc(\'sw-theme-manager.themeModal.modalTitle\')">\n\n        {% block sw_theme_modal_header %}\n            <div class="sw-theme-modal__header">\n\n                {% block sw_theme_modal_header_title %}\n                    <div class="sw-theme-modal__header-title">\n                        {{ $tc(\'sw-theme-manager.themeModal.headline\') }}\n                    </div>\n                {% endblock %}\n\n                {% block sw_theme_modal_header_search %}\n                    <sw-simple-search-field class="sw-theme-modal__header-search"\n                        :placeholder="$tc(\'sw-theme-manager.general.placeholderSearchBar\')"\n                        @search-term-change="onSearch">\n                    </sw-simple-search-field>\n                {% endblock %}\n\n            </div>\n        {% endblock %}\n\n        {% block sw_theme_modal_content %}\n            <div class="sw-theme-modal__content">\n\n                {% block sw_theme_modal_loader %}\n                    <sw-loader v-if="isLoading"></sw-loader>\n                {% endblock %}\n\n                {% block sw_theme_modal_content %}\n                    <sw-container v-else columns="repeat(auto-fill, minmax(250px, 1fr))" gap="24px">\n                        {% block sw_theme_modal_content_listing %}\n                            <div v-for="theme in themes" class="sw-theme-modal__content-item" :class="{ \'is--selected\': theme.id === selected }">\n                                {% block sw_theme_modal_content_listing_item %}\n\n                                    {% block sw_theme_modal_content_listing_item_checkbox %}\n                                        <sw-checkbox-field @change="onSelection(theme.id)" :value="theme.id === selected" type="checkbox"></sw-checkbox-field>\n                                    {% endblock %}\n\n                                    {% block sw_theme_modal_content_listing_item_inner %}\n                                        <sw-theme-list-item\n                                            :theme="theme"\n                                            :key="theme.id"\n                                            @item-click="selectItem(theme.id)">\n                                        </sw-theme-list-item>\n                                    {% endblock %}\n\n                                {% endblock %}\n                            </div>\n                        {% endblock %}\n                    </sw-container>\n                {% endblock %}\n            </div>\n        {% endblock %}\n\n        {% block sw_theme_modal_footer %}\n            <template slot="modal-footer">\n                <sw-button @click="closeModal">\n                    {{ $tc(\'sw-theme-manager.themeModal.actionCancel\') }}\n                </sw-button>\n                <sw-button @click="selectLayout" variant="primary">\n                    {{ $tc(\'sw-theme-manager.themeModal.actionConfirm\') }}\n                </sw-button>\n            </template>\n        {% endblock %}\n\n    </sw-modal>\n{% endblock %}\n',inject:["repositoryFactory"],mixins:[C.getByName("listing")],data:function(){return{selected:null,isLoading:!1,sortBy:"createdAt",sortDirection:"DESC",term:null,total:null,themes:[]}},computed:{themeRepository:function(){return this.repositoryFactory.create("theme")}},methods:{getList:function(){var e=this;this.isLoading=!0;var t=new T(this.page,this.limit);return t.addAssociation("previewMedia"),t.addAssociation("salesChannels"),t.addFilter(T.equals("active",!0)),t.addSorting(T.sort(this.sortBy,this.sortDirection)),null!==this.term&&t.setTerm(this.term),this.themeRepository.search(t,Shopware.Context.api).then((function(t){return e.total=t.total,e.themes=t,e.isLoading=!1,e.themes})).catch((function(){e.isLoading=!1}))},selectLayout:function(){this.$emit("modal-theme-select",this.selected),this.closeModal()},selectItem:function(e){this.selected=e},onSearch:function(e){this.term=e.length>0?e.length:null,this.page=1,this.getList()},onSelection:function(e){this.selected=e},closeModal:function(){this.$emit("modal-close"),this.selected=null,this.term=null}}});n("3jMd");Shopware.Module.register("sw-theme-manager",{type:"core",title:"sw-theme-manager.general.mainMenuItemGeneral",description:"sw-theme-manager.general.descriptionTextModule",version:"1.0.0",targetVersion:"1.0.0",color:"#ff68b4",icon:"regular-content",favicon:"icon-module-content.png",entity:"theme",routes:{index:{component:"sw-theme-manager-list",path:"index",meta:{privilege:"theme.viewer"}},detail:{component:"sw-theme-manager-detail",path:"detail/:id",meta:{parentPath:"sw.theme.manager.index",privilege:"theme.viewer"}}},navigation:[{id:"sw-theme-manager",label:"sw-theme-manager.general.mainMenuItemGeneral",color:"#ff68b4",icon:"default-object-image",path:"sw.theme.manager.index",privilege:"theme.viewer",position:80,parent:"sw-content"}],routeMiddleware:function(e,t){"sw.sales.channel.detail"===t.name&&t.children.push({component:"sw-sales-channel-detail-theme",name:"sw.sales.channel.detail.theme",isChildren:!0,path:"/sw/sales/channel/detail/:id/theme",meta:{privilege:"sales_channel.viewer"}}),e(t)}});n("f2W0");function S(e){return S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},S(e)}function x(){x=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function r(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{r({},"")}catch(e){r=function(e,t,n){return e[t]=n}}function c(e,t,n,i){var s=t&&t.prototype instanceof d?t:d,o=Object.create(s.prototype),l=new M(i||[]);return a(o,"_invoke",{value:k(e,n,l)}),o}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var h={};function d(){}function u(){}function f(){}var _={};r(_,s,(function(){return this}));var p=Object.getPrototypeOf,g=p&&p(p(I([])));g&&g!==t&&n.call(g,s)&&(_=g);var w=f.prototype=d.prototype=Object.create(_);function v(e){["next","throw","return"].forEach((function(t){r(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function i(a,s,o,l){var r=m(e[a],e,s);if("throw"!==r.type){var c=r.arg,h=c.value;return h&&"object"==S(h)&&n.call(h,"__await")?t.resolve(h.__await).then((function(e){i("next",e,o,l)}),(function(e){i("throw",e,o,l)})):t.resolve(h).then((function(e){c.value=e,o(c)}),(function(e){return i("throw",e,o,l)}))}l(r.arg)}var s;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){i(e,n,t,a)}))}return s=s?s.then(a,a):a()}})}function k(e,t,n){var a="suspendedStart";return function(i,s){if("executing"===a)throw new Error("Generator is already running");if("completed"===a){if("throw"===i)throw s;return $()}for(n.method=i,n.arg=s;;){var o=n.delegate;if(o){var l=y(o,n);if(l){if(l===h)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===a)throw a="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a="executing";var r=m(e,t,n);if("normal"===r.type){if(a=n.done?"completed":"suspendedYield",r.arg===h)continue;return{value:r.arg,done:n.done}}"throw"===r.type&&(a="completed",n.method="throw",n.arg=r.arg)}}}function y(e,t){var n=t.method,a=e.iterator[n];if(void 0===a)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,y(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=m(a,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,h;var s=i.arg;return s?s.done?(t[e.resultName]=s.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):s:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function I(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function t(){for(;++a<e.length;)if(n.call(e,a))return t.value=e[a],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:$}}function $(){return{value:void 0,done:!0}}return u.prototype=f,a(w,"constructor",{value:f,configurable:!0}),a(f,"constructor",{value:u,configurable:!0}),u.displayName=r(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===u||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,r(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},v(b.prototype),r(b.prototype,o,(function(){return this})),e.AsyncIterator=b,e.async=function(t,n,a,i,s){void 0===s&&(s=Promise);var o=new b(c(t,n,a,i),s);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},v(w),r(w,l,"Generator"),r(w,s,(function(){return this})),r(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var a in t)n.push(a);return n.reverse(),function e(){for(;n.length;){var a=n.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},e.values=I,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function a(n,a){return o.type="throw",o.arg=e,t.next=n,a&&(t.method="next",t.arg=void 0),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],o=s.completion;if("root"===s.tryLoc)return a("end");if(s.tryLoc<=this.prev){var l=n.call(s,"catchLoc"),r=n.call(s,"finallyLoc");if(l&&r){if(this.prev<s.catchLoc)return a(s.catchLoc,!0);if(this.prev<s.finallyLoc)return a(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return a(s.catchLoc,!0)}else{if(!r)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return a(s.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var o=s?s.completion:{};return o.type=e,o.arg=t,s?(this.method="next",this.next=s.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var a=n.completion;if("throw"===a.type){var i=a.arg;T(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:I(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},e}function M(e,t,n,a,i,s,o){try{var l=e[s](o),r=l.value}catch(e){return void n(e)}l.done?t(r):Promise.resolve(r).then(a,i)}function I(e){return function(){var t=this,n=arguments;return new Promise((function(a,i){var s=e.apply(t,n);function o(e){M(s,a,i,o,l,"next",e)}function l(e){M(s,a,i,o,l,"throw",e)}o(void 0)}))}}Shopware.Component.register("sw-settings-storefront-index",{template:'{% block sw_settings_storefront_index %}\n<sw-page class="sw-settings-storefront">\n\n    {% block sw_settings_storefront_smart_bar_header %}\n    <template slot="smart-bar-header">\n        {% block sw_settings_storefront_smart_bar_header_title %}\n        <h2>\n            {% block sw_settings_storefront_smart_bar_header_title_text %}\n            {{ $tc(\'sw-settings.index.title\') }}\n            <sw-icon\n                name="small-arrow-medium-right"\n                small\n            />\n            {{ $tc(\'sw-settings-storefront.general.textHeadline\') }}\n            {% endblock %}\n        </h2>\n        {% endblock %}\n    </template>\n    {% endblock %}\n\n    {% block sw_settings_storefront_smart_bar_actions %}\n    <template slot="smart-bar-actions">\n        {% block sw_settings_storefront_actions_save %}\n        <sw-button-process\n            :is-loading="isLoading"\n            :process-success="isSaveSuccessful"\n            variant="primary"\n            @process-finish="onSaveFinish"\n            @click="savestorefrontSettings"\n        >\n            {{ $tc(\'global.default.save\') }}\n        </sw-button-process>\n        {% endblock %}\n    </template>\n    {% endblock %}\n\n    {% block sw_settings_storefront_content %}\n    <template slot="content">\n        <sw-card-view>\n            <sw-skeleton v-if="isLoading" />\n\n            {% block sw_settings_storefront %}\n            <sw-card\n                position-identifier="sw-settings-storefront--settings"\n                :title="$tc(\'sw-settings-storefront.configuration.cardTitle\')"\n                class="sw-settings-storefront__input-fields"\n            >\n\n                {% block sw_settings_storefront_smtp_settings %}\n                    <sw-settings-storefront-configuration\n                        :storefront-settings="storefrontSettings"\n                    />\n                {% endblock %}\n\n            </sw-card>\n            {% endblock %}\n        </sw-card-view>\n    </template>\n    {% endblock %}\n</sw-page>\n{% endblock %}\n',inject:["systemConfigApiService"],data:function(){return{isLoading:!0,isSaveSuccessful:!1,storefrontSettings:{"core.storefrontSettings.iconCache":!0}}},metaInfo:function(){return{title:this.$createTitle()}},created:function(){this.createdComponent()},methods:{createdComponent:function(){var e=this;return I(x().mark((function t(){return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.loadPageContent();case 2:case"end":return t.stop()}}),t)})))()},loadPageContent:function(){var e=this;return I(x().mark((function t(){return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.loadstorefrontSettings();case 2:case"end":return t.stop()}}),t)})))()},loadstorefrontSettings:function(){var e=this;return I(x().mark((function t(){return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.systemConfigApiService.getValues("core.storefrontSettings");case 3:e.storefrontSettings=t.sent,0===Object.keys(e.storefrontSettings).length&&(Shopware.Feature.isActive("v6.5.0.0")?e.storefrontSettings={"core.storefrontSettings.iconCache":!0}:e.storefrontSettings={"core.storefrontSettings.iconCache":!1}),e.isLoading=!1;case 6:case"end":return t.stop()}}),t)})))()},savestorefrontSettings:function(){var e=this;return I(x().mark((function t(){return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.isLoading=!0,""===e.storefrontSettings["core.storefrontSettings.iconCache"]&&(e.storefrontSettings["core.storefrontSettings.iconCache"]=!0),t.next=4,e.systemConfigApiService.saveValues(e.storefrontSettings);case 4:e.isLoading=!1;case 5:case"end":return t.stop()}}),t)})))()},onSaveFinish:function(){var e=this;return I(x().mark((function t(){return x().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.loadPageContent();case 2:case"end":return t.stop()}}),t)})))()}}});n("orlE");Shopware.Component.register("sw-settings-storefront-configuration",{template:'{% block sw_settings_storefront %}\n<div class="sw-settings-storefront-configuration">\n    {% block sw_settings_storefront_settings_icon_cache %}\n    <sw-switch-field\n        v-model="storefrontSettings[\'core.storefrontSettings.iconCache\']"\n        :label="$tc(\'sw-settings-storefront.configuration.iconCache\')"\n        v-tooltip.top="{\n                        message: $tc(\'sw-settings-storefront.configuration.iconCacheToolTip\'),\n                        showOnDisabledElements: true\n                    }"\n    />\n    {% endblock %}\n</div>\n{% endblock %}\n',props:{storefrontSettings:{type:Object,required:!0}}}),Shopware.Module.register("sw-settings-storefront",{type:"core",name:"sw-settings-storefront",title:"sw-settings-storefront.general.mainMenuItemGeneral",description:"sw-settings-storefront.general.description",color:"#9AA8B5",icon:"regular-cog",favicon:"icon-module-settings.png",routes:{index:{component:"sw-settings-storefront-index",path:"index",meta:{parentPath:"sw.settings.index.system",privilege:"system.system_config"}}},settingsItem:{group:"system",to:"sw.settings.storefront.index",icon:"regular-storefront",privilege:"system.system_config"}});Shopware.Component.override("sw-sales-channel-detail",{template:"{%  block sw_sales_channel_detail_content_tab_theme %}\n    <sw-tabs-item :disabled=\"isProductComparison || isLoading\"\n                  :route=\"{ name: 'sw.sales.channel.detail.theme', params: { id: $route.params.id } }\"\n                  :title=\"$tc('sw-sales-channel.detail.tabTheme')\">\n        {{ $tc('sw-sales-channel.detail.tabTheme') }}\n    </sw-tabs-item>\n{% endblock %}\n",methods:{getLoadSalesChannelCriteria:function(){var e=this.$super("getLoadSalesChannelCriteria");return e.addAssociation("themes"),e}}});n("/Zkd");var $=Shopware,N=$.Component,D=$.Mixin,L=Shopware.Data.Criteria;function R(e){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},R(e)}function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){A(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function A(e,t,n){return(t=E(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function F(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,E(a.key),a)}}function E(e){var t=function(e,t){if("object"!==R(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!==R(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===R(t)?t:String(t)}function B(e,t){return B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},B(e,t)}function V(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=G(e);if(t){var i=G(this).constructor;n=Reflect.construct(a,arguments,i)}else n=a.apply(this,arguments);return z(this,n)}}function z(e,t){if(t&&("object"===R(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function G(e){return G=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},G(e)}N.register("sw-sales-channel-detail-theme",{template:'{% block sw_sales_channel_detail_theme %}\n    <sw-card :title="$tc(\'sales-channel-theme.title\')" :isLoading="isLoading">\n        <div class="sw-sales-channel-detail-theme">\n\n            {% block sw_sales_channel_detail_theme_preview %}\n                <div class="sw-sales-channel-detail-theme__preview">\n                    {% block sw_sales_channel_detail_theme_preview_item %}\n                        <div @click="openThemeModal">\n                            <sw-theme-list-item\n                                :theme="theme"\n                                :disabled="!acl.can(\'sales_channel.editor\')"\n                                :active="!!theme">\n                            </sw-theme-list-item>\n                        </div>\n                    {% endblock %}\n\n                    {% block sw_sales_channel_detail_theme_modal %}\n                        <sw-theme-modal\n                            v-if="showThemeSelectionModal"\n                            @modal-theme-select="onChangeTheme"\n                            @modal-close="closeThemeModal">\n                        </sw-theme-modal>\n                    {% endblock %}\n                </div>\n            {% endblock %}\n\n            {% block sw_sales_channel_detail_theme_info %}\n                <div class="sw-sales-channel-detail-theme__info">\n\n                    {% block sw_sales_channel_detail_theme_info_content %}\n                        <div class="sw-sales-channel-detail-theme__info-content">\n\n                            {% block sw_sales_channel_detail_theme_info_name %}\n                                <div class="sw-sales-channel-detail-theme__info-name" :class="{ \'is--empty\': !theme }">\n                                    {{ theme ? theme.name : $tc(\'sales-channel-theme.defaultTitle\') }}\n                                </div>\n                            {% endblock %}\n\n                            {% block sw_sales_channel_detail_theme_info_author %}\n                                <div v-if="theme" class="sw-sales-channel-detail-theme__info-author">\n                                    {{ theme.author }}\n                                </div>\n                            {% endblock %}\n\n                            {% block sw_sales_channel_detail_theme_info_description %}\n                                <div v-if="theme && theme.description" class="sw-sales-channel-detail-theme__info-description">\n                                    <p class="sw-sales-channel-detail-theme__info-description-title">\n                                        {{ $tc(\'sw-theme-manager.detail.description\') }}:\n                                    </p>\n                                    <p>{{ theme.description |truncate(140) }}</p>\n                                </div>\n                            {% endblock %}\n                        </div>\n                    {% endblock %}\n\n                    {% block sw_sales_channel_detail_theme_info_actions %}\n                        <div class="sw-sales-channel-detail-theme__info-actions">\n\n                            {% block sw_sales_channel_detail_theme_info_actions_theme %}\n                                <sw-button size="small" @click="openThemeModal" :disabled="!acl.can(\'sales_channel.editor\')">\n                                    {{ theme ? $tc(\'sales-channel-theme.changeTheme\') : $tc(\'sales-channel-theme.changeThemeEmpty\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_sales_channel_detail_theme_info_actions_manager %}\n                                <sw-button size="small" @click="openInThemeManager" :disabled="!acl.can(\'sales_channel.editor\')">\n                                    {{ theme ? $tc(\'sales-channel-theme.editContent\'): $tc(\'sales-channel-theme.createTheme\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </div>\n                    {% endblock %}\n                </div>\n            {% endblock %}\n\n            {% block sw_sales_channel_detail_theme_change_modal %}\n                <sw-modal v-if="showChangeModal"\n                          @modal-close="onCloseChangeModal"\n                          :title="$tc(\'sales-channel-theme.modal.modalTitleChange\')"\n                          variant="small">\n\n                    {% block sw_sales_channel_detail_theme_change_modal_info %}\n                        <p class="sw-sales-channel-detail-theme__confirm-change-text">\n                            {{ $tc(\'sales-channel-theme.modal.textChangeTheme\') }}\n                        </p>\n                    {% endblock %}\n\n                    {% block sw_sales_channel_detail_theme_change_modal_footer %}\n                        <template slot="modal-footer">\n                            {% block sw_sales_channel_detail_theme_change_modal_cancel %}\n                                <sw-button @click="onCloseChangeModal"\n                                           size="small">\n                                    {{ $tc(\'sales-channel-theme.modal.buttonCancel\') }}\n                                </sw-button>\n                            {% endblock %}\n\n                            {% block sw_sales_channel_detail_theme_change_modal_confirm %}\n                                <sw-button @click="onConfirmChange"\n                                           variant="primary"\n                                           size="small">\n                                    {{ $tc(\'sales-channel-theme.modal.buttonChange\') }}\n                                </sw-button>\n                            {% endblock %}\n                        </template>\n                    {% endblock %}\n                </sw-modal>\n            {% endblock %}\n        </div>\n    </sw-card>\n{% endblock %}\n',mixins:[D.getByName("notification"),D.getByName("placeholder")],inject:["repositoryFactory","themeService","acl"],props:{salesChannel:{required:!0}},data:function(){return{theme:null,showThemeSelectionModal:!1,showChangeModal:!1,newThemeId:null,isLoading:!1}},computed:{themeRepository:function(){return this.repositoryFactory.create("theme")}},watch:{"salesChannel.extensions.themes":{deep:!0,handler:function(){!this.salesChannel||!this.salesChannel.extensions||this.salesChannel.extensions.themes.length<1||(this.theme=this.salesChannel.extensions.themes[0],this.getTheme(this.theme.id))}}},created:function(){this.createdComponent()},methods:{createdComponent:function(){!this.salesChannel||!this.salesChannel.extensions||this.salesChannel.extensions.themes.length<1||(this.theme=this.salesChannel.extensions.themes[0],this.getTheme(this.theme.id))},getTheme:function(e){var t=this;if(null!==e){var n=new L;n.addAssociation("previewMedia"),this.themeRepository.get(e,Shopware.Context.api,n).then((function(e){t.theme=e}))}},openThemeModal:function(){this.acl.can("sales_channel.editor")&&(this.showThemeSelectionModal=!0)},closeThemeModal:function(){this.showThemeSelectionModal=!1},openInThemeManager:function(){this.theme?this.$router.push({name:"sw.theme.manager.detail",params:{id:this.theme.id}}):this.$router.push({name:"sw.theme.manager.index"})},onChangeTheme:function(e){this.showThemeSelectionModal=!1,this.newThemeId=e,this.showChangeModal=!0},onCloseChangeModal:function(){this.showChangeModal=!1,this.newThemeId=null},onConfirmChange:function(){this.newThemeId&&this.onThemeSelect(this.newThemeId),this.showChangeModal=!1,this.newThemeId=null},onThemeSelect:function(e){var t=this;this.isLoading=!0,this.getTheme(e),this.themeService.assignTheme(e,this.salesChannel.id).then((function(){t.isLoading=!1})).catch((function(){t.createNotificationError({title:t.$tc("sw-theme-manager.general.titleError"),message:t.$tc("sw-theme-manager.general.messageSaveError")}),t.isLoading=!1}))}}});var U=Shopware.Classes.ApiService,H=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&B(e,t)}(s,e);var t,n,a,i=V(s);function s(e,t){var n,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"theme";return P(this,s),(n=i.call(this,e,t,a)).name="themeService",n}return t=s,n=[{key:"assignTheme",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/".concat(this.getApiBasePath(),"/").concat(e,"/assign/").concat(t);return this.httpClient.post(i,{},{params:j({},n),headers:this.getBasicHeaders(a)}).then((function(e){return U.handleResponse(e)}))}},{key:"updateTheme",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i="/_action/".concat(this.getApiBasePath(),"/").concat(e);return this.httpClient.patch(i,t,{params:j({},n),headers:this.getBasicHeaders(a)}).then((function(e){return U.handleResponse(e)}))}},{key:"resetTheme",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a="/_action/".concat(this.getApiBasePath(),"/").concat(e,"/reset");return this.httpClient.patch(a,{},{params:j({},t),headers:this.getBasicHeaders(n)}).then((function(e){return U.handleResponse(e)}))}},{key:"getConfiguration",value:function(e){var t="/_action/".concat(this.getApiBasePath(),"/").concat(e,"/configuration"),n={"sw-language-id":Shopware.State.get("session").languageId};return this.httpClient.get(t,{headers:this.getBasicHeaders(n)}).then((function(e){return U.handleResponse(e)}))}},{key:"getStructuredFields",value:function(e){var t="/_action/".concat(this.getApiBasePath(),"/").concat(e,"/structured-fields"),n={"sw-language-id":Shopware.State.get("session").languageId};return this.httpClient.get(t,{headers:this.getBasicHeaders(n)}).then((function(e){return U.handleResponse(e)}))}}],n&&F(t.prototype,n),a&&F(t,a),Object.defineProperty(t,"prototype",{writable:!1}),s}(U),q=H,W=Shopware.Application;Shopware.Service().register("themeService",(function(e){var t=W.getContainer("init");return new q(t.httpClient,e.loginService)}))},SZ7m:function(e,t,n){"use strict";function a(e,t){for(var n=[],a={},i=0;i<t.length;i++){var s=t[i],o=s[0],l={id:e+":"+i,css:s[1],media:s[2],sourceMap:s[3]};a[o]?a[o].parts.push(l):n.push(a[o]={id:o,parts:[l]})}return n}n.r(t),n.d(t,"default",(function(){return f}));var i="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var s={},o=i&&(document.head||document.getElementsByTagName("head")[0]),l=null,r=0,c=!1,m=function(){},h=null,d="data-vue-ssr-id",u="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function f(e,t,n,i){c=n,h=i||{};var o=a(e,t);return _(o),function(t){for(var n=[],i=0;i<o.length;i++){var l=o[i];(r=s[l.id]).refs--,n.push(r)}t?_(o=a(e,t)):o=[];for(i=0;i<n.length;i++){var r;if(0===(r=n[i]).refs){for(var c=0;c<r.parts.length;c++)r.parts[c]();delete s[r.id]}}}}function _(e){for(var t=0;t<e.length;t++){var n=e[t],a=s[n.id];if(a){a.refs++;for(var i=0;i<a.parts.length;i++)a.parts[i](n.parts[i]);for(;i<n.parts.length;i++)a.parts.push(g(n.parts[i]));a.parts.length>n.parts.length&&(a.parts.length=n.parts.length)}else{var o=[];for(i=0;i<n.parts.length;i++)o.push(g(n.parts[i]));s[n.id]={id:n.id,refs:1,parts:o}}}}function p(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function g(e){var t,n,a=document.querySelector("style["+d+'~="'+e.id+'"]');if(a){if(c)return m;a.parentNode.removeChild(a)}if(u){var i=r++;a=l||(l=p()),t=b.bind(null,a,i,!1),n=b.bind(null,a,i,!0)}else a=p(),t=k.bind(null,a),n=function(){a.parentNode.removeChild(a)};return t(e),function(a){if(a){if(a.css===e.css&&a.media===e.media&&a.sourceMap===e.sourceMap)return;t(e=a)}else n()}}var w,v=(w=[],function(e,t){return w[e]=t,w.filter(Boolean).join("\n")});function b(e,t,n,a){var i=n?"":a.css;if(e.styleSheet)e.styleSheet.cssText=v(t,i);else{var s=document.createTextNode(i),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(s,o[t]):e.appendChild(s)}}function k(e,t){var n=t.css,a=t.media,i=t.sourceMap;if(a&&e.setAttribute("media",a),h.ssrId&&e.setAttribute(d,t.id),i&&(n+="\n/*# sourceURL="+i.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},Ujji:function(e,t){Shopware.Mixin.register("theme",{inject:["repositoryFactory","themeService","acl"],data:function(){return{showDeleteModal:!1,showMediaModal:!1,showRenameModal:!1,showDuplicateModal:!1,newThemeName:"",modalTheme:null}},computed:{themeRepository:function(){return this.repositoryFactory.create("theme")}},methods:{onDeleteTheme:function(e){this.acl.can("theme.deleter")&&(this.modalTheme=e,this.showDeleteModal=!0)},onCloseDeleteModal:function(){this.showDeleteModal=!1,this.modalTheme=null},onConfirmThemeDelete:function(){this.deleteTheme(this.modalTheme),this.showDeleteModal=!1,this.modalTheme=null},deleteTheme:function(e){var t=this,n=this.$tc("sw-theme-manager.components.themeListItem.notificationDeleteErrorTitle"),a=this.$tc("sw-theme-manager.components.themeListItem.notificationDeleteErrorMessage");this.isLoading=!0,this.themeRepository.delete(e.id,Shopware.Context.api).then((function(){t.getList?t.getList():t.$router.push({name:"sw.theme.manager.index"})})).catch((function(){t.isLoading=!1,t.createNotificationError({title:n,message:a})}))},onDuplicateTheme:function(e){this.acl.can("theme.creator")&&(this.modalTheme=e,this.showDuplicateModal=!0)},onCloseDuplicateModal:function(){this.showDuplicateModal=!1,this.modalTheme=null,this.newThemeName=""},onConfirmThemeDuplicate:function(){this.duplicateTheme(this.modalTheme,this.newThemeName),this.showDuplicateModal=!1,this.modalTheme=null,this.newThemeName=""},duplicateTheme:function(e,t){var n=this,a=this.themeRepository.create(Shopware.Context.api);a.name=t,a.parentThemeId=e.id,a.author=e.author,a.description=e.description,a.labels=e.labels,a.helpTexts=e.helpTexts,a.customFields=e.customFields,a.baseConfig=null,a.configValues=null,a.previewMediaId=e.previewMediaId,a.active=!0,this.themeRepository.save(a,Shopware.Context.api).then((function(){n.$router.push({name:"sw.theme.manager.detail",params:{id:a.id}})}))},onRenameTheme:function(e){this.acl.can("theme.editor")&&(this.modalTheme=e,this.newThemeName=this.modalTheme.name,this.showRenameModal=!0)},onCloseRenameModal:function(){this.showRenameModal=!1,this.modalTheme=null,this.newThemeName=""},onConfirmThemeRename:function(){this.RenameTheme(this.modalTheme,this.newThemeName),this.showRenameModal=!1,this.modalTheme=null,this.newThemeName=""},RenameTheme:function(e,t){t&&(e.name=t),this.themeRepository.save(e,Shopware.Context.api)}}})},"Zb/w":function(e,t,n){},"cu/B":function(e,t,n){var a=n("fa9H");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("623cfa56",a,!0,{})},eP6W:function(e,t,n){var a=n("kByD");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("4ae0e428",a,!0,{})},f2W0:function(e,t,n){var a=n("3zw8");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("33ab6046",a,!0,{})},fa9H:function(e,t,n){},iZ31:function(e,t,n){},jxjI:function(e,t,n){},kByD:function(e,t,n){},orlE:function(e,t,n){var a=n("Zb/w");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("SZ7m").default)("dd7beba2",a,!0,{})},qGhd:function(e,t,n){}});
//# sourceMappingURL=storefront.js.map